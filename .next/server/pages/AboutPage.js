"use strict";(()=>{var e={};e.id=765,e.ids=[765,660],e.modules={4297:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(6689),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let n=(0,r.forwardRef)(({color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:c="",children:u,...d},h)=>(0,r.createElement)("svg",{ref:h,...a,width:l,height:l,stroke:n,strokeWidth:o?24*Number(i)/Number(l):i,className:["lucide",`lucide-${s(e)}`,c].join(" "),...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n}},8814:(e,t,n)=>{n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},1323:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},6369:(e,t,n)=>{n.r(t),n.d(t,{config:()=>x,default:()=>g,getServerSideProps:()=>P,getStaticPaths:()=>f,getStaticProps:()=>m,reportWebVitals:()=>b,routeModule:()=>A,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>v});var r={};n.r(r),n.d(r,{default:()=>p});var a=n(7093),s=n(5244),l=n(1323),i=n(2329),o=n.n(i),c=n(4096),u=n.n(c),d=n(997),h=n(8814);function p(){return(0,d.jsxs)("div",{className:"max-w-2xl mx-auto py-8 space-y-4",children:[(0,d.jsxs)("p",{children:["Hello, m\xecnh l\xe0 Sang. Đến từ Huế, sống tại TP HCM, c\xf3 thời gian ở H\xe0 Nội. ",d.jsx("br",{}),"Hồi xưa đẹp trai lắm giờ đỡ nhiều rồi :) ",d.jsx("br",{}),"Đ\xe2y l\xe0 web c\xe1 nh\xe2n của m\xecnh:"," ",(0,d.jsxs)("a",{href:"https://ngosangns.com",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["https://ngosangns.com",d.jsx(h.Z,{className:"w-3 h-3"})]})]}),d.jsx("p",{children:"Trang web n\xe0y l\xe0 một dự \xe1n nằm trong chuỗi c\xe1c dữ \xe1n hỗ trợ sinh vi\xean của C\xe2u lạc bộ lập tr\xecnh Học viện Kỹ thuật Mật M\xe3 hay c\xf2n được gọi l\xe0 KIT (KMA IT)."}),d.jsx("p",{children:"Một số dự \xe1n kh\xe1c m\xe0 ch\xfang m\xecnh đ\xe3 thực hiện:"}),(0,d.jsxs)("ul",{className:"list-disc ml-8 space-y-2",children:[(0,d.jsxs)("li",{children:[(0,d.jsxs)("a",{href:"https://play.google.com/store/apps/details?id=kma.hatuan314.schedule",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["KIT Schedule",d.jsx(h.Z,{className:"w-3 h-3"})]})," ","- Ứng dụng xem lịch học tr\xean điện thoại d\xe0nh cho sinh vi\xean học viện KMA."]}),(0,d.jsxs)("li",{children:[(0,d.jsxs)("a",{href:"https://github.com/ngosangns/tin-chi",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["KMA T\xedn chỉ",d.jsx(h.Z,{className:"w-3 h-3"})]})," ","- Tool hỗ trợ sinh vi\xean sắp xếp lịch học hợp l\xed cho bản th\xe2n v\xe0o mỗi m\xf9a đăng k\xfd học."]})]})]})}let g=(0,l.l)(r,"default"),m=(0,l.l)(r,"getStaticProps"),f=(0,l.l)(r,"getStaticPaths"),P=(0,l.l)(r,"getServerSideProps"),x=(0,l.l)(r,"config"),b=(0,l.l)(r,"reportWebVitals"),v=(0,l.l)(r,"unstable_getStaticProps"),j=(0,l.l)(r,"unstable_getStaticPaths"),y=(0,l.l)(r,"unstable_getStaticParams"),S=(0,l.l)(r,"unstable_getServerProps"),_=(0,l.l)(r,"unstable_getServerSideProps"),A=new a.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/AboutPage",pathname:"/AboutPage",bundlePath:"",filename:""},components:{App:u(),Document:o()},userland:r})},4096:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let r=n(167)._(n(6689)),a=n(4316);async function s(e){let{Component:t,ctx:n}=e;return{pageProps:await (0,a.loadGetInitialProps)(t,n)}}class l extends r.default.Component{render(){let{Component:e,pageProps:t}=this.props;return r.default.createElement(e,t)}}l.origGetInitialProps=s,l.getInitialProps=s,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5244:(e,t)=>{var n;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},997:e=>{e.exports=require("react/jsx-runtime")},1017:e=>{e.exports=require("path")}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[329],()=>n(6369));module.exports=r})();