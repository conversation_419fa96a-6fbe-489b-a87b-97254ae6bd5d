"use strict";(()=>{var e={};e.id=377,e.ids=[377,660],e.modules={1323:(e,n)=>{Object.defineProperty(n,"l",{enumerable:!0,get:function(){return function e(n,t){return t in n?n[t]:"then"in n&&"function"==typeof n.then?n.then(n=>e(n,t)):"function"==typeof n&&"default"===t?n:void 0}}})},4123:(e,n,t)=>{t.r(n),t.d(n,{config:()=>j,default:()=>m,getServerSideProps:()=>p,getStaticPaths:()=>g,getStaticProps:()=>x,reportWebVitals:()=>P,routeModule:()=>_,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>b});var i={};t.r(i),t.d(i,{default:()=>u});var l=t(7093),s=t(5244),c=t(1323),r=t(2329),h=t.n(r),a=t(4096),d=t.n(a),o=t(997);function u(){return(0,o.jsxs)("div",{className:"max-w-2xl mx-auto py-8 space-y-6",children:[(0,o.jsxs)("div",{children:[o.jsx("p",{className:"font-semibold mb-2",children:"Version 2022.12 - 12/2022:"}),(0,o.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[o.jsx("li",{children:"Th\xeam lại t\xednh năng chọn học kỳ để xem lịch học."}),o.jsx("li",{children:"Bỏ t\xednh năng xem lịch từ file Excel."}),o.jsx("li",{children:"N\xe2ng cấp giao diện sử dụng DaisyUI."}),o.jsx("li",{children:"Th\xeam t\xednh năng hiển thị lịch học tuần hiện tại khi v\xe0o trang web."}),o.jsx("li",{children:"Bảo tr\xec API."}),o.jsx("li",{children:"Tối ưu code xử l\xed."}),o.jsx("li",{children:"Converted from SvelteKit to React with shadcn/ui."})]})]}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"font-semibold mb-2",children:"Version 10 - 12/2022:"}),(0,o.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[o.jsx("li",{children:"Bảo tr\xec API."}),o.jsx("li",{children:"Cập nhật c\xe1c package."})]})]}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"font-semibold mb-2",children:"Version 9 - 01/2021:"}),o.jsx("ul",{className:"list-disc ml-8 space-y-1",children:o.jsx("li",{children:"Bảo tr\xec API."})})]}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"font-semibold mb-2",children:"Version 8 - 06/2020:"}),(0,o.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[o.jsx("li",{children:"Th\xeam t\xednh năng xem lịch từ file Excel (cơ sở miền Nam)."}),o.jsx("li",{children:"Th\xeam lại t\xednh năng xem lịch bằng t\xe0i khoản học viện."}),o.jsx("li",{children:"Bỏ bố cục xem lịch theo kỳ học."}),o.jsx("li",{children:"Bỏ t\xednh năng hiển thị bảng th\xf4ng tin chi tiết c\xe1c m\xf4n học."}),o.jsx("li",{children:"Bỏ t\xednh năng chuyển đổi hiển thị t\xean m\xf4n/m\xe3 m\xf4n."}),o.jsx("li",{children:"N\xe2ng cấp giao diện sử dụng Material Design v\xe0 dark mode."})]})]}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"font-semibold mb-2",children:"Version 6 - 01/2020:"}),(0,o.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[o.jsx("li",{children:"Th\xeam t\xednh năng xem lịch bằng m\xe3 nguồn HTML."}),o.jsx("li",{children:"Bỏ t\xednh năng xem lịch bằng t\xe0i khoản học viện."}),o.jsx("li",{children:"Bỏ t\xednh năng chọn học k\xec để xem lịch."}),o.jsx("li",{children:"Th\xeam 2 bố cục xem lịch theo tuần."})]})]}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"font-semibold mb-2",children:"Version 1 - 10/2019:"}),(0,o.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[o.jsx("li",{children:"T\xednh năng xem lịch bằng t\xe0i khoản học viện."}),o.jsx("li",{children:"Bố cục lịch học hiển thị to\xe0n kỳ học theo bảng truyền thống."}),o.jsx("li",{children:"T\xednh năng hiển thị bảng th\xf4ng tin chi tiết c\xe1c m\xf4n học."}),o.jsx("li",{children:"T\xednh năng chuyển đổi hiển thị t\xean m\xf4n/m\xe3 m\xf4n."}),o.jsx("li",{children:"T\xednh năng chọn học k\xec để xem lịch."}),o.jsx("li",{children:"T\xednh năng xem lịch học dạng r\xfat gọn."}),o.jsx("li",{children:"T\xednh năng tải lịch học."}),o.jsx("li",{children:"T\xednh năng chuyển đổi light/dark mode."})]})]})]})}let m=(0,c.l)(i,"default"),x=(0,c.l)(i,"getStaticProps"),g=(0,c.l)(i,"getStaticPaths"),p=(0,c.l)(i,"getServerSideProps"),j=(0,c.l)(i,"config"),P=(0,c.l)(i,"reportWebVitals"),b=(0,c.l)(i,"unstable_getStaticProps"),f=(0,c.l)(i,"unstable_getStaticPaths"),v=(0,c.l)(i,"unstable_getStaticParams"),S=(0,c.l)(i,"unstable_getServerProps"),y=(0,c.l)(i,"unstable_getServerSideProps"),_=new l.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/ChangelogsPage",pathname:"/ChangelogsPage",bundlePath:"",filename:""},components:{App:d(),Document:h()},userland:i})},4096:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return c}});let i=t(167)._(t(6689)),l=t(4316);async function s(e){let{Component:n,ctx:t}=e;return{pageProps:await (0,l.loadGetInitialProps)(n,t)}}class c extends i.default.Component{render(){let{Component:e,pageProps:n}=this.props;return i.default.createElement(e,n)}}c.origGetInitialProps=s,c.getInitialProps=s,("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},5244:(e,n)=>{var t;Object.defineProperty(n,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},997:e=>{e.exports=require("react/jsx-runtime")},1017:e=>{e.exports=require("path")}};var n=require("../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),i=n.X(0,[329],()=>t(4123));module.exports=i})();