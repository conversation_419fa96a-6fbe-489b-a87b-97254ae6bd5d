"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar isUndefined = (val)=>val === undefined;\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [\n        path\n    ] : stringToPath(path)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nHookFormContext.displayName = \"HookFormContext\";\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: _localProxyFormState.current,\n            exact,\n            callback: (formState)=>{\n                !disabled && updateFormState({\n                    ...control._formState,\n                    ...formState\n                });\n            }\n        }), [\n        name,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name,\n            formState: {\n                values: true\n            },\n            exact,\n            callback: (formState)=>!disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n        }), [\n        name,\n        control,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name\n            },\n            type: EVENTS.CHANGE\n        }), [\n        name\n    ]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name\n            },\n            type: EVENTS.BLUR\n        }), [\n        name,\n        control._formValues\n    ]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: ()=>elm.focus && elm.focus(),\n                select: ()=>elm.select && elm.select(),\n                setCustomValidity: (message)=>elm.setCustomValidity(message),\n                reportValidity: ()=>elm.reportValidity()\n            };\n        }\n    }, [\n        control._fields,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange,\n            onBlur,\n            ref\n        }), [\n        name,\n        disabled,\n        formState.disabled,\n        onChange,\n        onBlur,\n        ref,\n        value1\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...isBoolean(_props.current.disabled) ? {\n                disabled: _props.current.disabled\n            } : {}\n        });\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._setDisabledField({\n            disabled,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2, _internal_visited) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar isRegex = (value1)=>value1 instanceof RegExp;\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isMessage = (value1)=>isString(value1);\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    const _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _setValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef)=>{\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data)=>data === checkboxRef.value);\n                                } else {\n                                    checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues)\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            if (!value1.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value1[fieldKey];\n            const fieldName = name + \".\" + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues)\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues)\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.state.next({\n                name,\n                type: event.type,\n                values: cloneObject(_formValues)\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    } else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.state.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const _subscribe = (props)=>_subjects.state.subscribe({\n            next: (formState)=>{\n                if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                    props.callback({\n                        values: {\n                            ..._formValues\n                        },\n                        ..._formState,\n                        ...formState\n                    });\n                }\n            }\n        }).unsubscribe;\n    const subscribe = (props)=>{\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState\n        });\n    };\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues)\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _runSchema();\n                _formState.errors = errors;\n                fieldValues = cloneObject(values);\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    unset(fieldValues, name);\n                }\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                for (const fieldName of _names.mount){\n                    const value1 = get(values, fieldName, get(_defaultValues, fieldName));\n                    if (!isUndefined(value1)) {\n                        set(values, fieldName, value1);\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                }\n            }\n            _formValues = cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.state.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n    return {\n        ...methods,\n        formControl: methods\n    };\n}\nvar generateId = ()=>{\n    if (typeof crypto !== \"undefined\" && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    useIsomorphicLayoutEffect(()=>control._subjects.array.subscribe({\n            next: ({ values, name: fieldArrayName })=>{\n                if (fieldArrayName === _name.current || !fieldArrayName) {\n                    const fieldValues = get(values, _name.current);\n                    if (Array.isArray(fieldValues)) {\n                        setFields(fieldValues);\n                        ids.current = fieldValues.map(generateId);\n                    }\n                }\n            }\n        }).unsubscribe, [\n        control\n    ]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues)\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._setValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return ()=>{\n            const updateMounted = (name, value1)=>{\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value1;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        } else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(()=>{\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: ()=>updateFormState({\n                    ...control._formState\n                }),\n            reRenderRoot: true\n        });\n        updateFormState((data)=>({\n                ...data,\n                isReady: true\n            }));\n        control._formState.isReady = true;\n        return sub;\n    }, [\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [\n        control,\n        props.mode,\n        props.reValidateMode\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [\n        control,\n        props.errors\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.state.next({\n            values: control._getWatch()\n        });\n    }, [\n        control,\n        props.shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        control,\n        props.values\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaG9vay1mb3JtL2Rpc3QvaW5kZXguZXNtLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNJO0FBRW5DLElBQUlFLGtCQUFrQixDQUFDQyxVQUFZQSxRQUFRQyxJQUFJLEtBQUs7QUFFcEQsSUFBSUMsZUFBZSxDQUFDQyxTQUFVQSxrQkFBaUJDO0FBRS9DLElBQUlDLG9CQUFvQixDQUFDRixTQUFVQSxVQUFTO0FBRTVDLE1BQU1HLGVBQWUsQ0FBQ0gsU0FBVSxPQUFPQSxXQUFVO0FBQ2pELElBQUlJLFdBQVcsQ0FBQ0osU0FBVSxDQUFDRSxrQkFBa0JGLFdBQ3pDLENBQUNLLE1BQU1DLE9BQU8sQ0FBQ04sV0FDZkcsYUFBYUgsV0FDYixDQUFDRCxhQUFhQztBQUVsQixJQUFJTyxnQkFBZ0IsQ0FBQ0MsUUFBVUosU0FBU0ksVUFBVUEsTUFBTUMsTUFBTSxHQUN4RGIsZ0JBQWdCWSxNQUFNQyxNQUFNLElBQ3hCRCxNQUFNQyxNQUFNLENBQUNDLE9BQU8sR0FDcEJGLE1BQU1DLE1BQU0sQ0FBQ1QsS0FBSyxHQUN0QlE7QUFFTixJQUFJRyxvQkFBb0IsQ0FBQ0MsT0FBU0EsS0FBS0MsU0FBUyxDQUFDLEdBQUdELEtBQUtFLE1BQU0sQ0FBQyxtQkFBbUJGO0FBRW5GLElBQUlHLHFCQUFxQixDQUFDQyxPQUFPSixPQUFTSSxNQUFNQyxHQUFHLENBQUNOLGtCQUFrQkM7QUFFdEUsSUFBSU0sZ0JBQWdCLENBQUNDO0lBQ2pCLE1BQU1DLGdCQUFnQkQsV0FBV0UsV0FBVyxJQUFJRixXQUFXRSxXQUFXLENBQUNDLFNBQVM7SUFDaEYsT0FBUWxCLFNBQVNnQixrQkFBa0JBLGNBQWNHLGNBQWMsQ0FBQztBQUNwRTtBQUVBLElBQUlDLFFBQVEsTUFDc0IsSUFDOUIsQ0FBb0I7QUFFeEIsU0FBU0ksWUFBWUMsSUFBSTtJQUNyQixJQUFJQztJQUNKLE1BQU14QixVQUFVRCxNQUFNQyxPQUFPLENBQUN1QjtJQUM5QixNQUFNRSxxQkFBcUIsT0FBT0MsYUFBYSxjQUFjSCxnQkFBZ0JHLFdBQVc7SUFDeEYsSUFBSUgsZ0JBQWdCNUIsTUFBTTtRQUN0QjZCLE9BQU8sSUFBSTdCLEtBQUs0QjtJQUNwQixPQUNLLElBQUlBLGdCQUFnQkksS0FBSztRQUMxQkgsT0FBTyxJQUFJRyxJQUFJSjtJQUNuQixPQUNLLElBQUksQ0FBRUwsQ0FBQUEsU0FBVUssQ0FBQUEsZ0JBQWdCSyxRQUFRSCxrQkFBaUIsQ0FBQyxLQUMxRHpCLENBQUFBLFdBQVdGLFNBQVN5QixLQUFJLEdBQUk7UUFDN0JDLE9BQU94QixVQUFVLEVBQUUsR0FBRyxDQUFDO1FBQ3ZCLElBQUksQ0FBQ0EsV0FBVyxDQUFDWSxjQUFjVyxPQUFPO1lBQ2xDQyxPQUFPRDtRQUNYLE9BQ0s7WUFDRCxJQUFLLE1BQU1NLE9BQU9OLEtBQU07Z0JBQ3BCLElBQUlBLEtBQUtOLGNBQWMsQ0FBQ1ksTUFBTTtvQkFDMUJMLElBQUksQ0FBQ0ssSUFBSSxHQUFHUCxZQUFZQyxJQUFJLENBQUNNLElBQUk7Z0JBQ3JDO1lBQ0o7UUFDSjtJQUNKLE9BQ0s7UUFDRCxPQUFPTjtJQUNYO0lBQ0EsT0FBT0M7QUFDWDtBQUVBLElBQUlNLFFBQVEsQ0FBQ3BDLFNBQVUsUUFBUXFDLElBQUksQ0FBQ3JDO0FBRXBDLElBQUlzQyxjQUFjLENBQUNDLE1BQVFBLFFBQVFDO0FBRW5DLElBQUlDLFVBQVUsQ0FBQ3pDLFNBQVVLLE1BQU1DLE9BQU8sQ0FBQ04sVUFBU0EsT0FBTTBDLE1BQU0sQ0FBQ0MsV0FBVyxFQUFFO0FBRTFFLElBQUlDLGVBQWUsQ0FBQ0MsUUFBVUosUUFBUUksTUFBTUMsT0FBTyxDQUFDLGFBQWEsSUFBSUMsS0FBSyxDQUFDO0FBRTNFLElBQUlDLE1BQU0sQ0FBQ0MsUUFBUUMsTUFBTUM7SUFDckIsSUFBSSxDQUFDRCxRQUFRLENBQUM5QyxTQUFTNkMsU0FBUztRQUM1QixPQUFPRTtJQUNYO0lBQ0EsTUFBTUMsU0FBUyxDQUFDaEIsTUFBTWMsUUFBUTtRQUFDQTtLQUFLLEdBQUdOLGFBQWFNLEtBQUksRUFBR0csTUFBTSxDQUFDLENBQUNELFFBQVFqQixNQUFRakMsa0JBQWtCa0QsVUFBVUEsU0FBU0EsTUFBTSxDQUFDakIsSUFBSSxFQUFFYztJQUNySSxPQUFPWCxZQUFZYyxXQUFXQSxXQUFXSCxTQUNuQ1gsWUFBWVcsTUFBTSxDQUFDQyxLQUFLLElBQ3BCQyxlQUNBRixNQUFNLENBQUNDLEtBQUssR0FDaEJFO0FBQ1Y7QUFFQSxJQUFJRSxZQUFZLENBQUN0RCxTQUFVLE9BQU9BLFdBQVU7QUFFNUMsSUFBSXVELE1BQU0sQ0FBQ04sUUFBUUMsTUFBTWxEO0lBQ3JCLElBQUl3RCxRQUFRLENBQUM7SUFDYixNQUFNQyxXQUFXckIsTUFBTWMsUUFBUTtRQUFDQTtLQUFLLEdBQUdOLGFBQWFNO0lBQ3JELE1BQU1RLFNBQVNELFNBQVNDLE1BQU07SUFDOUIsTUFBTUMsWUFBWUQsU0FBUztJQUMzQixNQUFPLEVBQUVGLFFBQVFFLE9BQVE7UUFDckIsTUFBTXZCLE1BQU1zQixRQUFRLENBQUNELE1BQU07UUFDM0IsSUFBSUksV0FBVzVEO1FBQ2YsSUFBSXdELFVBQVVHLFdBQVc7WUFDckIsTUFBTUUsV0FBV1osTUFBTSxDQUFDZCxJQUFJO1lBQzVCeUIsV0FDSXhELFNBQVN5RCxhQUFheEQsTUFBTUMsT0FBTyxDQUFDdUQsWUFDOUJBLFdBQ0EsQ0FBQ0MsTUFBTSxDQUFDTCxRQUFRLENBQUNELFFBQVEsRUFBRSxJQUN2QixFQUFFLEdBQ0YsQ0FBQztRQUNuQjtRQUNBLElBQUlyQixRQUFRLGVBQWVBLFFBQVEsaUJBQWlCQSxRQUFRLGFBQWE7WUFDckU7UUFDSjtRQUNBYyxNQUFNLENBQUNkLElBQUksR0FBR3lCO1FBQ2RYLFNBQVNBLE1BQU0sQ0FBQ2QsSUFBSTtJQUN4QjtBQUNKO0FBRUEsTUFBTTRCLFNBQVM7SUFDWEMsTUFBTTtJQUNOQyxXQUFXO0lBQ1hDLFFBQVE7QUFDWjtBQUNBLE1BQU1DLGtCQUFrQjtJQUNwQkMsUUFBUTtJQUNSQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsV0FBVztJQUNYQyxLQUFLO0FBQ1Q7QUFDQSxNQUFNQyx5QkFBeUI7SUFDM0JDLEtBQUs7SUFDTEMsS0FBSztJQUNMQyxXQUFXO0lBQ1hDLFdBQVc7SUFDWEMsU0FBUztJQUNUQyxVQUFVO0lBQ1ZDLFVBQVU7QUFDZDtBQUVBLE1BQU1DLGdDQUFrQnRGLGdEQUE0QixDQUFDO0FBQ3JEc0YsZ0JBQWdCRSxXQUFXLEdBQUc7QUFDOUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNkJDLEdBQ0QsTUFBTUMsaUJBQWlCLElBQU16Riw2Q0FBeUIsQ0FBQ3NGO0FBQ3ZEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTZCQyxHQUNELE1BQU1LLGVBQWUsQ0FBQ0M7SUFDbEIsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBRzNELE1BQU0sR0FBRzBEO0lBQzlCLHFCQUFRNUYsZ0RBQTRCLENBQUNzRixnQkFBZ0JTLFFBQVEsRUFBRTtRQUFFMUYsT0FBTzZCO0lBQUssR0FBRzJEO0FBQ3BGO0FBRUEsSUFBSUcsb0JBQW9CLENBQUNDLFdBQVdDLFNBQVNDLHFCQUFxQkMsU0FBUyxJQUFJO0lBQzNFLE1BQU0zQyxTQUFTO1FBQ1g0QyxlQUFlSCxRQUFRSSxjQUFjO0lBQ3pDO0lBQ0EsSUFBSyxNQUFNOUQsT0FBT3lELFVBQVc7UUFDekJNLE9BQU9DLGNBQWMsQ0FBQy9DLFFBQVFqQixLQUFLO1lBQy9CYSxLQUFLO2dCQUNELE1BQU1vRCxPQUFPakU7Z0JBQ2IsSUFBSTBELFFBQVFRLGVBQWUsQ0FBQ0QsS0FBSyxLQUFLakMsZ0JBQWdCSyxHQUFHLEVBQUU7b0JBQ3ZEcUIsUUFBUVEsZUFBZSxDQUFDRCxLQUFLLEdBQUcsQ0FBQ0wsVUFBVTVCLGdCQUFnQkssR0FBRztnQkFDbEU7Z0JBQ0FzQix1QkFBd0JBLENBQUFBLG1CQUFtQixDQUFDTSxLQUFLLEdBQUcsSUFBRztnQkFDdkQsT0FBT1IsU0FBUyxDQUFDUSxLQUFLO1lBQzFCO1FBQ0o7SUFDSjtJQUNBLE9BQU9oRDtBQUNYO0FBRUEsTUFBTWtELDRCQUE0QixNQUFrQixHQUFjNUcsQ0FBcUIsR0FBR0EsNENBQWU7QUFFekc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNkJDLEdBQ0QsU0FBUytHLGFBQWFsQixLQUFLO0lBQ3ZCLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxFQUFFUyxVQUFVYSxRQUFRYixPQUFPLEVBQUVjLFFBQVEsRUFBRS9GLElBQUksRUFBRWdHLEtBQUssRUFBRSxHQUFHckIsU0FBUyxDQUFDO0lBQ3ZFLE1BQU0sQ0FBQ0ssV0FBV2lCLGdCQUFnQixHQUFHbEgsMkNBQXVCLENBQUNrRyxRQUFRa0IsVUFBVTtJQUMvRSxNQUFNQyx1QkFBdUJySCx5Q0FBcUIsQ0FBQztRQUMvQ3VILFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsa0JBQWtCO1FBQ2xCQyxjQUFjO1FBQ2RDLFNBQVM7UUFDVEMsUUFBUTtJQUNaO0lBQ0FuQiwwQkFBMEIsSUFBTVQsUUFBUTZCLFVBQVUsQ0FBQztZQUMvQzlHO1lBQ0FnRixXQUFXb0IscUJBQXFCVyxPQUFPO1lBQ3ZDZjtZQUNBZ0IsVUFBVSxDQUFDaEM7Z0JBQ1AsQ0FBQ2UsWUFDR0UsZ0JBQWdCO29CQUNaLEdBQUdoQixRQUFRa0IsVUFBVTtvQkFDckIsR0FBR25CLFNBQVM7Z0JBQ2hCO1lBQ1I7UUFDSixJQUFJO1FBQUNoRjtRQUFNK0Y7UUFBVUM7S0FBTTtJQUMzQmpILDRDQUF3QixDQUFDO1FBQ3JCcUgscUJBQXFCVyxPQUFPLENBQUNILE9BQU8sSUFBSTNCLFFBQVFnQyxTQUFTLENBQUM7SUFDOUQsR0FBRztRQUFDaEM7S0FBUTtJQUNaLE9BQU9sRywwQ0FBc0IsQ0FBQyxJQUFNZ0csa0JBQWtCQyxXQUFXQyxTQUFTbUIscUJBQXFCVyxPQUFPLEVBQUUsUUFBUTtRQUFDL0I7UUFBV0M7S0FBUTtBQUN4STtBQUVBLElBQUlrQyxXQUFXLENBQUMvSCxTQUFVLE9BQU9BLFdBQVU7QUFFM0MsSUFBSWdJLHNCQUFzQixDQUFDaEgsT0FBT2lILFFBQVFDLFlBQVlDLFVBQVVoRjtJQUM1RCxJQUFJNEUsU0FBUy9HLFFBQVE7UUFDakJtSCxZQUFZRixPQUFPRyxLQUFLLENBQUNDLEdBQUcsQ0FBQ3JIO1FBQzdCLE9BQU9nQyxJQUFJa0YsWUFBWWxILE9BQU9tQztJQUNsQztJQUNBLElBQUk5QyxNQUFNQyxPQUFPLENBQUNVLFFBQVE7UUFDdEIsT0FBT0EsTUFBTXNILEdBQUcsQ0FBQyxDQUFDQyxZQUFlSixDQUFBQSxZQUFZRixPQUFPRyxLQUFLLENBQUNDLEdBQUcsQ0FBQ0UsWUFBWXZGLElBQUlrRixZQUFZSyxVQUFTO0lBQ3ZHO0lBQ0FKLFlBQWFGLENBQUFBLE9BQU9PLFFBQVEsR0FBRyxJQUFHO0lBQ2xDLE9BQU9OO0FBQ1g7QUFFQTs7Ozs7Ozs7Ozs7Ozs7O0NBZUMsR0FDRCxTQUFTTyxTQUFTbEQsS0FBSztJQUNuQixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRVMsVUFBVWEsUUFBUWIsT0FBTyxFQUFFakYsSUFBSSxFQUFFdUMsWUFBWSxFQUFFd0QsUUFBUSxFQUFFQyxLQUFLLEVBQUcsR0FBR3JCLFNBQVMsQ0FBQztJQUN0RixNQUFNbUQsZ0JBQWdCL0kseUNBQXFCLENBQUN3RDtJQUM1QyxNQUFNLENBQUNuRCxRQUFPMkksWUFBWSxHQUFHaEosMkNBQXVCLENBQUNrRyxRQUFRK0MsU0FBUyxDQUFDaEksTUFBTThILGNBQWNmLE9BQU87SUFDbEdyQiwwQkFBMEIsSUFBTVQsUUFBUTZCLFVBQVUsQ0FBQztZQUMvQzlHO1lBQ0FnRixXQUFXO2dCQUNQaUQsUUFBUTtZQUNaO1lBQ0FqQztZQUNBZ0IsVUFBVSxDQUFDaEMsWUFBYyxDQUFDZSxZQUN0QmdDLFlBQVlYLG9CQUFvQnBILE1BQU1pRixRQUFRb0MsTUFBTSxFQUFFckMsVUFBVWlELE1BQU0sSUFBSWhELFFBQVFpRCxXQUFXLEVBQUUsT0FBT0osY0FBY2YsT0FBTztRQUNuSSxJQUFJO1FBQUMvRztRQUFNaUY7UUFBU2M7UUFBVUM7S0FBTTtJQUNwQ2pILDRDQUF3QixDQUFDLElBQU1rRyxRQUFRa0QsZ0JBQWdCO0lBQ3ZELE9BQU8vSTtBQUNYO0FBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBdUJDLEdBQ0QsU0FBU2dKLGNBQWN6RCxLQUFLO0lBQ3hCLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxFQUFFeEUsSUFBSSxFQUFFK0YsUUFBUSxFQUFFZCxVQUFVYSxRQUFRYixPQUFPLEVBQUVvRCxnQkFBZ0IsRUFBRSxHQUFHMUQ7SUFDeEUsTUFBTTJELGVBQWVuSSxtQkFBbUI4RSxRQUFRb0MsTUFBTSxDQUFDa0IsS0FBSyxFQUFFdkk7SUFDOUQsTUFBTVosU0FBUXlJLFNBQVM7UUFDbkI1QztRQUNBakY7UUFDQXVDLGNBQWNILElBQUk2QyxRQUFRaUQsV0FBVyxFQUFFbEksTUFBTW9DLElBQUk2QyxRQUFRSSxjQUFjLEVBQUVyRixNQUFNMkUsTUFBTXBDLFlBQVk7UUFDakd5RCxPQUFPO0lBQ1g7SUFDQSxNQUFNaEIsWUFBWWEsYUFBYTtRQUMzQlo7UUFDQWpGO1FBQ0FnRyxPQUFPO0lBQ1g7SUFDQSxNQUFNd0MsU0FBU3pKLHlDQUFxQixDQUFDNEY7SUFDckMsTUFBTThELGlCQUFpQjFKLHlDQUFxQixDQUFDa0csUUFBUXlELFFBQVEsQ0FBQzFJLE1BQU07UUFDaEUsR0FBRzJFLE1BQU1nRSxLQUFLO1FBQ2R2SixPQUFBQTtRQUNBLEdBQUlzRCxVQUFVaUMsTUFBTW9CLFFBQVEsSUFBSTtZQUFFQSxVQUFVcEIsTUFBTW9CLFFBQVE7UUFBQyxJQUFJLENBQUMsQ0FBQztJQUNyRTtJQUNBLE1BQU02QyxhQUFhN0osMENBQXNCLENBQUMsSUFBTXVHLE9BQU91RCxnQkFBZ0IsQ0FBQyxDQUFDLEdBQUc7WUFDeEVDLFNBQVM7Z0JBQ0xDLFlBQVk7Z0JBQ1ozRyxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJNEMsVUFBVTZCLE1BQU0sRUFBRTdHO1lBQ3ZDO1lBQ0FzRyxTQUFTO2dCQUNMeUMsWUFBWTtnQkFDWjNHLEtBQUssSUFBTSxDQUFDLENBQUNBLElBQUk0QyxVQUFVd0IsV0FBVyxFQUFFeEc7WUFDNUM7WUFDQWdKLFdBQVc7Z0JBQ1BELFlBQVk7Z0JBQ1ozRyxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJNEMsVUFBVXlCLGFBQWEsRUFBRXpHO1lBQzlDO1lBQ0EyRyxjQUFjO2dCQUNWb0MsWUFBWTtnQkFDWjNHLEtBQUssSUFBTSxDQUFDLENBQUNBLElBQUk0QyxVQUFVMEIsZ0JBQWdCLEVBQUUxRztZQUNqRDtZQUNBaUosT0FBTztnQkFDSEYsWUFBWTtnQkFDWjNHLEtBQUssSUFBTUEsSUFBSTRDLFVBQVU2QixNQUFNLEVBQUU3RztZQUNyQztRQUNKLElBQUk7UUFBQ2dGO1FBQVdoRjtLQUFLO0lBQ3JCLE1BQU15RCxXQUFXMUUsOENBQTBCLENBQUMsQ0FBQ2EsUUFBVTZJLGVBQWUxQixPQUFPLENBQUN0RCxRQUFRLENBQUM7WUFDbkY1RCxRQUFRO2dCQUNKVCxPQUFPTyxjQUFjQztnQkFDckJJLE1BQU1BO1lBQ1Y7WUFDQWQsTUFBTWlFLE9BQU9HLE1BQU07UUFDdkIsSUFBSTtRQUFDdEQ7S0FBSztJQUNWLE1BQU13RCxTQUFTekUsOENBQTBCLENBQUMsSUFBTTBKLGVBQWUxQixPQUFPLENBQUN2RCxNQUFNLENBQUM7WUFDMUUzRCxRQUFRO2dCQUNKVCxPQUFPZ0QsSUFBSTZDLFFBQVFpRCxXQUFXLEVBQUVsSTtnQkFDaENBLE1BQU1BO1lBQ1Y7WUFDQWQsTUFBTWlFLE9BQU9DLElBQUk7UUFDckIsSUFBSTtRQUFDcEQ7UUFBTWlGLFFBQVFpRCxXQUFXO0tBQUM7SUFDL0IsTUFBTWlCLE1BQU1wSyw4Q0FBMEIsQ0FBQyxDQUFDcUs7UUFDcEMsTUFBTUMsUUFBUWpILElBQUk2QyxRQUFRcUUsT0FBTyxFQUFFdEo7UUFDbkMsSUFBSXFKLFNBQVNELEtBQUs7WUFDZEMsTUFBTUUsRUFBRSxDQUFDSixHQUFHLEdBQUc7Z0JBQ1hLLE9BQU8sSUFBTUosSUFBSUksS0FBSyxJQUFJSixJQUFJSSxLQUFLO2dCQUNuQ0MsUUFBUSxJQUFNTCxJQUFJSyxNQUFNLElBQUlMLElBQUlLLE1BQU07Z0JBQ3RDQyxtQkFBbUIsQ0FBQ0MsVUFBWVAsSUFBSU0saUJBQWlCLENBQUNDO2dCQUN0REMsZ0JBQWdCLElBQU1SLElBQUlRLGNBQWM7WUFDNUM7UUFDSjtJQUNKLEdBQUc7UUFBQzNFLFFBQVFxRSxPQUFPO1FBQUV0SjtLQUFLO0lBQzFCLE1BQU1xSixRQUFRdEssMENBQXNCLENBQUMsSUFBTztZQUN4Q2lCO1lBQ0FaLE9BQUFBO1lBQ0EsR0FBSXNELFVBQVVxRCxhQUFhZixVQUFVZSxRQUFRLEdBQ3ZDO2dCQUFFQSxVQUFVZixVQUFVZSxRQUFRLElBQUlBO1lBQVMsSUFDM0MsQ0FBQyxDQUFDO1lBQ1J0QztZQUNBRDtZQUNBMkY7UUFDSixJQUFJO1FBQUNuSjtRQUFNK0Y7UUFBVWYsVUFBVWUsUUFBUTtRQUFFdEM7UUFBVUQ7UUFBUTJGO1FBQUsvSjtLQUFNO0lBQ3RFTCw0Q0FBd0IsQ0FBQztRQUNyQixNQUFNOEsseUJBQXlCNUUsUUFBUTZFLFFBQVEsQ0FBQ3pCLGdCQUFnQixJQUFJQTtRQUNwRXBELFFBQVF5RCxRQUFRLENBQUMxSSxNQUFNO1lBQ25CLEdBQUd3SSxPQUFPekIsT0FBTyxDQUFDNEIsS0FBSztZQUN2QixHQUFJakcsVUFBVThGLE9BQU96QixPQUFPLENBQUNoQixRQUFRLElBQy9CO2dCQUFFQSxVQUFVeUMsT0FBT3pCLE9BQU8sQ0FBQ2hCLFFBQVE7WUFBQyxJQUNwQyxDQUFDLENBQUM7UUFDWjtRQUNBLE1BQU1nRSxnQkFBZ0IsQ0FBQy9KLE1BQU1aO1lBQ3pCLE1BQU1pSyxRQUFRakgsSUFBSTZDLFFBQVFxRSxPQUFPLEVBQUV0SjtZQUNuQyxJQUFJcUosU0FBU0EsTUFBTUUsRUFBRSxFQUFFO2dCQUNuQkYsTUFBTUUsRUFBRSxDQUFDUyxLQUFLLEdBQUc1SztZQUNyQjtRQUNKO1FBQ0EySyxjQUFjL0osTUFBTTtRQUNwQixJQUFJNkosd0JBQXdCO1lBQ3hCLE1BQU16SyxTQUFRNEIsWUFBWW9CLElBQUk2QyxRQUFRNkUsUUFBUSxDQUFDMUUsYUFBYSxFQUFFcEY7WUFDOUQyQyxJQUFJc0MsUUFBUUksY0FBYyxFQUFFckYsTUFBTVo7WUFDbEMsSUFBSXNDLFlBQVlVLElBQUk2QyxRQUFRaUQsV0FBVyxFQUFFbEksUUFBUTtnQkFDN0MyQyxJQUFJc0MsUUFBUWlELFdBQVcsRUFBRWxJLE1BQU1aO1lBQ25DO1FBQ0o7UUFDQSxDQUFDa0osZ0JBQWdCckQsUUFBUXlELFFBQVEsQ0FBQzFJO1FBQ2xDLE9BQU87WUFDRnNJLENBQUFBLGVBQ0t1QiwwQkFBMEIsQ0FBQzVFLFFBQVFnRixNQUFNLENBQUNDLE1BQU0sR0FDaERMLHNCQUFxQixJQUNyQjVFLFFBQVFrRixVQUFVLENBQUNuSyxRQUNuQitKLGNBQWMvSixNQUFNO1FBQzlCO0lBQ0osR0FBRztRQUFDQTtRQUFNaUY7UUFBU3FEO1FBQWNEO0tBQWlCO0lBQ2xEdEosNENBQXdCLENBQUM7UUFDckJrRyxRQUFRbUYsaUJBQWlCLENBQUM7WUFDdEJyRTtZQUNBL0Y7UUFDSjtJQUNKLEdBQUc7UUFBQytGO1FBQVUvRjtRQUFNaUY7S0FBUTtJQUM1QixPQUFPbEcsMENBQXNCLENBQUMsSUFBTztZQUNqQ3NLO1lBQ0FyRTtZQUNBNEQ7UUFDSixJQUFJO1FBQUNTO1FBQU9yRTtRQUFXNEQ7S0FBVztBQUN0QztBQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXlDQyxHQUNELE1BQU15QixhQUFhLENBQUMxRixRQUFVQSxNQUFNMkYsTUFBTSxDQUFDbEMsY0FBY3pEO0FBRXpELE1BQU00RixVQUFVLENBQUNDO0lBQ2IsTUFBTUMsU0FBUyxDQUFDO0lBQ2hCLEtBQUssTUFBTWxKLE9BQU8rRCxPQUFPb0YsSUFBSSxDQUFDRixLQUFNO1FBQ2hDLElBQUlqTCxhQUFhaUwsR0FBRyxDQUFDakosSUFBSSxLQUFLaUosR0FBRyxDQUFDakosSUFBSSxLQUFLLE1BQU07WUFDN0MsTUFBTW9KLFNBQVNKLFFBQVFDLEdBQUcsQ0FBQ2pKLElBQUk7WUFDL0IsS0FBSyxNQUFNcUosYUFBYXRGLE9BQU9vRixJQUFJLENBQUNDLFFBQVM7Z0JBQ3pDRixNQUFNLENBQUMsQ0FBQyxFQUFFbEosSUFBSSxDQUFDLEVBQUVxSixVQUFVLENBQUMsQ0FBQyxHQUFHRCxNQUFNLENBQUNDLFVBQVU7WUFDckQ7UUFDSixPQUNLO1lBQ0RILE1BQU0sQ0FBQ2xKLElBQUksR0FBR2lKLEdBQUcsQ0FBQ2pKLElBQUk7UUFDMUI7SUFDSjtJQUNBLE9BQU9rSjtBQUNYO0FBRUEsTUFBTUksZUFBZTtBQUNyQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBcUJDLEdBQ0QsU0FBU0MsS0FBS25HLEtBQUs7SUFDZixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sQ0FBQ3VHLFNBQVNDLFdBQVcsR0FBR2pNLDJDQUF1QixDQUFDO0lBQ3RELE1BQU0sRUFBRWtHLFVBQVVhLFFBQVFiLE9BQU8sRUFBRXZCLFFBQVEsRUFBRWtCLFFBQVEsRUFBRXNGLE1BQU0sRUFBRWUsU0FBU0osWUFBWSxFQUFFSyxPQUFPLEVBQUVDLE9BQU8sRUFBRUMsT0FBTyxFQUFFZCxNQUFNLEVBQUVlLFNBQVMsRUFBRUMsY0FBYyxFQUFFLEdBQUdDLE1BQU0sR0FBRzVHO0lBQ2hLLE1BQU02RyxTQUFTLE9BQU81TDtRQUNsQixJQUFJNkwsV0FBVztRQUNmLElBQUl2TSxPQUFPO1FBQ1gsTUFBTStGLFFBQVF5RyxZQUFZLENBQUMsT0FBT3pLO1lBQzlCLE1BQU0wSyxXQUFXLElBQUlDO1lBQ3JCLElBQUlDLGVBQWU7WUFDbkIsSUFBSTtnQkFDQUEsZUFBZUMsS0FBS0MsU0FBUyxDQUFDOUs7WUFDbEMsRUFDQSxPQUFPK0ssSUFBSSxDQUFFO1lBQ2IsTUFBTUMsb0JBQW9CMUIsUUFBUXRGLFFBQVFpRCxXQUFXO1lBQ3JELElBQUssTUFBTTNHLE9BQU8wSyxrQkFBbUI7Z0JBQ2pDTixTQUFTTyxNQUFNLENBQUMzSyxLQUFLMEssaUJBQWlCLENBQUMxSyxJQUFJO1lBQy9DO1lBQ0EsSUFBSW1DLFVBQVU7Z0JBQ1YsTUFBTUEsU0FBUztvQkFDWHpDO29CQUNBckI7b0JBQ0FxTDtvQkFDQVU7b0JBQ0FFO2dCQUNKO1lBQ0o7WUFDQSxJQUFJM0IsUUFBUTtnQkFDUixJQUFJO29CQUNBLE1BQU1pQyxnQ0FBZ0M7d0JBQ2xDakIsV0FBV0EsT0FBTyxDQUFDLGVBQWU7d0JBQ2xDQztxQkFDSCxDQUFDaUIsSUFBSSxDQUFDLENBQUNoTixTQUFVQSxVQUFTQSxPQUFNaU4sUUFBUSxDQUFDO29CQUMxQyxNQUFNQyxXQUFXLE1BQU1DLE1BQU1DLE9BQU90QyxTQUFTO3dCQUN6Q2U7d0JBQ0FDLFNBQVM7NEJBQ0wsR0FBR0EsT0FBTzs0QkFDVixHQUFJQyxVQUFVO2dDQUFFLGdCQUFnQkE7NEJBQVEsSUFBSSxDQUFDLENBQUM7d0JBQ2xEO3dCQUNBc0IsTUFBTU4sZ0NBQWdDTixlQUFlRjtvQkFDekQ7b0JBQ0EsSUFBSVcsWUFDQ2hCLENBQUFBLGlCQUNLLENBQUNBLGVBQWVnQixTQUFTSSxNQUFNLElBQy9CSixTQUFTSSxNQUFNLEdBQUcsT0FBT0osU0FBU0ksTUFBTSxJQUFJLEdBQUUsR0FBSTt3QkFDeERqQixXQUFXO3dCQUNYTCxXQUFXQSxRQUFROzRCQUFFa0I7d0JBQVM7d0JBQzlCcE4sT0FBT3NOLE9BQU9GLFNBQVNJLE1BQU07b0JBQ2pDLE9BQ0s7d0JBQ0RyQixhQUFhQSxVQUFVOzRCQUFFaUI7d0JBQVM7b0JBQ3RDO2dCQUNKLEVBQ0EsT0FBT3JELE9BQU87b0JBQ1Z3QyxXQUFXO29CQUNYTCxXQUFXQSxRQUFRO3dCQUFFbkM7b0JBQU07Z0JBQy9CO1lBQ0o7UUFDSixHQUFHcko7UUFDSCxJQUFJNkwsWUFBWTlHLE1BQU1NLE9BQU8sRUFBRTtZQUMzQk4sTUFBTU0sT0FBTyxDQUFDMEgsU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFDL0JDLG9CQUFvQjtZQUN4QjtZQUNBbkksTUFBTU0sT0FBTyxDQUFDOEgsUUFBUSxDQUFDLGVBQWU7Z0JBQ2xDN047WUFDSjtRQUNKO0lBQ0o7SUFDQUgsNENBQXdCLENBQUM7UUFDckJpTSxXQUFXO0lBQ2YsR0FBRyxFQUFFO0lBQ0wsT0FBT1YsdUJBQVV2TCxnREFBNEIsQ0FBQ0EsMkNBQXVCLEVBQUUsTUFBTXVMLE9BQU87UUFDaEZrQjtJQUNKLG9CQUFRek0sZ0RBQTRCLENBQUMsUUFBUTtRQUFFa08sWUFBWWxDO1FBQVNiLFFBQVFBO1FBQVFlLFFBQVFBO1FBQVFFLFNBQVNBO1FBQVN6SCxVQUFVOEg7UUFBUSxHQUFHRCxJQUFJO0lBQUMsR0FBRzNHO0FBQ3ZKO0FBRUEsSUFBSXNJLGVBQWUsQ0FBQ2xOLE1BQU1tTiwwQkFBMEJ0RyxRQUFRM0gsTUFBTXlLLFVBQVl3RCwyQkFDeEU7UUFDRSxHQUFHdEcsTUFBTSxDQUFDN0csS0FBSztRQUNmb04sT0FBTztZQUNILEdBQUl2RyxNQUFNLENBQUM3RyxLQUFLLElBQUk2RyxNQUFNLENBQUM3RyxLQUFLLENBQUNvTixLQUFLLEdBQUd2RyxNQUFNLENBQUM3RyxLQUFLLENBQUNvTixLQUFLLEdBQUcsQ0FBQyxDQUFDO1lBQ2hFLENBQUNsTyxLQUFLLEVBQUV5SyxXQUFXO1FBQ3ZCO0lBQ0osSUFDRSxDQUFDO0FBRVAsSUFBSTBELHdCQUF3QixDQUFDak8sU0FBV0ssTUFBTUMsT0FBTyxDQUFDTixVQUFTQSxTQUFRO1FBQUNBO0tBQU07QUFFOUUsSUFBSWtPLGdCQUFnQjtJQUNoQixJQUFJQyxhQUFhLEVBQUU7SUFDbkIsTUFBTVYsT0FBTyxDQUFDek47UUFDVixLQUFLLE1BQU1vTyxZQUFZRCxXQUFZO1lBQy9CQyxTQUFTWCxJQUFJLElBQUlXLFNBQVNYLElBQUksQ0FBQ3pOO1FBQ25DO0lBQ0o7SUFDQSxNQUFNcU8sWUFBWSxDQUFDRDtRQUNmRCxXQUFXRyxJQUFJLENBQUNGO1FBQ2hCLE9BQU87WUFDSEcsYUFBYTtnQkFDVEosYUFBYUEsV0FBV3pMLE1BQU0sQ0FBQyxDQUFDOEwsSUFBTUEsTUFBTUo7WUFDaEQ7UUFDSjtJQUNKO0lBQ0EsTUFBTUcsY0FBYztRQUNoQkosYUFBYSxFQUFFO0lBQ25CO0lBQ0EsT0FBTztRQUNILElBQUlNLGFBQVk7WUFDWixPQUFPTjtRQUNYO1FBQ0FWO1FBQ0FZO1FBQ0FFO0lBQ0o7QUFDSjtBQUVBLElBQUlHLGNBQWMsQ0FBQzFPLFNBQVVFLGtCQUFrQkYsV0FBVSxDQUFDRyxhQUFhSDtBQUV2RSxTQUFTMk8sVUFBVUMsT0FBTyxFQUFFQyxPQUFPLEVBQUVDLG9CQUFvQixJQUFJQyxTQUFTO0lBQ2xFLElBQUlMLFlBQVlFLFlBQVlGLFlBQVlHLFVBQVU7UUFDOUMsT0FBT0QsWUFBWUM7SUFDdkI7SUFDQSxJQUFJOU8sYUFBYTZPLFlBQVk3TyxhQUFhOE8sVUFBVTtRQUNoRCxPQUFPRCxRQUFRSSxPQUFPLE9BQU9ILFFBQVFHLE9BQU87SUFDaEQ7SUFDQSxNQUFNQyxRQUFRL0ksT0FBT29GLElBQUksQ0FBQ3NEO0lBQzFCLE1BQU1NLFFBQVFoSixPQUFPb0YsSUFBSSxDQUFDdUQ7SUFDMUIsSUFBSUksTUFBTXZMLE1BQU0sS0FBS3dMLE1BQU14TCxNQUFNLEVBQUU7UUFDL0IsT0FBTztJQUNYO0lBQ0EsSUFBSW9MLGtCQUFrQjdOLEdBQUcsQ0FBQzJOLFlBQVlFLGtCQUFrQjdOLEdBQUcsQ0FBQzROLFVBQVU7UUFDbEUsT0FBTztJQUNYO0lBQ0FDLGtCQUFrQnpHLEdBQUcsQ0FBQ3VHO0lBQ3RCRSxrQkFBa0J6RyxHQUFHLENBQUN3RztJQUN0QixLQUFLLE1BQU0xTSxPQUFPOE0sTUFBTztRQUNyQixNQUFNRSxPQUFPUCxPQUFPLENBQUN6TSxJQUFJO1FBQ3pCLElBQUksQ0FBQytNLE1BQU1qQyxRQUFRLENBQUM5SyxNQUFNO1lBQ3RCLE9BQU87UUFDWDtRQUNBLElBQUlBLFFBQVEsT0FBTztZQUNmLE1BQU1pTixPQUFPUCxPQUFPLENBQUMxTSxJQUFJO1lBQ3pCLElBQUksYUFBY2dOLFNBQVNwUCxhQUFhcVAsU0FDbkNoUCxTQUFTK08sU0FBUy9PLFNBQVNnUCxTQUMzQi9PLE1BQU1DLE9BQU8sQ0FBQzZPLFNBQVM5TyxNQUFNQyxPQUFPLENBQUM4TyxRQUNwQyxDQUFDVCxVQUFVUSxNQUFNQyxNQUFNTixxQkFDdkJLLFNBQVNDLE1BQU07Z0JBQ2pCLE9BQU87WUFDWDtRQUNKO0lBQ0o7SUFDQSxPQUFPO0FBQ1g7QUFFQSxJQUFJQyxnQkFBZ0IsQ0FBQ3JQLFNBQVVJLFNBQVNKLFdBQVUsQ0FBQ2tHLE9BQU9vRixJQUFJLENBQUN0TCxRQUFPMEQsTUFBTTtBQUU1RSxJQUFJNEwsY0FBYyxDQUFDelAsVUFBWUEsUUFBUUMsSUFBSSxLQUFLO0FBRWhELElBQUl5UCxhQUFhLENBQUN2UCxTQUFVLE9BQU9BLFdBQVU7QUFFN0MsSUFBSXdQLGdCQUFnQixDQUFDeFA7SUFDakIsSUFBSSxDQUFDd0IsT0FBTztRQUNSLE9BQU87SUFDWDtJQUNBLE1BQU1pTyxRQUFRelAsU0FBUUEsT0FBTTBQLGFBQWEsR0FBRztJQUM1QyxPQUFRMVAsa0JBQ0h5UCxDQUFBQSxTQUFTQSxNQUFNRSxXQUFXLEdBQUdGLE1BQU1FLFdBQVcsQ0FBQ2pPLFdBQVcsR0FBR0EsV0FBVTtBQUNoRjtBQUVBLElBQUlrTyxtQkFBbUIsQ0FBQy9QLFVBQVlBLFFBQVFDLElBQUksS0FBSyxDQUFDLGVBQWUsQ0FBQztBQUV0RSxJQUFJK1AsZUFBZSxDQUFDaFEsVUFBWUEsUUFBUUMsSUFBSSxLQUFLO0FBRWpELElBQUlnUSxvQkFBb0IsQ0FBQy9GLE1BQVE4RixhQUFhOUYsUUFBUW5LLGdCQUFnQm1LO0FBRXRFLElBQUlnRyxPQUFPLENBQUNoRyxNQUFReUYsY0FBY3pGLFFBQVFBLElBQUlpRyxXQUFXO0FBRXpELFNBQVNDLFFBQVFoTixNQUFNLEVBQUVpTixVQUFVO0lBQy9CLE1BQU14TSxTQUFTd00sV0FBV0MsS0FBSyxDQUFDLEdBQUcsQ0FBQyxHQUFHek0sTUFBTTtJQUM3QyxJQUFJRixRQUFRO0lBQ1osTUFBT0EsUUFBUUUsT0FBUTtRQUNuQlQsU0FBU1gsWUFBWVcsVUFBVU8sVUFBVVAsTUFBTSxDQUFDaU4sVUFBVSxDQUFDMU0sUUFBUSxDQUFDO0lBQ3hFO0lBQ0EsT0FBT1A7QUFDWDtBQUNBLFNBQVNtTixhQUFhaEYsR0FBRztJQUNyQixJQUFLLE1BQU1qSixPQUFPaUosSUFBSztRQUNuQixJQUFJQSxJQUFJN0osY0FBYyxDQUFDWSxRQUFRLENBQUNHLFlBQVk4SSxHQUFHLENBQUNqSixJQUFJLEdBQUc7WUFDbkQsT0FBTztRQUNYO0lBQ0o7SUFDQSxPQUFPO0FBQ1g7QUFDQSxTQUFTa08sTUFBTXBOLE1BQU0sRUFBRUMsSUFBSTtJQUN2QixNQUFNb04sUUFBUWpRLE1BQU1DLE9BQU8sQ0FBQzRDLFFBQ3RCQSxPQUNBZCxNQUFNYyxRQUNGO1FBQUNBO0tBQUssR0FDTk4sYUFBYU07SUFDdkIsTUFBTXFOLGNBQWNELE1BQU01TSxNQUFNLEtBQUssSUFBSVQsU0FBU2dOLFFBQVFoTixRQUFRcU47SUFDbEUsTUFBTTlNLFFBQVE4TSxNQUFNNU0sTUFBTSxHQUFHO0lBQzdCLE1BQU12QixNQUFNbU8sS0FBSyxDQUFDOU0sTUFBTTtJQUN4QixJQUFJK00sYUFBYTtRQUNiLE9BQU9BLFdBQVcsQ0FBQ3BPLElBQUk7SUFDM0I7SUFDQSxJQUFJcUIsVUFBVSxLQUNULFVBQVUrTSxnQkFBZ0JsQixjQUFja0IsZ0JBQ3BDbFEsTUFBTUMsT0FBTyxDQUFDaVEsZ0JBQWdCSCxhQUFhRyxZQUFZLEdBQUk7UUFDaEVGLE1BQU1wTixRQUFRcU4sTUFBTUgsS0FBSyxDQUFDLEdBQUcsQ0FBQztJQUNsQztJQUNBLE9BQU9sTjtBQUNYO0FBRUEsSUFBSXVOLG9CQUFvQixDQUFDM087SUFDckIsSUFBSyxNQUFNTSxPQUFPTixLQUFNO1FBQ3BCLElBQUkwTixXQUFXMU4sSUFBSSxDQUFDTSxJQUFJLEdBQUc7WUFDdkIsT0FBTztRQUNYO0lBQ0o7SUFDQSxPQUFPO0FBQ1g7QUFFQSxTQUFTc08sZ0JBQWdCNU8sSUFBSSxFQUFFNk8sU0FBUyxDQUFDLENBQUM7SUFDdEMsTUFBTUMsb0JBQW9CdFEsTUFBTUMsT0FBTyxDQUFDdUI7SUFDeEMsSUFBSXpCLFNBQVN5QixTQUFTOE8sbUJBQW1CO1FBQ3JDLElBQUssTUFBTXhPLE9BQU9OLEtBQU07WUFDcEIsSUFBSXhCLE1BQU1DLE9BQU8sQ0FBQ3VCLElBQUksQ0FBQ00sSUFBSSxLQUN0Qi9CLFNBQVN5QixJQUFJLENBQUNNLElBQUksS0FBSyxDQUFDcU8sa0JBQWtCM08sSUFBSSxDQUFDTSxJQUFJLEdBQUk7Z0JBQ3hEdU8sTUFBTSxDQUFDdk8sSUFBSSxHQUFHOUIsTUFBTUMsT0FBTyxDQUFDdUIsSUFBSSxDQUFDTSxJQUFJLElBQUksRUFBRSxHQUFHLENBQUM7Z0JBQy9Dc08sZ0JBQWdCNU8sSUFBSSxDQUFDTSxJQUFJLEVBQUV1TyxNQUFNLENBQUN2TyxJQUFJO1lBQzFDLE9BQ0ssSUFBSSxDQUFDakMsa0JBQWtCMkIsSUFBSSxDQUFDTSxJQUFJLEdBQUc7Z0JBQ3BDdU8sTUFBTSxDQUFDdk8sSUFBSSxHQUFHO1lBQ2xCO1FBQ0o7SUFDSjtJQUNBLE9BQU91TztBQUNYO0FBQ0EsU0FBU0UsZ0NBQWdDL08sSUFBSSxFQUFFcUcsVUFBVSxFQUFFMkkscUJBQXFCO0lBQzVFLE1BQU1GLG9CQUFvQnRRLE1BQU1DLE9BQU8sQ0FBQ3VCO0lBQ3hDLElBQUl6QixTQUFTeUIsU0FBUzhPLG1CQUFtQjtRQUNyQyxJQUFLLE1BQU14TyxPQUFPTixLQUFNO1lBQ3BCLElBQUl4QixNQUFNQyxPQUFPLENBQUN1QixJQUFJLENBQUNNLElBQUksS0FDdEIvQixTQUFTeUIsSUFBSSxDQUFDTSxJQUFJLEtBQUssQ0FBQ3FPLGtCQUFrQjNPLElBQUksQ0FBQ00sSUFBSSxHQUFJO2dCQUN4RCxJQUFJRyxZQUFZNEYsZUFDWndHLFlBQVltQyxxQkFBcUIsQ0FBQzFPLElBQUksR0FBRztvQkFDekMwTyxxQkFBcUIsQ0FBQzFPLElBQUksR0FBRzlCLE1BQU1DLE9BQU8sQ0FBQ3VCLElBQUksQ0FBQ00sSUFBSSxJQUM5Q3NPLGdCQUFnQjVPLElBQUksQ0FBQ00sSUFBSSxFQUFFLEVBQUUsSUFDN0I7d0JBQUUsR0FBR3NPLGdCQUFnQjVPLElBQUksQ0FBQ00sSUFBSSxDQUFDO29CQUFDO2dCQUMxQyxPQUNLO29CQUNEeU8sZ0NBQWdDL08sSUFBSSxDQUFDTSxJQUFJLEVBQUVqQyxrQkFBa0JnSSxjQUFjLENBQUMsSUFBSUEsVUFBVSxDQUFDL0YsSUFBSSxFQUFFME8scUJBQXFCLENBQUMxTyxJQUFJO2dCQUMvSDtZQUNKLE9BQ0s7Z0JBQ0QwTyxxQkFBcUIsQ0FBQzFPLElBQUksR0FBRyxDQUFDd00sVUFBVTlNLElBQUksQ0FBQ00sSUFBSSxFQUFFK0YsVUFBVSxDQUFDL0YsSUFBSTtZQUN0RTtRQUNKO0lBQ0o7SUFDQSxPQUFPME87QUFDWDtBQUNBLElBQUlDLGlCQUFpQixDQUFDOUssZUFBZWtDLGFBQWUwSSxnQ0FBZ0M1SyxlQUFla0MsWUFBWXVJLGdCQUFnQnZJO0FBRS9ILE1BQU02SSxnQkFBZ0I7SUFDbEIvUSxPQUFPO0lBQ1B3SCxTQUFTO0FBQ2I7QUFDQSxNQUFNd0osY0FBYztJQUFFaFIsT0FBTztJQUFNd0gsU0FBUztBQUFLO0FBQ2pELElBQUl5SixtQkFBbUIsQ0FBQ0M7SUFDcEIsSUFBSTdRLE1BQU1DLE9BQU8sQ0FBQzRRLFVBQVU7UUFDeEIsSUFBSUEsUUFBUXhOLE1BQU0sR0FBRyxHQUFHO1lBQ3BCLE1BQU1tRixTQUFTcUksUUFDVnhPLE1BQU0sQ0FBQyxDQUFDeU8sU0FBV0EsVUFBVUEsT0FBT3pRLE9BQU8sSUFBSSxDQUFDeVEsT0FBT3hLLFFBQVEsRUFDL0QyQixHQUFHLENBQUMsQ0FBQzZJLFNBQVdBLE9BQU9uUixLQUFLO1lBQ2pDLE9BQU87Z0JBQUVBLE9BQU82STtnQkFBUXJCLFNBQVMsQ0FBQyxDQUFDcUIsT0FBT25GLE1BQU07WUFBQztRQUNyRDtRQUNBLE9BQU93TixPQUFPLENBQUMsRUFBRSxDQUFDeFEsT0FBTyxJQUFJLENBQUN3USxPQUFPLENBQUMsRUFBRSxDQUFDdkssUUFBUSxHQUV6Q3VLLE9BQU8sQ0FBQyxFQUFFLENBQUNFLFVBQVUsSUFBSSxDQUFDOU8sWUFBWTRPLE9BQU8sQ0FBQyxFQUFFLENBQUNFLFVBQVUsQ0FBQ3BSLEtBQUssSUFDM0RzQyxZQUFZNE8sT0FBTyxDQUFDLEVBQUUsQ0FBQ2xSLEtBQUssS0FBS2tSLE9BQU8sQ0FBQyxFQUFFLENBQUNsUixLQUFLLEtBQUssS0FDbERnUixjQUNBO1lBQUVoUixPQUFPa1IsT0FBTyxDQUFDLEVBQUUsQ0FBQ2xSLEtBQUs7WUFBRXdILFNBQVM7UUFBSyxJQUM3Q3dKLGNBQ1JEO0lBQ1Y7SUFDQSxPQUFPQTtBQUNYO0FBRUEsSUFBSU0sa0JBQWtCLENBQUNyUixRQUFPLEVBQUVzUixhQUFhLEVBQUVDLFdBQVcsRUFBRUMsVUFBVSxFQUFFLEdBQUtsUCxZQUFZdEMsVUFDbkZBLFNBQ0FzUixnQkFDSXRSLFdBQVUsS0FDTnlSLE1BQ0F6UixTQUNJLENBQUNBLFNBQ0RBLFNBQ1J1UixlQUFleEosU0FBUy9ILFVBQ3BCLElBQUlDLEtBQUtELFVBQ1R3UixhQUNJQSxXQUFXeFIsVUFDWEE7QUFFbEIsTUFBTTBSLGdCQUFnQjtJQUNsQmxLLFNBQVM7SUFDVHhILE9BQU87QUFDWDtBQUNBLElBQUkyUixnQkFBZ0IsQ0FBQ1QsVUFBWTdRLE1BQU1DLE9BQU8sQ0FBQzRRLFdBQ3pDQSxRQUFRN04sTUFBTSxDQUFDLENBQUN1TyxVQUFVVCxTQUFXQSxVQUFVQSxPQUFPelEsT0FBTyxJQUFJLENBQUN5USxPQUFPeEssUUFBUSxHQUM3RTtZQUNFYSxTQUFTO1lBQ1R4SCxPQUFPbVIsT0FBT25SLEtBQUs7UUFDdkIsSUFDRTRSLFVBQVVGLGlCQUNkQTtBQUVOLFNBQVNHLGNBQWMxSCxFQUFFO0lBQ3JCLE1BQU1KLE1BQU1JLEdBQUdKLEdBQUc7SUFDbEIsSUFBSXVGLFlBQVl2RixNQUFNO1FBQ2xCLE9BQU9BLElBQUkrSCxLQUFLO0lBQ3BCO0lBQ0EsSUFBSWpDLGFBQWE5RixNQUFNO1FBQ25CLE9BQU80SCxjQUFjeEgsR0FBRzRILElBQUksRUFBRS9SLEtBQUs7SUFDdkM7SUFDQSxJQUFJNFAsaUJBQWlCN0YsTUFBTTtRQUN2QixPQUFPO2VBQUlBLElBQUlpSSxlQUFlO1NBQUMsQ0FBQzFKLEdBQUcsQ0FBQyxDQUFDLEVBQUV0SSxPQUFBQSxNQUFLLEVBQUUsR0FBS0E7SUFDdkQ7SUFDQSxJQUFJSixnQkFBZ0JtSyxNQUFNO1FBQ3RCLE9BQU9rSCxpQkFBaUI5RyxHQUFHNEgsSUFBSSxFQUFFL1IsS0FBSztJQUMxQztJQUNBLE9BQU9xUixnQkFBZ0IvTyxZQUFZeUgsSUFBSS9KLEtBQUssSUFBSW1LLEdBQUdKLEdBQUcsQ0FBQy9KLEtBQUssR0FBRytKLElBQUkvSixLQUFLLEVBQUVtSztBQUM5RTtBQUVBLElBQUk4SCxxQkFBcUIsQ0FBQ0MsYUFBYWhJLFNBQVNpSSxjQUFjQztJQUMxRCxNQUFNMUIsU0FBUyxDQUFDO0lBQ2hCLEtBQUssTUFBTTlQLFFBQVFzUixZQUFhO1FBQzVCLE1BQU1qSSxRQUFRakgsSUFBSWtILFNBQVN0SjtRQUMzQnFKLFNBQVMxRyxJQUFJbU4sUUFBUTlQLE1BQU1xSixNQUFNRSxFQUFFO0lBQ3ZDO0lBQ0EsT0FBTztRQUNIZ0k7UUFDQW5SLE9BQU87ZUFBSWtSO1NBQVk7UUFDdkJ4QjtRQUNBMEI7SUFDSjtBQUNKO0FBRUEsSUFBSUMsVUFBVSxDQUFDclMsU0FBVUEsa0JBQWlCc1M7QUFFMUMsSUFBSUMsZUFBZSxDQUFDQyxPQUFTbFEsWUFBWWtRLFFBQ25DQSxPQUNBSCxRQUFRRyxRQUNKQSxLQUFLQyxNQUFNLEdBQ1hyUyxTQUFTb1MsUUFDTEgsUUFBUUcsS0FBS3hTLEtBQUssSUFDZHdTLEtBQUt4UyxLQUFLLENBQUN5UyxNQUFNLEdBQ2pCRCxLQUFLeFMsS0FBSyxHQUNkd1M7QUFFZCxJQUFJRSxxQkFBcUIsQ0FBQ0MsT0FBVTtRQUNoQ0MsWUFBWSxDQUFDRCxRQUFRQSxTQUFTeE8sZ0JBQWdCRyxRQUFRO1FBQ3REdU8sVUFBVUYsU0FBU3hPLGdCQUFnQkMsTUFBTTtRQUN6QzBPLFlBQVlILFNBQVN4TyxnQkFBZ0JFLFFBQVE7UUFDN0MwTyxTQUFTSixTQUFTeE8sZ0JBQWdCSyxHQUFHO1FBQ3JDd08sV0FBV0wsU0FBU3hPLGdCQUFnQkksU0FBUztJQUNqRDtBQUVBLE1BQU0wTyxpQkFBaUI7QUFDdkIsSUFBSUMsdUJBQXVCLENBQUNDLGlCQUFtQixDQUFDLENBQUNBLGtCQUM3QyxDQUFDLENBQUNBLGVBQWVuTyxRQUFRLElBQ3pCLENBQUMsQ0FBRSxZQUFZbU8sZUFBZW5PLFFBQVEsS0FDbENtTyxlQUFlbk8sUUFBUSxDQUFDM0QsV0FBVyxDQUFDVCxJQUFJLEtBQUtxUyxrQkFDNUM3UyxTQUFTK1MsZUFBZW5PLFFBQVEsS0FDN0JrQixPQUFPMkMsTUFBTSxDQUFDc0ssZUFBZW5PLFFBQVEsRUFBRW9PLElBQUksQ0FBQyxDQUFDQyxtQkFBcUJBLGlCQUFpQmhTLFdBQVcsQ0FBQ1QsSUFBSSxLQUFLcVMsZUFBZTtBQUVuSSxJQUFJSyxnQkFBZ0IsQ0FBQ3BDLFVBQVlBLFFBQVF0RyxLQUFLLElBQ3pDc0csQ0FBQUEsUUFBUW5NLFFBQVEsSUFDYm1NLFFBQVF2TSxHQUFHLElBQ1h1TSxRQUFReE0sR0FBRyxJQUNYd00sUUFBUXRNLFNBQVMsSUFDakJzTSxRQUFRck0sU0FBUyxJQUNqQnFNLFFBQVFwTSxPQUFPLElBQ2ZvTSxRQUFRbE0sUUFBUTtBQUV4QixJQUFJdU8sWUFBWSxDQUFDM1MsTUFBTXFILFFBQVF1TCxjQUFnQixDQUFDQSxlQUMzQ3ZMLENBQUFBLE9BQU9PLFFBQVEsSUFDWlAsT0FBT0csS0FBSyxDQUFDbkgsR0FBRyxDQUFDTCxTQUNqQjtXQUFJcUgsT0FBT0csS0FBSztLQUFDLENBQUM0RSxJQUFJLENBQUMsQ0FBQ3lHLFlBQWM3UyxLQUFLOFMsVUFBVSxDQUFDRCxjQUNsRCxTQUFTcFIsSUFBSSxDQUFDekIsS0FBS3VQLEtBQUssQ0FBQ3NELFVBQVUvUCxNQUFNLEdBQUU7QUFFdkQsTUFBTWlRLHdCQUF3QixDQUFDakQsUUFBUTVGLFFBQVFvSCxhQUFhMEI7SUFDeEQsS0FBSyxNQUFNelIsT0FBTytQLGVBQWVoTSxPQUFPb0YsSUFBSSxDQUFDb0YsUUFBUztRQUNsRCxNQUFNekcsUUFBUWpILElBQUkwTixRQUFRdk87UUFDMUIsSUFBSThILE9BQU87WUFDUCxNQUFNLEVBQUVFLEVBQUUsRUFBRSxHQUFHMEosY0FBYyxHQUFHNUo7WUFDaEMsSUFBSUUsSUFBSTtnQkFDSixJQUFJQSxHQUFHNEgsSUFBSSxJQUFJNUgsR0FBRzRILElBQUksQ0FBQyxFQUFFLElBQUlqSCxPQUFPWCxHQUFHNEgsSUFBSSxDQUFDLEVBQUUsRUFBRTVQLFFBQVEsQ0FBQ3lSLFlBQVk7b0JBQ2pFLE9BQU87Z0JBQ1gsT0FDSyxJQUFJekosR0FBR0osR0FBRyxJQUFJZSxPQUFPWCxHQUFHSixHQUFHLEVBQUVJLEdBQUd2SixJQUFJLEtBQUssQ0FBQ2dULFlBQVk7b0JBQ3ZELE9BQU87Z0JBQ1gsT0FDSztvQkFDRCxJQUFJRCxzQkFBc0JFLGNBQWMvSSxTQUFTO3dCQUM3QztvQkFDSjtnQkFDSjtZQUNKLE9BQ0ssSUFBSTFLLFNBQVN5VCxlQUFlO2dCQUM3QixJQUFJRixzQkFBc0JFLGNBQWMvSSxTQUFTO29CQUM3QztnQkFDSjtZQUNKO1FBQ0o7SUFDSjtJQUNBO0FBQ0o7QUFFQSxTQUFTZ0osa0JBQWtCck0sTUFBTSxFQUFFeUMsT0FBTyxFQUFFdEosSUFBSTtJQUM1QyxNQUFNaUosUUFBUTdHLElBQUl5RSxRQUFRN0c7SUFDMUIsSUFBSWlKLFNBQVN6SCxNQUFNeEIsT0FBTztRQUN0QixPQUFPO1lBQ0hpSjtZQUNBako7UUFDSjtJQUNKO0lBQ0EsTUFBTUksUUFBUUosS0FBS21DLEtBQUssQ0FBQztJQUN6QixNQUFPL0IsTUFBTTBDLE1BQU0sQ0FBRTtRQUNqQixNQUFNNkUsWUFBWXZILE1BQU0rUyxJQUFJLENBQUM7UUFDN0IsTUFBTTlKLFFBQVFqSCxJQUFJa0gsU0FBUzNCO1FBQzNCLE1BQU15TCxhQUFhaFIsSUFBSXlFLFFBQVFjO1FBQy9CLElBQUkwQixTQUFTLENBQUM1SixNQUFNQyxPQUFPLENBQUMySixVQUFVckosU0FBUzJILFdBQVc7WUFDdEQsT0FBTztnQkFBRTNIO1lBQUs7UUFDbEI7UUFDQSxJQUFJb1QsY0FBY0EsV0FBV2xVLElBQUksRUFBRTtZQUMvQixPQUFPO2dCQUNIYyxNQUFNMkg7Z0JBQ05zQixPQUFPbUs7WUFDWDtRQUNKO1FBQ0EsSUFBSUEsY0FBY0EsV0FBV0MsSUFBSSxJQUFJRCxXQUFXQyxJQUFJLENBQUNuVSxJQUFJLEVBQUU7WUFDdkQsT0FBTztnQkFDSGMsTUFBTSxDQUFDLEVBQUUySCxVQUFVLEtBQUssQ0FBQztnQkFDekJzQixPQUFPbUssV0FBV0MsSUFBSTtZQUMxQjtRQUNKO1FBQ0FqVCxNQUFNa1QsR0FBRztJQUNiO0lBQ0EsT0FBTztRQUNIdFQ7SUFDSjtBQUNKO0FBRUEsSUFBSXVULHdCQUF3QixDQUFDQyxlQUFlL04saUJBQWlCUSxpQkFBaUJkO0lBQzFFYyxnQkFBZ0J1TjtJQUNoQixNQUFNLEVBQUV4VCxJQUFJLEVBQUUsR0FBR2dGLFdBQVcsR0FBR3dPO0lBQy9CLE9BQVEvRSxjQUFjekosY0FDbEJNLE9BQU9vRixJQUFJLENBQUMxRixXQUFXbEMsTUFBTSxJQUFJd0MsT0FBT29GLElBQUksQ0FBQ2pGLGlCQUFpQjNDLE1BQU0sSUFDcEV3QyxPQUFPb0YsSUFBSSxDQUFDMUYsV0FBV3dOLElBQUksQ0FBQyxDQUFDalIsTUFBUWtFLGVBQWUsQ0FBQ2xFLElBQUksS0FDcEQsRUFBQzRELFVBQVU1QixnQkFBZ0JLLEdBQUc7QUFDM0M7QUFFQSxJQUFJNlAsd0JBQXdCLENBQUN6VCxNQUFNMFQsWUFBWTFOLFFBQVUsQ0FBQ2hHLFFBQ3RELENBQUMwVCxjQUNEMVQsU0FBUzBULGNBQ1RyRyxzQkFBc0JyTixNQUFNb00sSUFBSSxDQUFDLENBQUN1SCxjQUFnQkEsZUFDN0MzTixDQUFBQSxRQUNLMk4sZ0JBQWdCRCxhQUNoQkMsWUFBWWIsVUFBVSxDQUFDWSxlQUNyQkEsV0FBV1osVUFBVSxDQUFDYSxZQUFXO0FBRWpELElBQUlDLGlCQUFpQixDQUFDaEIsYUFBYTVKLFdBQVc2SyxhQUFhQyxnQkFBZ0IvQjtJQUN2RSxJQUFJQSxLQUFLSSxPQUFPLEVBQUU7UUFDZCxPQUFPO0lBQ1gsT0FDSyxJQUFJLENBQUMwQixlQUFlOUIsS0FBS0ssU0FBUyxFQUFFO1FBQ3JDLE9BQU8sQ0FBRXBKLENBQUFBLGFBQWE0SixXQUFVO0lBQ3BDLE9BQ0ssSUFBSWlCLGNBQWNDLGVBQWU3QixRQUFRLEdBQUdGLEtBQUtFLFFBQVEsRUFBRTtRQUM1RCxPQUFPLENBQUNXO0lBQ1osT0FDSyxJQUFJaUIsY0FBY0MsZUFBZTVCLFVBQVUsR0FBR0gsS0FBS0csVUFBVSxFQUFFO1FBQ2hFLE9BQU9VO0lBQ1g7SUFDQSxPQUFPO0FBQ1g7QUFFQSxJQUFJbUIsa0JBQWtCLENBQUM1SyxLQUFLbkosT0FBUyxDQUFDNkIsUUFBUU8sSUFBSStHLEtBQUtuSixPQUFPOEMsTUFBTSxJQUFJMk0sTUFBTXRHLEtBQUtuSjtBQUVuRixJQUFJZ1UsNEJBQTRCLENBQUNuTixRQUFRb0MsT0FBT2pKO0lBQzVDLE1BQU1pVSxtQkFBbUI1RyxzQkFBc0JqTCxJQUFJeUUsUUFBUTdHO0lBQzNEMkMsSUFBSXNSLGtCQUFrQixRQUFRaEwsS0FBSyxDQUFDakosS0FBSztJQUN6QzJDLElBQUlrRSxRQUFRN0csTUFBTWlVO0lBQ2xCLE9BQU9wTjtBQUNYO0FBRUEsSUFBSXFOLFlBQVksQ0FBQzlVLFNBQVUrSCxTQUFTL0g7QUFFcEMsU0FBUytVLGlCQUFpQjNSLE1BQU0sRUFBRTJHLEdBQUcsRUFBRWpLLE9BQU8sVUFBVTtJQUNwRCxJQUFJZ1YsVUFBVTFSLFdBQ1QvQyxNQUFNQyxPQUFPLENBQUM4QyxXQUFXQSxPQUFPNFIsS0FBSyxDQUFDRixjQUN0Q3hSLFVBQVVGLFdBQVcsQ0FBQ0EsUUFBUztRQUNoQyxPQUFPO1lBQ0h0RDtZQUNBeUssU0FBU3VLLFVBQVUxUixVQUFVQSxTQUFTO1lBQ3RDMkc7UUFDSjtJQUNKO0FBQ0o7QUFFQSxJQUFJa0wscUJBQXFCLENBQUNDLGlCQUFtQjlVLFNBQVM4VSxtQkFBbUIsQ0FBQzdDLFFBQVE2QyxrQkFDNUVBLGlCQUNBO1FBQ0VsVixPQUFPa1Y7UUFDUDNLLFNBQVM7SUFDYjtBQUVKLElBQUk0SyxnQkFBZ0IsT0FBT2xMLE9BQU9tTCxvQkFBb0JsTixZQUFZNkYsMEJBQTBCcUUsMkJBQTJCaUQ7SUFDbkgsTUFBTSxFQUFFdEwsR0FBRyxFQUFFZ0ksSUFBSSxFQUFFaE4sUUFBUSxFQUFFSCxTQUFTLEVBQUVDLFNBQVMsRUFBRUYsR0FBRyxFQUFFRCxHQUFHLEVBQUVJLE9BQU8sRUFBRUUsUUFBUSxFQUFFcEUsSUFBSSxFQUFFMFEsYUFBYSxFQUFFMUcsS0FBSyxFQUFHLEdBQUdYLE1BQU1FLEVBQUU7SUFDeEgsTUFBTW1MLGFBQWF0UyxJQUFJa0YsWUFBWXRIO0lBQ25DLElBQUksQ0FBQ2dLLFNBQVN3SyxtQkFBbUJuVSxHQUFHLENBQUNMLE9BQU87UUFDeEMsT0FBTyxDQUFDO0lBQ1o7SUFDQSxNQUFNMlUsV0FBV3hELE9BQU9BLElBQUksQ0FBQyxFQUFFLEdBQUdoSTtJQUNsQyxNQUFNTyxvQkFBb0IsQ0FBQ0M7UUFDdkIsSUFBSTZILDZCQUE2Qm1ELFNBQVMvSyxjQUFjLEVBQUU7WUFDdEQrSyxTQUFTakwsaUJBQWlCLENBQUNoSCxVQUFVaUgsV0FBVyxLQUFLQSxXQUFXO1lBQ2hFZ0wsU0FBUy9LLGNBQWM7UUFDM0I7SUFDSjtJQUNBLE1BQU1YLFFBQVEsQ0FBQztJQUNmLE1BQU0yTCxVQUFVM0YsYUFBYTlGO0lBQzdCLE1BQU0wTCxhQUFhN1YsZ0JBQWdCbUs7SUFDbkMsTUFBTStGLG9CQUFvQjBGLFdBQVdDO0lBQ3JDLE1BQU1DLFVBQVUsQ0FBRXBFLGlCQUFpQmhDLFlBQVl2RixJQUFHLEtBQzlDekgsWUFBWXlILElBQUkvSixLQUFLLEtBQ3JCc0MsWUFBWWdULGVBQ1g5RixjQUFjekYsUUFBUUEsSUFBSS9KLEtBQUssS0FBSyxNQUNyQ3NWLGVBQWUsTUFDZGpWLE1BQU1DLE9BQU8sQ0FBQ2dWLGVBQWUsQ0FBQ0EsV0FBVzVSLE1BQU07SUFDcEQsTUFBTWlTLG9CQUFvQjdILGFBQWE4SCxJQUFJLENBQUMsTUFBTWhWLE1BQU1tTiwwQkFBMEJsRTtJQUNsRixNQUFNZ00sbUJBQW1CLENBQUNDLFdBQVdDLGtCQUFrQkMsa0JBQWtCQyxVQUFVeFIsdUJBQXVCRyxTQUFTLEVBQUVzUixVQUFVelIsdUJBQXVCSSxTQUFTO1FBQzNKLE1BQU0wRixVQUFVdUwsWUFBWUMsbUJBQW1CQztRQUMvQ25NLEtBQUssQ0FBQ2pKLEtBQUssR0FBRztZQUNWZCxNQUFNZ1csWUFBWUcsVUFBVUM7WUFDNUIzTDtZQUNBUjtZQUNBLEdBQUc0TCxrQkFBa0JHLFlBQVlHLFVBQVVDLFNBQVMzTCxRQUFRO1FBQ2hFO0lBQ0o7SUFDQSxJQUFJOEssZUFDRSxDQUFDaFYsTUFBTUMsT0FBTyxDQUFDZ1YsZUFBZSxDQUFDQSxXQUFXNVIsTUFBTSxHQUNoRHFCLFlBQ0csRUFBRStLLHFCQUFzQjRGLENBQUFBLFdBQVd4VixrQkFBa0JvVixXQUFVLEtBQzNEaFMsVUFBVWdTLGVBQWUsQ0FBQ0EsY0FDMUJHLGNBQWMsQ0FBQ3hFLGlCQUFpQmMsTUFBTXZLLE9BQU8sSUFDN0NnTyxXQUFXLENBQUM3RCxjQUFjSSxNQUFNdkssT0FBTyxHQUFJO1FBQ3BELE1BQU0sRUFBRXhILE9BQUFBLE1BQUssRUFBRXVLLE9BQU8sRUFBRSxHQUFHdUssVUFBVS9QLFlBQy9CO1lBQUUvRSxPQUFPLENBQUMsQ0FBQytFO1lBQVV3RixTQUFTeEY7UUFBUyxJQUN2Q2tRLG1CQUFtQmxRO1FBQ3pCLElBQUkvRSxRQUFPO1lBQ1A2SixLQUFLLENBQUNqSixLQUFLLEdBQUc7Z0JBQ1ZkLE1BQU0yRSx1QkFBdUJNLFFBQVE7Z0JBQ3JDd0Y7Z0JBQ0FSLEtBQUt3TDtnQkFDTCxHQUFHSSxrQkFBa0JsUix1QkFBdUJNLFFBQVEsRUFBRXdGLFFBQVE7WUFDbEU7WUFDQSxJQUFJLENBQUN3RCwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCQztnQkFDbEIsT0FBT1Y7WUFDWDtRQUNKO0lBQ0o7SUFDQSxJQUFJLENBQUM2TCxXQUFZLEVBQUN4VixrQkFBa0J5RSxRQUFRLENBQUN6RSxrQkFBa0J3RSxJQUFHLEdBQUk7UUFDbEUsSUFBSW9SO1FBQ0osSUFBSUs7UUFDSixNQUFNQyxZQUFZbkIsbUJBQW1CdlE7UUFDckMsTUFBTTJSLFlBQVlwQixtQkFBbUJ0UTtRQUNyQyxJQUFJLENBQUN6RSxrQkFBa0JvVixlQUFlLENBQUN4UixNQUFNd1IsYUFBYTtZQUN0RCxNQUFNZ0IsY0FBY3ZNLElBQUl1SCxhQUFhLElBQ2hDZ0UsQ0FBQUEsYUFBYSxDQUFDQSxhQUFhQSxVQUFTO1lBQ3pDLElBQUksQ0FBQ3BWLGtCQUFrQmtXLFVBQVVwVyxLQUFLLEdBQUc7Z0JBQ3JDOFYsWUFBWVEsY0FBY0YsVUFBVXBXLEtBQUs7WUFDN0M7WUFDQSxJQUFJLENBQUNFLGtCQUFrQm1XLFVBQVVyVyxLQUFLLEdBQUc7Z0JBQ3JDbVcsWUFBWUcsY0FBY0QsVUFBVXJXLEtBQUs7WUFDN0M7UUFDSixPQUNLO1lBQ0QsTUFBTXVXLFlBQVl4TSxJQUFJd0gsV0FBVyxJQUFJLElBQUl0UixLQUFLcVY7WUFDOUMsTUFBTWtCLG9CQUFvQixDQUFDQyxPQUFTLElBQUl4VyxLQUFLLElBQUlBLE9BQU95VyxZQUFZLEtBQUssTUFBTUQ7WUFDL0UsTUFBTUUsU0FBUzVNLElBQUlqSyxJQUFJLElBQUk7WUFDM0IsTUFBTThXLFNBQVM3TSxJQUFJakssSUFBSSxJQUFJO1lBQzNCLElBQUlpSSxTQUFTcU8sVUFBVXBXLEtBQUssS0FBS3NWLFlBQVk7Z0JBQ3pDUSxZQUFZYSxTQUNOSCxrQkFBa0JsQixjQUFja0Isa0JBQWtCSixVQUFVcFcsS0FBSyxJQUNqRTRXLFNBQ0l0QixhQUFhYyxVQUFVcFcsS0FBSyxHQUM1QnVXLFlBQVksSUFBSXRXLEtBQUttVyxVQUFVcFcsS0FBSztZQUNsRDtZQUNBLElBQUkrSCxTQUFTc08sVUFBVXJXLEtBQUssS0FBS3NWLFlBQVk7Z0JBQ3pDYSxZQUFZUSxTQUNOSCxrQkFBa0JsQixjQUFja0Isa0JBQWtCSCxVQUFVclcsS0FBSyxJQUNqRTRXLFNBQ0l0QixhQUFhZSxVQUFVclcsS0FBSyxHQUM1QnVXLFlBQVksSUFBSXRXLEtBQUtvVyxVQUFVclcsS0FBSztZQUNsRDtRQUNKO1FBQ0EsSUFBSThWLGFBQWFLLFdBQVc7WUFDeEJOLGlCQUFpQixDQUFDLENBQUNDLFdBQVdNLFVBQVU3TCxPQUFPLEVBQUU4TCxVQUFVOUwsT0FBTyxFQUFFOUYsdUJBQXVCQyxHQUFHLEVBQUVELHVCQUF1QkUsR0FBRztZQUMxSCxJQUFJLENBQUNvSiwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCVCxLQUFLLENBQUNqSixLQUFLLENBQUMySixPQUFPO2dCQUNyQyxPQUFPVjtZQUNYO1FBQ0o7SUFDSjtJQUNBLElBQUksQ0FBQ2pGLGFBQWFDLFNBQVEsS0FDdEIsQ0FBQzZRLFdBQ0EzTixDQUFBQSxTQUFTdU4sZUFBZ0JELGdCQUFnQmhWLE1BQU1DLE9BQU8sQ0FBQ2dWLFdBQVcsR0FBSTtRQUN2RSxNQUFNdUIsa0JBQWtCNUIsbUJBQW1CclE7UUFDM0MsTUFBTWtTLGtCQUFrQjdCLG1CQUFtQnBRO1FBQzNDLE1BQU1pUixZQUFZLENBQUM1VixrQkFBa0IyVyxnQkFBZ0I3VyxLQUFLLEtBQ3REc1YsV0FBVzVSLE1BQU0sR0FBRyxDQUFDbVQsZ0JBQWdCN1csS0FBSztRQUM5QyxNQUFNbVcsWUFBWSxDQUFDalcsa0JBQWtCNFcsZ0JBQWdCOVcsS0FBSyxLQUN0RHNWLFdBQVc1UixNQUFNLEdBQUcsQ0FBQ29ULGdCQUFnQjlXLEtBQUs7UUFDOUMsSUFBSThWLGFBQWFLLFdBQVc7WUFDeEJOLGlCQUFpQkMsV0FBV2UsZ0JBQWdCdE0sT0FBTyxFQUFFdU0sZ0JBQWdCdk0sT0FBTztZQUM1RSxJQUFJLENBQUN3RCwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCVCxLQUFLLENBQUNqSixLQUFLLENBQUMySixPQUFPO2dCQUNyQyxPQUFPVjtZQUNYO1FBQ0o7SUFDSjtJQUNBLElBQUkvRSxXQUFXLENBQUM0USxXQUFXM04sU0FBU3VOLGFBQWE7UUFDN0MsTUFBTSxFQUFFdFYsT0FBTytXLFlBQVksRUFBRXhNLE9BQU8sRUFBRSxHQUFHMEssbUJBQW1CblE7UUFDNUQsSUFBSXVOLFFBQVEwRSxpQkFBaUIsQ0FBQ3pCLFdBQVcwQixLQUFLLENBQUNELGVBQWU7WUFDMURsTixLQUFLLENBQUNqSixLQUFLLEdBQUc7Z0JBQ1ZkLE1BQU0yRSx1QkFBdUJLLE9BQU87Z0JBQ3BDeUY7Z0JBQ0FSO2dCQUNBLEdBQUc0TCxrQkFBa0JsUix1QkFBdUJLLE9BQU8sRUFBRXlGLFFBQVE7WUFDakU7WUFDQSxJQUFJLENBQUN3RCwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCQztnQkFDbEIsT0FBT1Y7WUFDWDtRQUNKO0lBQ0o7SUFDQSxJQUFJN0UsVUFBVTtRQUNWLElBQUl1SyxXQUFXdkssV0FBVztZQUN0QixNQUFNNUIsU0FBUyxNQUFNNEIsU0FBU3NRLFlBQVlwTjtZQUMxQyxNQUFNK08sZ0JBQWdCbEMsaUJBQWlCM1IsUUFBUW1TO1lBQy9DLElBQUkwQixlQUFlO2dCQUNmcE4sS0FBSyxDQUFDakosS0FBSyxHQUFHO29CQUNWLEdBQUdxVyxhQUFhO29CQUNoQixHQUFHdEIsa0JBQWtCbFIsdUJBQXVCTyxRQUFRLEVBQUVpUyxjQUFjMU0sT0FBTyxDQUFDO2dCQUNoRjtnQkFDQSxJQUFJLENBQUN3RCwwQkFBMEI7b0JBQzNCekQsa0JBQWtCMk0sY0FBYzFNLE9BQU87b0JBQ3ZDLE9BQU9WO2dCQUNYO1lBQ0o7UUFDSixPQUNLLElBQUl6SixTQUFTNEUsV0FBVztZQUN6QixJQUFJa1MsbUJBQW1CLENBQUM7WUFDeEIsSUFBSyxNQUFNL1UsT0FBTzZDLFNBQVU7Z0JBQ3hCLElBQUksQ0FBQ3FLLGNBQWM2SCxxQkFBcUIsQ0FBQ25KLDBCQUEwQjtvQkFDL0Q7Z0JBQ0o7Z0JBQ0EsTUFBTWtKLGdCQUFnQmxDLGlCQUFpQixNQUFNL1AsUUFBUSxDQUFDN0MsSUFBSSxDQUFDbVQsWUFBWXBOLGFBQWFxTixVQUFVcFQ7Z0JBQzlGLElBQUk4VSxlQUFlO29CQUNmQyxtQkFBbUI7d0JBQ2YsR0FBR0QsYUFBYTt3QkFDaEIsR0FBR3RCLGtCQUFrQnhULEtBQUs4VSxjQUFjMU0sT0FBTyxDQUFDO29CQUNwRDtvQkFDQUQsa0JBQWtCMk0sY0FBYzFNLE9BQU87b0JBQ3ZDLElBQUl3RCwwQkFBMEI7d0JBQzFCbEUsS0FBSyxDQUFDakosS0FBSyxHQUFHc1c7b0JBQ2xCO2dCQUNKO1lBQ0o7WUFDQSxJQUFJLENBQUM3SCxjQUFjNkgsbUJBQW1CO2dCQUNsQ3JOLEtBQUssQ0FBQ2pKLEtBQUssR0FBRztvQkFDVm1KLEtBQUt3TDtvQkFDTCxHQUFHMkIsZ0JBQWdCO2dCQUN2QjtnQkFDQSxJQUFJLENBQUNuSiwwQkFBMEI7b0JBQzNCLE9BQU9sRTtnQkFDWDtZQUNKO1FBQ0o7SUFDSjtJQUNBUyxrQkFBa0I7SUFDbEIsT0FBT1Q7QUFDWDtBQUVBLE1BQU1zTixpQkFBaUI7SUFDbkJ4RSxNQUFNeE8sZ0JBQWdCRyxRQUFRO0lBQzlCb1EsZ0JBQWdCdlEsZ0JBQWdCRSxRQUFRO0lBQ3hDK1Msa0JBQWtCO0FBQ3RCO0FBQ0EsU0FBU0Msa0JBQWtCOVIsUUFBUSxDQUFDLENBQUM7SUFDakMsSUFBSW1GLFdBQVc7UUFDWCxHQUFHeU0sY0FBYztRQUNqQixHQUFHNVIsS0FBSztJQUNaO0lBQ0EsSUFBSXdCLGFBQWE7UUFDYnVRLGFBQWE7UUFDYnBRLFNBQVM7UUFDVHFRLFNBQVM7UUFDVHBRLFdBQVdvSSxXQUFXN0UsU0FBUzFFLGFBQWE7UUFDNUN1QixjQUFjO1FBQ2RrTixhQUFhO1FBQ2IrQyxjQUFjO1FBQ2Q5SixvQkFBb0I7UUFDcEJsRyxTQUFTO1FBQ1RILGVBQWUsQ0FBQztRQUNoQkQsYUFBYSxDQUFDO1FBQ2RFLGtCQUFrQixDQUFDO1FBQ25CRyxRQUFRaUQsU0FBU2pELE1BQU0sSUFBSSxDQUFDO1FBQzVCZCxVQUFVK0QsU0FBUy9ELFFBQVEsSUFBSTtJQUNuQztJQUNBLE1BQU11RCxVQUFVLENBQUM7SUFDakIsSUFBSWpFLGlCQUFpQjdGLFNBQVNzSyxTQUFTMUUsYUFBYSxLQUFLNUYsU0FBU3NLLFNBQVM3QixNQUFNLElBQzNFakgsWUFBWThJLFNBQVMxRSxhQUFhLElBQUkwRSxTQUFTN0IsTUFBTSxLQUFLLENBQUMsSUFDM0QsQ0FBQztJQUNQLElBQUlDLGNBQWM0QixTQUFTekIsZ0JBQWdCLEdBQ3JDLENBQUMsSUFDRHJILFlBQVlxRTtJQUNsQixJQUFJNEUsU0FBUztRQUNUQyxRQUFRO1FBQ1JGLE9BQU87UUFDUHhDLE9BQU87SUFDWDtJQUNBLElBQUlILFNBQVM7UUFDVDJDLE9BQU8sSUFBSTNJO1FBQ1gwRSxVQUFVLElBQUkxRTtRQUNkd1YsU0FBUyxJQUFJeFY7UUFDYmtILE9BQU8sSUFBSWxIO1FBQ1htRyxPQUFPLElBQUluRztJQUNmO0lBQ0EsSUFBSXlWO0lBQ0osSUFBSUMsUUFBUTtJQUNaLE1BQU10UixrQkFBa0I7UUFDcEJhLFNBQVM7UUFDVEUsYUFBYTtRQUNiRSxrQkFBa0I7UUFDbEJELGVBQWU7UUFDZkUsY0FBYztRQUNkQyxTQUFTO1FBQ1RDLFFBQVE7SUFDWjtJQUNBLElBQUltUSwyQkFBMkI7UUFDM0IsR0FBR3ZSLGVBQWU7SUFDdEI7SUFDQSxNQUFNa0gsWUFBWTtRQUNkcEUsT0FBTytFO1FBQ1BWLE9BQU9VO0lBQ1g7SUFDQSxNQUFNMkosbUNBQW1Dbk4sU0FBU3lILFlBQVksS0FBS2hPLGdCQUFnQkssR0FBRztJQUN0RixNQUFNc1QsV0FBVyxDQUFDbFEsV0FBYSxDQUFDbVE7WUFDNUJDLGFBQWFMO1lBQ2JBLFFBQVFNLFdBQVdyUSxVQUFVbVE7UUFDakM7SUFDQSxNQUFNbFEsWUFBWSxPQUFPcVE7UUFDckIsSUFBSSxDQUFDeE4sU0FBUy9ELFFBQVEsSUFDakJOLENBQUFBLGdCQUFnQm1CLE9BQU8sSUFDcEJvUSx5QkFBeUJwUSxPQUFPLElBQ2hDMFEsaUJBQWdCLEdBQUk7WUFDeEIsTUFBTTFRLFVBQVVrRCxTQUFTeU4sUUFBUSxHQUMzQjlJLGNBQWMsQ0FBQyxNQUFNK0ksWUFBVyxFQUFHM1EsTUFBTSxJQUN6QyxNQUFNNFEseUJBQXlCbk8sU0FBUztZQUM5QyxJQUFJMUMsWUFBWVQsV0FBV1MsT0FBTyxFQUFFO2dCQUNoQytGLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUNqQmpHO2dCQUNKO1lBQ0o7UUFDSjtJQUNKO0lBQ0EsTUFBTThRLHNCQUFzQixDQUFDdFgsT0FBT3VHO1FBQ2hDLElBQUksQ0FBQ21ELFNBQVMvRCxRQUFRLElBQ2pCTixDQUFBQSxnQkFBZ0JrQixZQUFZLElBQ3pCbEIsZ0JBQWdCaUIsZ0JBQWdCLElBQ2hDc1EseUJBQXlCclEsWUFBWSxJQUNyQ3FRLHlCQUF5QnRRLGdCQUFnQixHQUFHO1lBQy9DdEcsQ0FBQUEsU0FBU1gsTUFBTWtZLElBQUksQ0FBQ3RRLE9BQU8yQyxLQUFLLEdBQUc0TixPQUFPLENBQUMsQ0FBQzVYO2dCQUN6QyxJQUFJQSxNQUFNO29CQUNOMkcsZUFDTWhFLElBQUl3RCxXQUFXTyxnQkFBZ0IsRUFBRTFHLE1BQU0yRyxnQkFDdkM4SSxNQUFNdEosV0FBV08sZ0JBQWdCLEVBQUUxRztnQkFDN0M7WUFDSjtZQUNBMk0sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCbkcsa0JBQWtCUCxXQUFXTyxnQkFBZ0I7Z0JBQzdDQyxjQUFjLENBQUM4SCxjQUFjdEksV0FBV08sZ0JBQWdCO1lBQzVEO1FBQ0o7SUFDSjtJQUNBLE1BQU1tUixpQkFBaUIsQ0FBQzdYLE1BQU1pSSxTQUFTLEVBQUUsRUFBRWdELFFBQVE2TSxNQUFNQyxrQkFBa0IsSUFBSSxFQUFFQyw2QkFBNkIsSUFBSTtRQUM5RyxJQUFJRixRQUFRN00sVUFBVSxDQUFDbkIsU0FBUy9ELFFBQVEsRUFBRTtZQUN0Q2tFLE9BQU9DLE1BQU0sR0FBRztZQUNoQixJQUFJOE4sOEJBQThCdlksTUFBTUMsT0FBTyxDQUFDMEMsSUFBSWtILFNBQVN0SixRQUFRO2dCQUNqRSxNQUFNaVksY0FBY2hOLE9BQU83SSxJQUFJa0gsU0FBU3RKLE9BQU84WCxLQUFLSSxJQUFJLEVBQUVKLEtBQUtLLElBQUk7Z0JBQ25FSixtQkFBbUJwVixJQUFJMkcsU0FBU3RKLE1BQU1pWTtZQUMxQztZQUNBLElBQUlELDhCQUNBdlksTUFBTUMsT0FBTyxDQUFDMEMsSUFBSStELFdBQVdVLE1BQU0sRUFBRTdHLFFBQVE7Z0JBQzdDLE1BQU02RyxTQUFTb0UsT0FBTzdJLElBQUkrRCxXQUFXVSxNQUFNLEVBQUU3RyxPQUFPOFgsS0FBS0ksSUFBSSxFQUFFSixLQUFLSyxJQUFJO2dCQUN4RUosbUJBQW1CcFYsSUFBSXdELFdBQVdVLE1BQU0sRUFBRTdHLE1BQU02RztnQkFDaERrTixnQkFBZ0I1TixXQUFXVSxNQUFNLEVBQUU3RztZQUN2QztZQUNBLElBQUksQ0FBQ3lGLGdCQUFnQmdCLGFBQWEsSUFDOUJ1USx5QkFBeUJ2USxhQUFhLEtBQ3RDdVIsOEJBQ0F2WSxNQUFNQyxPQUFPLENBQUMwQyxJQUFJK0QsV0FBV00sYUFBYSxFQUFFekcsUUFBUTtnQkFDcEQsTUFBTXlHLGdCQUFnQndFLE9BQU83SSxJQUFJK0QsV0FBV00sYUFBYSxFQUFFekcsT0FBTzhYLEtBQUtJLElBQUksRUFBRUosS0FBS0ssSUFBSTtnQkFDdEZKLG1CQUFtQnBWLElBQUl3RCxXQUFXTSxhQUFhLEVBQUV6RyxNQUFNeUc7WUFDM0Q7WUFDQSxJQUFJaEIsZ0JBQWdCZSxXQUFXLElBQUl3USx5QkFBeUJ4USxXQUFXLEVBQUU7Z0JBQ3JFTCxXQUFXSyxXQUFXLEdBQUcwSixlQUFlN0ssZ0JBQWdCNkM7WUFDNUQ7WUFDQXlFLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjdNO2dCQUNBc0csU0FBUzhSLFVBQVVwWSxNQUFNaUk7Z0JBQ3pCekIsYUFBYUwsV0FBV0ssV0FBVztnQkFDbkNLLFFBQVFWLFdBQVdVLE1BQU07Z0JBQ3pCRCxTQUFTVCxXQUFXUyxPQUFPO1lBQy9CO1FBQ0osT0FDSztZQUNEakUsSUFBSXVGLGFBQWFsSSxNQUFNaUk7UUFDM0I7SUFDSjtJQUNBLE1BQU1vUSxlQUFlLENBQUNyWSxNQUFNaUo7UUFDeEJ0RyxJQUFJd0QsV0FBV1UsTUFBTSxFQUFFN0csTUFBTWlKO1FBQzdCMEQsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakJoRyxRQUFRVixXQUFXVSxNQUFNO1FBQzdCO0lBQ0o7SUFDQSxNQUFNeVIsYUFBYSxDQUFDelI7UUFDaEJWLFdBQVdVLE1BQU0sR0FBR0E7UUFDcEI4RixVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQmhHLFFBQVFWLFdBQVdVLE1BQU07WUFDekJELFNBQVM7UUFDYjtJQUNKO0lBQ0EsTUFBTTJSLHNCQUFzQixDQUFDdlksTUFBTXdZLHNCQUFzQnBaLFFBQU8rSjtRQUM1RCxNQUFNRSxRQUFRakgsSUFBSWtILFNBQVN0SjtRQUMzQixJQUFJcUosT0FBTztZQUNQLE1BQU05RyxlQUFlSCxJQUFJOEYsYUFBYWxJLE1BQU0wQixZQUFZdEMsVUFBU2dELElBQUlpRCxnQkFBZ0JyRixRQUFRWjtZQUM3RnNDLFlBQVlhLGlCQUNQNEcsT0FBT0EsSUFBSXNQLGNBQWMsSUFDMUJELHVCQUNFN1YsSUFBSXVGLGFBQWFsSSxNQUFNd1ksdUJBQXVCalcsZUFBZTBPLGNBQWM1SCxNQUFNRSxFQUFFLEtBQ25GbVAsY0FBYzFZLE1BQU11QztZQUMxQjBILE9BQU9ELEtBQUssSUFBSS9DO1FBQ3BCO0lBQ0o7SUFDQSxNQUFNMFIsc0JBQXNCLENBQUMzWSxNQUFNNFksWUFBWWhHLGFBQWFpRyxhQUFhQztRQUNyRSxJQUFJQyxvQkFBb0I7UUFDeEIsSUFBSUMsa0JBQWtCO1FBQ3RCLE1BQU12TyxTQUFTO1lBQ1h6SztRQUNKO1FBQ0EsSUFBSSxDQUFDOEosU0FBUy9ELFFBQVEsRUFBRTtZQUNwQixJQUFJLENBQUM2TSxlQUFlaUcsYUFBYTtnQkFDN0IsSUFBSXBULGdCQUFnQmEsT0FBTyxJQUFJMFEseUJBQXlCMVEsT0FBTyxFQUFFO29CQUM3RDBTLGtCQUFrQjdTLFdBQVdHLE9BQU87b0JBQ3BDSCxXQUFXRyxPQUFPLEdBQUdtRSxPQUFPbkUsT0FBTyxHQUFHOFI7b0JBQ3RDVyxvQkFBb0JDLG9CQUFvQnZPLE9BQU9uRSxPQUFPO2dCQUMxRDtnQkFDQSxNQUFNMlMseUJBQXlCbEwsVUFBVTNMLElBQUlpRCxnQkFBZ0JyRixPQUFPNFk7Z0JBQ3BFSSxrQkFBa0IsQ0FBQyxDQUFDNVcsSUFBSStELFdBQVdLLFdBQVcsRUFBRXhHO2dCQUNoRGlaLHlCQUNNeEosTUFBTXRKLFdBQVdLLFdBQVcsRUFBRXhHLFFBQzlCMkMsSUFBSXdELFdBQVdLLFdBQVcsRUFBRXhHLE1BQU07Z0JBQ3hDeUssT0FBT2pFLFdBQVcsR0FBR0wsV0FBV0ssV0FBVztnQkFDM0N1UyxvQkFDSUEscUJBQ0ssQ0FBQ3RULGdCQUFnQmUsV0FBVyxJQUN6QndRLHlCQUF5QnhRLFdBQVcsS0FDcEN3UyxvQkFBb0IsQ0FBQ0M7WUFDckM7WUFDQSxJQUFJckcsYUFBYTtnQkFDYixNQUFNc0cseUJBQXlCOVcsSUFBSStELFdBQVdNLGFBQWEsRUFBRXpHO2dCQUM3RCxJQUFJLENBQUNrWix3QkFBd0I7b0JBQ3pCdlcsSUFBSXdELFdBQVdNLGFBQWEsRUFBRXpHLE1BQU00UztvQkFDcENuSSxPQUFPaEUsYUFBYSxHQUFHTixXQUFXTSxhQUFhO29CQUMvQ3NTLG9CQUNJQSxxQkFDSyxDQUFDdFQsZ0JBQWdCZ0IsYUFBYSxJQUMzQnVRLHlCQUF5QnZRLGFBQWEsS0FDdEN5UywyQkFBMkJ0RztnQkFDM0M7WUFDSjtZQUNBbUcscUJBQXFCRCxnQkFBZ0JuTSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQ3BDO1FBQzlEO1FBQ0EsT0FBT3NPLG9CQUFvQnRPLFNBQVMsQ0FBQztJQUN6QztJQUNBLE1BQU0wTyxzQkFBc0IsQ0FBQ25aLE1BQU00RyxTQUFTcUMsT0FBT0w7UUFDL0MsTUFBTXdRLHFCQUFxQmhYLElBQUkrRCxXQUFXVSxNQUFNLEVBQUU3RztRQUNsRCxNQUFNc1gsb0JBQW9CLENBQUM3UixnQkFBZ0JtQixPQUFPLElBQUlvUSx5QkFBeUJwUSxPQUFPLEtBQ2xGbEUsVUFBVWtFLFlBQ1ZULFdBQVdTLE9BQU8sS0FBS0E7UUFDM0IsSUFBSWtELFNBQVN1UCxVQUFVLElBQUlwUSxPQUFPO1lBQzlCNk4scUJBQXFCSSxTQUFTLElBQU1tQixhQUFhclksTUFBTWlKO1lBQ3ZENk4sbUJBQW1CaE4sU0FBU3VQLFVBQVU7UUFDMUMsT0FDSztZQUNEakMsYUFBYUw7WUFDYkQscUJBQXFCO1lBQ3JCN04sUUFDTXRHLElBQUl3RCxXQUFXVSxNQUFNLEVBQUU3RyxNQUFNaUosU0FDN0J3RyxNQUFNdEosV0FBV1UsTUFBTSxFQUFFN0c7UUFDbkM7UUFDQSxJQUFJLENBQUNpSixRQUFRLENBQUM4RSxVQUFVcUwsb0JBQW9CblEsU0FBU21RLGtCQUFpQixLQUNsRSxDQUFDM0ssY0FBYzdGLGVBQ2YwTyxtQkFBbUI7WUFDbkIsTUFBTWdDLG1CQUFtQjtnQkFDckIsR0FBRzFRLFVBQVU7Z0JBQ2IsR0FBSTBPLHFCQUFxQjVVLFVBQVVrRSxXQUFXO29CQUFFQTtnQkFBUSxJQUFJLENBQUMsQ0FBQztnQkFDOURDLFFBQVFWLFdBQVdVLE1BQU07Z0JBQ3pCN0c7WUFDSjtZQUNBbUcsYUFBYTtnQkFDVCxHQUFHQSxVQUFVO2dCQUNiLEdBQUdtVCxnQkFBZ0I7WUFDdkI7WUFDQTNNLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDeU07UUFDekI7SUFDSjtJQUNBLE1BQU05QixhQUFhLE9BQU94WDtRQUN0QjBYLG9CQUFvQjFYLE1BQU07UUFDMUIsTUFBTXdDLFNBQVMsTUFBTXNILFNBQVN5TixRQUFRLENBQUNyUCxhQUFhNEIsU0FBU3lQLE9BQU8sRUFBRWxJLG1CQUFtQnJSLFFBQVFxSCxPQUFPMkMsS0FBSyxFQUFFVixTQUFTUSxTQUFTeUgsWUFBWSxFQUFFekgsU0FBUzBILHlCQUF5QjtRQUNqTGtHLG9CQUFvQjFYO1FBQ3BCLE9BQU93QztJQUNYO0lBQ0EsTUFBTWdYLDhCQUE4QixPQUFPcFo7UUFDdkMsTUFBTSxFQUFFeUcsTUFBTSxFQUFFLEdBQUcsTUFBTTJRLFdBQVdwWDtRQUNwQyxJQUFJQSxPQUFPO1lBQ1AsS0FBSyxNQUFNSixRQUFRSSxNQUFPO2dCQUN0QixNQUFNNkksUUFBUTdHLElBQUl5RSxRQUFRN0c7Z0JBQzFCaUosUUFDTXRHLElBQUl3RCxXQUFXVSxNQUFNLEVBQUU3RyxNQUFNaUosU0FDN0J3RyxNQUFNdEosV0FBV1UsTUFBTSxFQUFFN0c7WUFDbkM7UUFDSixPQUNLO1lBQ0RtRyxXQUFXVSxNQUFNLEdBQUdBO1FBQ3hCO1FBQ0EsT0FBT0E7SUFDWDtJQUNBLE1BQU00USwyQkFBMkIsT0FBTzNILFFBQVEySixzQkFBc0JGLFVBQVU7UUFDNUVHLE9BQU87SUFDWCxDQUFDO1FBQ0csSUFBSyxNQUFNMVosUUFBUThQLE9BQVE7WUFDdkIsTUFBTXpHLFFBQVF5RyxNQUFNLENBQUM5UCxLQUFLO1lBQzFCLElBQUlxSixPQUFPO2dCQUNQLE1BQU0sRUFBRUUsRUFBRSxFQUFFLEdBQUdxUCxZQUFZLEdBQUd2UDtnQkFDOUIsSUFBSUUsSUFBSTtvQkFDSixNQUFNb1EsbUJBQW1CdFMsT0FBT2tCLEtBQUssQ0FBQ2xJLEdBQUcsQ0FBQ2tKLEdBQUd2SixJQUFJO29CQUNqRCxNQUFNNFosb0JBQW9CdlEsTUFBTUUsRUFBRSxJQUFJK0kscUJBQXFCakosTUFBTUUsRUFBRTtvQkFDbkUsSUFBSXFRLHFCQUFxQm5VLGdCQUFnQmlCLGdCQUFnQixFQUFFO3dCQUN2RGdSLG9CQUFvQjs0QkFBQzFYO3lCQUFLLEVBQUU7b0JBQ2hDO29CQUNBLE1BQU02WixhQUFhLE1BQU10RixjQUFjbEwsT0FBT2hDLE9BQU90QixRQUFRLEVBQUVtQyxhQUFhK08sa0NBQWtDbk4sU0FBUzBILHlCQUF5QixJQUFJLENBQUNpSSxzQkFBc0JFO29CQUMzSyxJQUFJQyxxQkFBcUJuVSxnQkFBZ0JpQixnQkFBZ0IsRUFBRTt3QkFDdkRnUixvQkFBb0I7NEJBQUMxWDt5QkFBSztvQkFDOUI7b0JBQ0EsSUFBSTZaLFVBQVUsQ0FBQ3RRLEdBQUd2SixJQUFJLENBQUMsRUFBRTt3QkFDckJ1WixRQUFRRyxLQUFLLEdBQUc7d0JBQ2hCLElBQUlELHNCQUFzQjs0QkFDdEI7d0JBQ0o7b0JBQ0o7b0JBQ0EsQ0FBQ0Esd0JBQ0lyWCxDQUFBQSxJQUFJeVgsWUFBWXRRLEdBQUd2SixJQUFJLElBQ2xCMlosbUJBQ0kzRiwwQkFBMEI3TixXQUFXVSxNQUFNLEVBQUVnVCxZQUFZdFEsR0FBR3ZKLElBQUksSUFDaEUyQyxJQUFJd0QsV0FBV1UsTUFBTSxFQUFFMEMsR0FBR3ZKLElBQUksRUFBRTZaLFVBQVUsQ0FBQ3RRLEdBQUd2SixJQUFJLENBQUMsSUFDdkR5UCxNQUFNdEosV0FBV1UsTUFBTSxFQUFFMEMsR0FBR3ZKLElBQUk7Z0JBQzlDO2dCQUNBLENBQUN5TyxjQUFjbUssZUFDVixNQUFNbkIseUJBQXlCbUIsWUFBWWEsc0JBQXNCRjtZQUMxRTtRQUNKO1FBQ0EsT0FBT0EsUUFBUUcsS0FBSztJQUN4QjtJQUNBLE1BQU12UixtQkFBbUI7UUFDckIsS0FBSyxNQUFNbkksUUFBUXFILE9BQU93UCxPQUFPLENBQUU7WUFDL0IsTUFBTXhOLFFBQVFqSCxJQUFJa0gsU0FBU3RKO1lBQzNCcUosU0FDS0EsQ0FBQUEsTUFBTUUsRUFBRSxDQUFDNEgsSUFBSSxHQUNSOUgsTUFBTUUsRUFBRSxDQUFDNEgsSUFBSSxDQUFDaUQsS0FBSyxDQUFDLENBQUNqTCxNQUFRLENBQUNnRyxLQUFLaEcsUUFDbkMsQ0FBQ2dHLEtBQUs5RixNQUFNRSxFQUFFLENBQUNKLEdBQUcsTUFDeEJnQixXQUFXbks7UUFDbkI7UUFDQXFILE9BQU93UCxPQUFPLEdBQUcsSUFBSXhWO0lBQ3pCO0lBQ0EsTUFBTStXLFlBQVksQ0FBQ3BZLE1BQU1pQixPQUFTLENBQUM2SSxTQUFTL0QsUUFBUSxJQUMvQy9GLENBQUFBLFFBQVFpQixRQUFRMEIsSUFBSXVGLGFBQWFsSSxNQUFNaUIsT0FDcEMsQ0FBQzhNLFVBQVUrTCxhQUFhelUsZUFBYztJQUM5QyxNQUFNMkMsWUFBWSxDQUFDNUgsT0FBT21DLGNBQWNnRixXQUFhSCxvQkFBb0JoSCxPQUFPaUgsUUFBUTtZQUNwRixHQUFJNEMsT0FBT0QsS0FBSyxHQUNWOUIsY0FDQXhHLFlBQVlhLGdCQUNSOEMsaUJBQ0E4QixTQUFTL0csU0FDTDtnQkFBRSxDQUFDQSxNQUFNLEVBQUVtQztZQUFhLElBQ3hCQSxZQUFZO1FBQzlCLEdBQUdnRixVQUFVaEY7SUFDYixNQUFNd1gsaUJBQWlCLENBQUMvWixPQUFTNkIsUUFBUU8sSUFBSTZILE9BQU9ELEtBQUssR0FBRzlCLGNBQWM3QyxnQkFBZ0JyRixNQUFNOEosU0FBU3pCLGdCQUFnQixHQUFHakcsSUFBSWlELGdCQUFnQnJGLE1BQU0sRUFBRSxJQUFJLEVBQUU7SUFDOUosTUFBTTBZLGdCQUFnQixDQUFDMVksTUFBTVosUUFBT2tSLFVBQVUsQ0FBQyxDQUFDO1FBQzVDLE1BQU1qSCxRQUFRakgsSUFBSWtILFNBQVN0SjtRQUMzQixJQUFJNFksYUFBYXhaO1FBQ2pCLElBQUlpSyxPQUFPO1lBQ1AsTUFBTWtKLGlCQUFpQmxKLE1BQU1FLEVBQUU7WUFDL0IsSUFBSWdKLGdCQUFnQjtnQkFDaEIsQ0FBQ0EsZUFBZXhNLFFBQVEsSUFDcEJwRCxJQUFJdUYsYUFBYWxJLE1BQU15USxnQkFBZ0JyUixRQUFPbVQ7Z0JBQ2xEcUcsYUFDSWhLLGNBQWMyRCxlQUFlcEosR0FBRyxLQUFLN0osa0JBQWtCRixVQUNqRCxLQUNBQTtnQkFDVixJQUFJNFAsaUJBQWlCdUQsZUFBZXBKLEdBQUcsR0FBRztvQkFDdEM7MkJBQUlvSixlQUFlcEosR0FBRyxDQUFDbUgsT0FBTztxQkFBQyxDQUFDc0gsT0FBTyxDQUFDLENBQUNvQyxZQUFlQSxVQUFVQyxRQUFRLEdBQUdyQixXQUFXdk0sUUFBUSxDQUFDMk4sVUFBVTVhLEtBQUs7Z0JBQ3BILE9BQ0ssSUFBSW1ULGVBQWVwQixJQUFJLEVBQUU7b0JBQzFCLElBQUluUyxnQkFBZ0J1VCxlQUFlcEosR0FBRyxHQUFHO3dCQUNyQ29KLGVBQWVwQixJQUFJLENBQUN5RyxPQUFPLENBQUMsQ0FBQ3NDOzRCQUN6QixJQUFJLENBQUNBLFlBQVl6QixjQUFjLElBQUksQ0FBQ3lCLFlBQVluVSxRQUFRLEVBQUU7Z0NBQ3RELElBQUl0RyxNQUFNQyxPQUFPLENBQUNrWixhQUFhO29DQUMzQnNCLFlBQVlwYSxPQUFPLEdBQUcsQ0FBQyxDQUFDOFksV0FBV3BHLElBQUksQ0FBQyxDQUFDdlIsT0FBU0EsU0FBU2laLFlBQVk5YSxLQUFLO2dDQUNoRixPQUNLO29DQUNEOGEsWUFBWXBhLE9BQU8sR0FDZjhZLGVBQWVzQixZQUFZOWEsS0FBSyxJQUFJLENBQUMsQ0FBQ3daO2dDQUM5Qzs0QkFDSjt3QkFDSjtvQkFDSixPQUNLO3dCQUNEckcsZUFBZXBCLElBQUksQ0FBQ3lHLE9BQU8sQ0FBQyxDQUFDdUMsV0FBY0EsU0FBU3JhLE9BQU8sR0FBR3FhLFNBQVMvYSxLQUFLLEtBQUt3WjtvQkFDckY7Z0JBQ0osT0FDSyxJQUFJbEssWUFBWTZELGVBQWVwSixHQUFHLEdBQUc7b0JBQ3RDb0osZUFBZXBKLEdBQUcsQ0FBQy9KLEtBQUssR0FBRztnQkFDL0IsT0FDSztvQkFDRG1ULGVBQWVwSixHQUFHLENBQUMvSixLQUFLLEdBQUd3WjtvQkFDM0IsSUFBSSxDQUFDckcsZUFBZXBKLEdBQUcsQ0FBQ2pLLElBQUksRUFBRTt3QkFDMUJ5TixVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQzs0QkFDakI3TTs0QkFDQWlJLFFBQVFqSCxZQUFZa0g7d0JBQ3hCO29CQUNKO2dCQUNKO1lBQ0o7UUFDSjtRQUNDb0ksQ0FBQUEsUUFBUXVJLFdBQVcsSUFBSXZJLFFBQVE4SixXQUFXLEtBQ3ZDekIsb0JBQW9CM1ksTUFBTTRZLFlBQVl0SSxRQUFROEosV0FBVyxFQUFFOUosUUFBUXVJLFdBQVcsRUFBRTtRQUNwRnZJLFFBQVErSixjQUFjLElBQUlDLFFBQVF0YTtJQUN0QztJQUNBLE1BQU11YSxZQUFZLENBQUN2YSxNQUFNWixRQUFPa1I7UUFDNUIsSUFBSyxNQUFNa0ssWUFBWXBiLE9BQU87WUFDMUIsSUFBSSxDQUFDQSxPQUFNdUIsY0FBYyxDQUFDNlosV0FBVztnQkFDakM7WUFDSjtZQUNBLE1BQU01QixhQUFheFosTUFBSyxDQUFDb2IsU0FBUztZQUNsQyxNQUFNN1MsWUFBWTNILE9BQU8sTUFBTXdhO1lBQy9CLE1BQU1uUixRQUFRakgsSUFBSWtILFNBQVMzQjtZQUMxQk4sQ0FBQUEsT0FBT2tCLEtBQUssQ0FBQ2xJLEdBQUcsQ0FBQ0wsU0FDZFIsU0FBU29aLGVBQ1J2UCxTQUFTLENBQUNBLE1BQU1FLEVBQUUsS0FDbkIsQ0FBQ3BLLGFBQWF5WixjQUNaMkIsVUFBVTVTLFdBQVdpUixZQUFZdEksV0FDakNvSSxjQUFjL1EsV0FBV2lSLFlBQVl0STtRQUMvQztJQUNKO0lBQ0EsTUFBTW1LLFdBQVcsQ0FBQ3phLE1BQU1aLFFBQU9rUixVQUFVLENBQUMsQ0FBQztRQUN2QyxNQUFNakgsUUFBUWpILElBQUlrSCxTQUFTdEo7UUFDM0IsTUFBTXlVLGVBQWVwTixPQUFPa0IsS0FBSyxDQUFDbEksR0FBRyxDQUFDTDtRQUN0QyxNQUFNMGEsYUFBYTFaLFlBQVk1QjtRQUMvQnVELElBQUl1RixhQUFhbEksTUFBTTBhO1FBQ3ZCLElBQUlqRyxjQUFjO1lBQ2Q5SCxVQUFVcEUsS0FBSyxDQUFDc0UsSUFBSSxDQUFDO2dCQUNqQjdNO2dCQUNBaUksUUFBUWpILFlBQVlrSDtZQUN4QjtZQUNBLElBQUksQ0FBQ3pDLGdCQUFnQmEsT0FBTyxJQUN4QmIsZ0JBQWdCZSxXQUFXLElBQzNCd1EseUJBQXlCMVEsT0FBTyxJQUNoQzBRLHlCQUF5QnhRLFdBQVcsS0FDcEM4SixRQUFRdUksV0FBVyxFQUFFO2dCQUNyQmxNLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUNqQjdNO29CQUNBd0csYUFBYTBKLGVBQWU3SyxnQkFBZ0I2QztvQkFDNUM1QixTQUFTOFIsVUFBVXBZLE1BQU0wYTtnQkFDN0I7WUFDSjtRQUNKLE9BQ0s7WUFDRHJSLFNBQVMsQ0FBQ0EsTUFBTUUsRUFBRSxJQUFJLENBQUNqSyxrQkFBa0JvYixjQUNuQ0gsVUFBVXZhLE1BQU0wYSxZQUFZcEssV0FDNUJvSSxjQUFjMVksTUFBTTBhLFlBQVlwSztRQUMxQztRQUNBcUMsVUFBVTNTLE1BQU1xSCxXQUFXc0YsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFBRSxHQUFHMUcsVUFBVTtRQUFDO1FBQ2hFd0csVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakI3TSxNQUFNaUssT0FBT0QsS0FBSyxHQUFHaEssT0FBTzRCO1lBQzVCcUcsUUFBUWpILFlBQVlrSDtRQUN4QjtJQUNKO0lBQ0EsTUFBTXpFLFdBQVcsT0FBTzdEO1FBQ3BCcUssT0FBT0QsS0FBSyxHQUFHO1FBQ2YsTUFBTW5LLFNBQVNELE1BQU1DLE1BQU07UUFDM0IsSUFBSUcsT0FBT0gsT0FBT0csSUFBSTtRQUN0QixJQUFJMmEsc0JBQXNCO1FBQzFCLE1BQU10UixRQUFRakgsSUFBSWtILFNBQVN0SjtRQUMzQixNQUFNNGEsNkJBQTZCLENBQUNoQztZQUNoQytCLHNCQUNJRSxPQUFPM1gsS0FBSyxDQUFDMFYsZUFDUnpaLGFBQWF5WixlQUFlMVYsTUFBTTBWLFdBQVd4SyxPQUFPLE9BQ3JETCxVQUFVNkssWUFBWXhXLElBQUk4RixhQUFhbEksTUFBTTRZO1FBQ3pEO1FBQ0EsTUFBTWtDLDZCQUE2QmhKLG1CQUFtQmhJLFNBQVNpSSxJQUFJO1FBQ25FLE1BQU1nSiw0QkFBNEJqSixtQkFBbUJoSSxTQUFTZ0ssY0FBYztRQUM1RSxJQUFJekssT0FBTztZQUNQLElBQUlKO1lBQ0osSUFBSXJDO1lBQ0osTUFBTWdTLGFBQWEvWSxPQUFPWCxJQUFJLEdBQ3hCK1IsY0FBYzVILE1BQU1FLEVBQUUsSUFDdEI1SixjQUFjQztZQUNwQixNQUFNZ1QsY0FBY2hULE1BQU1WLElBQUksS0FBS2lFLE9BQU9DLElBQUksSUFBSXhELE1BQU1WLElBQUksS0FBS2lFLE9BQU9FLFNBQVM7WUFDakYsTUFBTTJYLHVCQUF1QixDQUFFdEksY0FBY3JKLE1BQU1FLEVBQUUsS0FDakQsQ0FBQ08sU0FBU3lOLFFBQVEsSUFDbEIsQ0FBQ25WLElBQUkrRCxXQUFXVSxNQUFNLEVBQUU3RyxTQUN4QixDQUFDcUosTUFBTUUsRUFBRSxDQUFDMFIsSUFBSSxJQUNkckgsZUFBZWhCLGFBQWF4USxJQUFJK0QsV0FBV00sYUFBYSxFQUFFekcsT0FBT21HLFdBQVcwTixXQUFXLEVBQUVrSCwyQkFBMkJEO1lBQ3hILE1BQU1JLFVBQVV2SSxVQUFVM1MsTUFBTXFILFFBQVF1TDtZQUN4Q2pRLElBQUl1RixhQUFhbEksTUFBTTRZO1lBQ3ZCLElBQUloRyxhQUFhO2dCQUNidkosTUFBTUUsRUFBRSxDQUFDL0YsTUFBTSxJQUFJNkYsTUFBTUUsRUFBRSxDQUFDL0YsTUFBTSxDQUFDNUQ7Z0JBQ25Da1gsc0JBQXNCQSxtQkFBbUI7WUFDN0MsT0FDSyxJQUFJek4sTUFBTUUsRUFBRSxDQUFDOUYsUUFBUSxFQUFFO2dCQUN4QjRGLE1BQU1FLEVBQUUsQ0FBQzlGLFFBQVEsQ0FBQzdEO1lBQ3RCO1lBQ0EsTUFBTWdKLGFBQWErUCxvQkFBb0IzWSxNQUFNNFksWUFBWWhHO1lBQ3pELE1BQU1rRyxlQUFlLENBQUNySyxjQUFjN0YsZUFBZXNTO1lBQ25ELENBQUN0SSxlQUNHakcsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCN007Z0JBQ0FkLE1BQU1VLE1BQU1WLElBQUk7Z0JBQ2hCK0ksUUFBUWpILFlBQVlrSDtZQUN4QjtZQUNKLElBQUk4UyxzQkFBc0I7Z0JBQ3RCLElBQUl2VixnQkFBZ0JtQixPQUFPLElBQUlvUSx5QkFBeUJwUSxPQUFPLEVBQUU7b0JBQzdELElBQUlrRCxTQUFTaUksSUFBSSxLQUFLLFVBQVU7d0JBQzVCLElBQUlhLGFBQWE7NEJBQ2IzTDt3QkFDSjtvQkFDSixPQUNLLElBQUksQ0FBQzJMLGFBQWE7d0JBQ25CM0w7b0JBQ0o7Z0JBQ0o7Z0JBQ0EsT0FBUTZSLGdCQUNKbk0sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7b0JBQUU3TTtvQkFBTSxHQUFJa2IsVUFBVSxDQUFDLElBQUl0UyxVQUFVO2dCQUFFO1lBQ3BFO1lBQ0EsQ0FBQ2dLLGVBQWVzSSxXQUFXdk8sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQUUsR0FBRzFHLFVBQVU7WUFBQztZQUNoRSxJQUFJMkQsU0FBU3lOLFFBQVEsRUFBRTtnQkFDbkIsTUFBTSxFQUFFMVEsTUFBTSxFQUFFLEdBQUcsTUFBTTJRLFdBQVc7b0JBQUN4WDtpQkFBSztnQkFDMUM0YSwyQkFBMkJoQztnQkFDM0IsSUFBSStCLHFCQUFxQjtvQkFDckIsTUFBTVEsNEJBQTRCakksa0JBQWtCL00sV0FBV1UsTUFBTSxFQUFFeUMsU0FBU3RKO29CQUNoRixNQUFNb2Isb0JBQW9CbEksa0JBQWtCck0sUUFBUXlDLFNBQVM2UiwwQkFBMEJuYixJQUFJLElBQUlBO29CQUMvRmlKLFFBQVFtUyxrQkFBa0JuUyxLQUFLO29CQUMvQmpKLE9BQU9vYixrQkFBa0JwYixJQUFJO29CQUM3QjRHLFVBQVU2SCxjQUFjNUg7Z0JBQzVCO1lBQ0osT0FDSztnQkFDRDZRLG9CQUFvQjtvQkFBQzFYO2lCQUFLLEVBQUU7Z0JBQzVCaUosUUFBUSxDQUFDLE1BQU1zTCxjQUFjbEwsT0FBT2hDLE9BQU90QixRQUFRLEVBQUVtQyxhQUFhK08sa0NBQWtDbk4sU0FBUzBILHlCQUF5QixFQUFFLENBQUN4UixLQUFLO2dCQUM5STBYLG9CQUFvQjtvQkFBQzFYO2lCQUFLO2dCQUMxQjRhLDJCQUEyQmhDO2dCQUMzQixJQUFJK0IscUJBQXFCO29CQUNyQixJQUFJMVIsT0FBTzt3QkFDUHJDLFVBQVU7b0JBQ2QsT0FDSyxJQUFJbkIsZ0JBQWdCbUIsT0FBTyxJQUM1Qm9RLHlCQUF5QnBRLE9BQU8sRUFBRTt3QkFDbENBLFVBQVUsTUFBTTZRLHlCQUF5Qm5PLFNBQVM7b0JBQ3REO2dCQUNKO1lBQ0o7WUFDQSxJQUFJcVIscUJBQXFCO2dCQUNyQnRSLE1BQU1FLEVBQUUsQ0FBQzBSLElBQUksSUFDVFgsUUFBUWpSLE1BQU1FLEVBQUUsQ0FBQzBSLElBQUk7Z0JBQ3pCOUIsb0JBQW9CblosTUFBTTRHLFNBQVNxQyxPQUFPTDtZQUM5QztRQUNKO0lBQ0o7SUFDQSxNQUFNeVMsY0FBYyxDQUFDbFMsS0FBSzVIO1FBQ3RCLElBQUlhLElBQUkrRCxXQUFXVSxNQUFNLEVBQUV0RixRQUFRNEgsSUFBSUssS0FBSyxFQUFFO1lBQzFDTCxJQUFJSyxLQUFLO1lBQ1QsT0FBTztRQUNYO1FBQ0E7SUFDSjtJQUNBLE1BQU04USxVQUFVLE9BQU90YSxNQUFNc1EsVUFBVSxDQUFDLENBQUM7UUFDckMsSUFBSTFKO1FBQ0osSUFBSTBQO1FBQ0osTUFBTWdGLGFBQWFqTyxzQkFBc0JyTjtRQUN6QyxJQUFJOEosU0FBU3lOLFFBQVEsRUFBRTtZQUNuQixNQUFNMVEsU0FBUyxNQUFNMlMsNEJBQTRCOVgsWUFBWTFCLFFBQVFBLE9BQU9zYjtZQUM1RTFVLFVBQVU2SCxjQUFjNUg7WUFDeEJ5UCxtQkFBbUJ0VyxPQUNiLENBQUNzYixXQUFXbFAsSUFBSSxDQUFDLENBQUNwTSxPQUFTb0MsSUFBSXlFLFFBQVE3RyxTQUN2QzRHO1FBQ1YsT0FDSyxJQUFJNUcsTUFBTTtZQUNYc1csbUJBQW1CLENBQUMsTUFBTWlGLFFBQVEzWCxHQUFHLENBQUMwWCxXQUFXNVQsR0FBRyxDQUFDLE9BQU9DO2dCQUN4RCxNQUFNMEIsUUFBUWpILElBQUlrSCxTQUFTM0I7Z0JBQzNCLE9BQU8sTUFBTThQLHlCQUF5QnBPLFNBQVNBLE1BQU1FLEVBQUUsR0FBRztvQkFBRSxDQUFDNUIsVUFBVSxFQUFFMEI7Z0JBQU0sSUFBSUE7WUFDdkYsR0FBRSxFQUFHK0ssS0FBSyxDQUFDclM7WUFDWCxDQUFFLEVBQUN1VSxvQkFBb0IsQ0FBQ25RLFdBQVdTLE9BQU8sS0FBS0s7UUFDbkQsT0FDSztZQUNEcVAsbUJBQW1CMVAsVUFBVSxNQUFNNlEseUJBQXlCbk87UUFDaEU7UUFDQXFELFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCLEdBQUksQ0FBQzFGLFNBQVNuSCxTQUNULENBQUN5RixnQkFBZ0JtQixPQUFPLElBQUlvUSx5QkFBeUJwUSxPQUFPLEtBQ3pEQSxZQUFZVCxXQUFXUyxPQUFPLEdBQ2hDLENBQUMsSUFDRDtnQkFBRTVHO1lBQUssQ0FBQztZQUNkLEdBQUk4SixTQUFTeU4sUUFBUSxJQUFJLENBQUN2WCxPQUFPO2dCQUFFNEc7WUFBUSxJQUFJLENBQUMsQ0FBQztZQUNqREMsUUFBUVYsV0FBV1UsTUFBTTtRQUM3QjtRQUNBeUosUUFBUWtMLFdBQVcsSUFDZixDQUFDbEYsb0JBQ0R2RCxzQkFBc0J6SixTQUFTK1IsYUFBYXJiLE9BQU9zYixhQUFhalUsT0FBTzJDLEtBQUs7UUFDaEYsT0FBT3NNO0lBQ1g7SUFDQSxNQUFNd0QsWUFBWSxDQUFDd0I7UUFDZixNQUFNclQsU0FBUztZQUNYLEdBQUlnQyxPQUFPRCxLQUFLLEdBQUc5QixjQUFjN0MsY0FBYztRQUNuRDtRQUNBLE9BQU8zRCxZQUFZNFosY0FDYnJULFNBQ0FkLFNBQVNtVSxjQUNMbFosSUFBSTZGLFFBQVFxVCxjQUNaQSxXQUFXNVQsR0FBRyxDQUFDLENBQUMxSCxPQUFTb0MsSUFBSTZGLFFBQVFqSTtJQUNuRDtJQUNBLE1BQU15YixnQkFBZ0IsQ0FBQ3piLE1BQU1nRixZQUFlO1lBQ3hDOEQsU0FBUyxDQUFDLENBQUMxRyxJQUFJLENBQUM0QyxhQUFhbUIsVUFBUyxFQUFHVSxNQUFNLEVBQUU3RztZQUNqRHNHLFNBQVMsQ0FBQyxDQUFDbEUsSUFBSSxDQUFDNEMsYUFBYW1CLFVBQVMsRUFBR0ssV0FBVyxFQUFFeEc7WUFDdERpSixPQUFPN0csSUFBSSxDQUFDNEMsYUFBYW1CLFVBQVMsRUFBR1UsTUFBTSxFQUFFN0c7WUFDN0MyRyxjQUFjLENBQUMsQ0FBQ3ZFLElBQUkrRCxXQUFXTyxnQkFBZ0IsRUFBRTFHO1lBQ2pEZ0osV0FBVyxDQUFDLENBQUM1RyxJQUFJLENBQUM0QyxhQUFhbUIsVUFBUyxFQUFHTSxhQUFhLEVBQUV6RztRQUM5RDtJQUNBLE1BQU0wYixjQUFjLENBQUMxYjtRQUNqQkEsUUFDSXFOLHNCQUFzQnJOLE1BQU00WCxPQUFPLENBQUMsQ0FBQytELFlBQWNsTSxNQUFNdEosV0FBV1UsTUFBTSxFQUFFOFU7UUFDaEZoUCxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQmhHLFFBQVE3RyxPQUFPbUcsV0FBV1UsTUFBTSxHQUFHLENBQUM7UUFDeEM7SUFDSjtJQUNBLE1BQU1rRyxXQUFXLENBQUMvTSxNQUFNaUosT0FBT3FIO1FBQzNCLE1BQU1uSCxNQUFNLENBQUMvRyxJQUFJa0gsU0FBU3RKLE1BQU07WUFBRXVKLElBQUksQ0FBQztRQUFFLEdBQUdBLEVBQUUsSUFBSSxDQUFDLEdBQUdKLEdBQUc7UUFDekQsTUFBTXlTLGVBQWV4WixJQUFJK0QsV0FBV1UsTUFBTSxFQUFFN0csU0FBUyxDQUFDO1FBQ3RELHVFQUF1RTtRQUN2RSxNQUFNLEVBQUVtSixLQUFLMFMsVUFBVSxFQUFFbFMsT0FBTyxFQUFFekssSUFBSSxFQUFFLEdBQUc0YyxpQkFBaUIsR0FBR0Y7UUFDL0RqWixJQUFJd0QsV0FBV1UsTUFBTSxFQUFFN0csTUFBTTtZQUN6QixHQUFHOGIsZUFBZTtZQUNsQixHQUFHN1MsS0FBSztZQUNSRTtRQUNKO1FBQ0F3RCxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQjdNO1lBQ0E2RyxRQUFRVixXQUFXVSxNQUFNO1lBQ3pCRCxTQUFTO1FBQ2I7UUFDQTBKLFdBQVdBLFFBQVFrTCxXQUFXLElBQUlyUyxPQUFPQSxJQUFJSyxLQUFLLElBQUlMLElBQUlLLEtBQUs7SUFDbkU7SUFDQSxNQUFNaEMsUUFBUSxDQUFDeEgsTUFBTXVDLGVBQWlCb00sV0FBVzNPLFFBQzNDMk0sVUFBVUMsS0FBSyxDQUFDYSxTQUFTLENBQUM7WUFDeEJaLE1BQU0sQ0FBQ2tQLFVBQVkvYixLQUFLZ0ksVUFBVXBHLFdBQVdXLGVBQWV3WjtRQUNoRSxLQUNFL1QsVUFBVWhJLE1BQU11QyxjQUFjO0lBQ3BDLE1BQU11RSxhQUFhLENBQUNuQyxRQUFVZ0ksVUFBVUMsS0FBSyxDQUFDYSxTQUFTLENBQUM7WUFDcERaLE1BQU0sQ0FBQzdIO2dCQUNILElBQUl5TyxzQkFBc0I5TyxNQUFNM0UsSUFBSSxFQUFFZ0YsVUFBVWhGLElBQUksRUFBRTJFLE1BQU1xQixLQUFLLEtBQzdEdU4sc0JBQXNCdk8sV0FBV0wsTUFBTUssU0FBUyxJQUFJUyxpQkFBaUJ1VyxlQUFlclgsTUFBTXNYLFlBQVksR0FBRztvQkFDekd0WCxNQUFNcUMsUUFBUSxDQUFDO3dCQUNYaUIsUUFBUTs0QkFBRSxHQUFHQyxXQUFXO3dCQUFDO3dCQUN6QixHQUFHL0IsVUFBVTt3QkFDYixHQUFHbkIsU0FBUztvQkFDaEI7Z0JBQ0o7WUFDSjtRQUNKLEdBQUcySSxXQUFXO0lBQ2QsTUFBTUYsWUFBWSxDQUFDOUk7UUFDZnNGLE9BQU9ELEtBQUssR0FBRztRQUNmZ04sMkJBQTJCO1lBQ3ZCLEdBQUdBLHdCQUF3QjtZQUMzQixHQUFHclMsTUFBTUssU0FBUztRQUN0QjtRQUNBLE9BQU84QixXQUFXO1lBQ2QsR0FBR25DLEtBQUs7WUFDUkssV0FBV2dTO1FBQ2Y7SUFDSjtJQUNBLE1BQU03TSxhQUFhLENBQUNuSyxNQUFNc1EsVUFBVSxDQUFDLENBQUM7UUFDbEMsS0FBSyxNQUFNM0ksYUFBYTNILE9BQU9xTixzQkFBc0JyTixRQUFRcUgsT0FBTzJDLEtBQUssQ0FBRTtZQUN2RTNDLE9BQU8yQyxLQUFLLENBQUNrUyxNQUFNLENBQUN2VTtZQUNwQk4sT0FBT2tCLEtBQUssQ0FBQzJULE1BQU0sQ0FBQ3ZVO1lBQ3BCLElBQUksQ0FBQzJJLFFBQVE2TCxTQUFTLEVBQUU7Z0JBQ3BCMU0sTUFBTW5HLFNBQVMzQjtnQkFDZjhILE1BQU12SCxhQUFhUDtZQUN2QjtZQUNBLENBQUMySSxRQUFROEwsU0FBUyxJQUFJM00sTUFBTXRKLFdBQVdVLE1BQU0sRUFBRWM7WUFDL0MsQ0FBQzJJLFFBQVErTCxTQUFTLElBQUk1TSxNQUFNdEosV0FBV0ssV0FBVyxFQUFFbUI7WUFDcEQsQ0FBQzJJLFFBQVFnTSxXQUFXLElBQUk3TSxNQUFNdEosV0FBV00sYUFBYSxFQUFFa0I7WUFDeEQsQ0FBQzJJLFFBQVFpTSxnQkFBZ0IsSUFDckI5TSxNQUFNdEosV0FBV08sZ0JBQWdCLEVBQUVpQjtZQUN2QyxDQUFDbUMsU0FBU3pCLGdCQUFnQixJQUN0QixDQUFDaUksUUFBUWtNLGdCQUFnQixJQUN6Qi9NLE1BQU1wSyxnQkFBZ0JzQztRQUM5QjtRQUNBZ0YsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakI1RSxRQUFRakgsWUFBWWtIO1FBQ3hCO1FBQ0F5RSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQixHQUFHMUcsVUFBVTtZQUNiLEdBQUksQ0FBQ21LLFFBQVErTCxTQUFTLEdBQUcsQ0FBQyxJQUFJO2dCQUFFL1YsU0FBUzhSO1lBQVksQ0FBQztRQUMxRDtRQUNBLENBQUM5SCxRQUFRbU0sV0FBVyxJQUFJeFY7SUFDNUI7SUFDQSxNQUFNbUQsb0JBQW9CLENBQUMsRUFBRXJFLFFBQVEsRUFBRS9GLElBQUksRUFBRztRQUMxQyxJQUFJLFVBQVcrRixhQUFha0UsT0FBT0QsS0FBSyxJQUNwQyxDQUFDLENBQUNqRSxZQUNGc0IsT0FBT3RCLFFBQVEsQ0FBQzFGLEdBQUcsQ0FBQ0wsT0FBTztZQUMzQitGLFdBQVdzQixPQUFPdEIsUUFBUSxDQUFDMEIsR0FBRyxDQUFDekgsUUFBUXFILE9BQU90QixRQUFRLENBQUNtVyxNQUFNLENBQUNsYztRQUNsRTtJQUNKO0lBQ0EsTUFBTTBJLFdBQVcsQ0FBQzFJLE1BQU1zUSxVQUFVLENBQUMsQ0FBQztRQUNoQyxJQUFJakgsUUFBUWpILElBQUlrSCxTQUFTdEo7UUFDekIsTUFBTTBjLG9CQUFvQmhhLFVBQVU0TixRQUFRdkssUUFBUSxLQUFLckQsVUFBVW9ILFNBQVMvRCxRQUFRO1FBQ3BGcEQsSUFBSTJHLFNBQVN0SixNQUFNO1lBQ2YsR0FBSXFKLFNBQVMsQ0FBQyxDQUFDO1lBQ2ZFLElBQUk7Z0JBQ0EsR0FBSUYsU0FBU0EsTUFBTUUsRUFBRSxHQUFHRixNQUFNRSxFQUFFLEdBQUc7b0JBQUVKLEtBQUs7d0JBQUVuSjtvQkFBSztnQkFBRSxDQUFDO2dCQUNwREE7Z0JBQ0FnSyxPQUFPO2dCQUNQLEdBQUdzRyxPQUFPO1lBQ2Q7UUFDSjtRQUNBakosT0FBTzJDLEtBQUssQ0FBQ3ZDLEdBQUcsQ0FBQ3pIO1FBQ2pCLElBQUlxSixPQUFPO1lBQ1BlLGtCQUFrQjtnQkFDZHJFLFVBQVVyRCxVQUFVNE4sUUFBUXZLLFFBQVEsSUFDOUJ1SyxRQUFRdkssUUFBUSxHQUNoQitELFNBQVMvRCxRQUFRO2dCQUN2Qi9GO1lBQ0o7UUFDSixPQUNLO1lBQ0R1WSxvQkFBb0J2WSxNQUFNLE1BQU1zUSxRQUFRbFIsS0FBSztRQUNqRDtRQUNBLE9BQU87WUFDSCxHQUFJc2Qsb0JBQ0U7Z0JBQUUzVyxVQUFVdUssUUFBUXZLLFFBQVEsSUFBSStELFNBQVMvRCxRQUFRO1lBQUMsSUFDbEQsQ0FBQyxDQUFDO1lBQ1IsR0FBSStELFNBQVM2UyxXQUFXLEdBQ2xCO2dCQUNFeFksVUFBVSxDQUFDLENBQUNtTSxRQUFRbk0sUUFBUTtnQkFDNUJKLEtBQUs0TixhQUFhckIsUUFBUXZNLEdBQUc7Z0JBQzdCRCxLQUFLNk4sYUFBYXJCLFFBQVF4TSxHQUFHO2dCQUM3QkcsV0FBVzBOLGFBQWFyQixRQUFRck0sU0FBUztnQkFDekNELFdBQVcyTixhQUFhckIsUUFBUXRNLFNBQVM7Z0JBQ3pDRSxTQUFTeU4sYUFBYXJCLFFBQVFwTSxPQUFPO1lBQ3pDLElBQ0UsQ0FBQyxDQUFDO1lBQ1JsRTtZQUNBeUQ7WUFDQUQsUUFBUUM7WUFDUjBGLEtBQUssQ0FBQ0E7Z0JBQ0YsSUFBSUEsS0FBSztvQkFDTFQsU0FBUzFJLE1BQU1zUTtvQkFDZmpILFFBQVFqSCxJQUFJa0gsU0FBU3RKO29CQUNyQixNQUFNNGMsV0FBV2xiLFlBQVl5SCxJQUFJL0osS0FBSyxJQUNoQytKLElBQUkwVCxnQkFBZ0IsR0FDaEIxVCxJQUFJMFQsZ0JBQWdCLENBQUMsd0JBQXdCLENBQUMsRUFBRSxJQUFJMVQsTUFDcERBLE1BQ0pBO29CQUNOLE1BQU0yVCxrQkFBa0I1TixrQkFBa0IwTjtvQkFDMUMsTUFBTXpMLE9BQU85SCxNQUFNRSxFQUFFLENBQUM0SCxJQUFJLElBQUksRUFBRTtvQkFDaEMsSUFBSTJMLGtCQUNFM0wsS0FBS3FCLElBQUksQ0FBQyxDQUFDakMsU0FBV0EsV0FBV3FNLFlBQ2pDQSxhQUFhdlQsTUFBTUUsRUFBRSxDQUFDSixHQUFHLEVBQUU7d0JBQzdCO29CQUNKO29CQUNBeEcsSUFBSTJHLFNBQVN0SixNQUFNO3dCQUNmdUosSUFBSTs0QkFDQSxHQUFHRixNQUFNRSxFQUFFOzRCQUNYLEdBQUl1VCxrQkFDRTtnQ0FDRTNMLE1BQU07dUNBQ0NBLEtBQUtyUCxNQUFNLENBQUNxTjtvQ0FDZnlOO3VDQUNJbmQsTUFBTUMsT0FBTyxDQUFDMEMsSUFBSWlELGdCQUFnQnJGLFNBQVM7d0NBQUMsQ0FBQztxQ0FBRSxHQUFHLEVBQUU7aUNBQzNEO2dDQUNEbUosS0FBSztvQ0FBRWpLLE1BQU0wZCxTQUFTMWQsSUFBSTtvQ0FBRWM7Z0NBQUs7NEJBQ3JDLElBQ0U7Z0NBQUVtSixLQUFLeVQ7NEJBQVMsQ0FBQzt3QkFDM0I7b0JBQ0o7b0JBQ0FyRSxvQkFBb0J2WSxNQUFNLE9BQU80QixXQUFXZ2I7Z0JBQ2hELE9BQ0s7b0JBQ0R2VCxRQUFRakgsSUFBSWtILFNBQVN0SixNQUFNLENBQUM7b0JBQzVCLElBQUlxSixNQUFNRSxFQUFFLEVBQUU7d0JBQ1ZGLE1BQU1FLEVBQUUsQ0FBQ1MsS0FBSyxHQUFHO29CQUNyQjtvQkFDQ0YsQ0FBQUEsU0FBU3pCLGdCQUFnQixJQUFJaUksUUFBUWpJLGdCQUFnQixLQUNsRCxDQUFFbEksQ0FBQUEsbUJBQW1Ca0gsT0FBT2tCLEtBQUssRUFBRXZJLFNBQVNpSyxPQUFPQyxNQUFNLEtBQ3pEN0MsT0FBT3dQLE9BQU8sQ0FBQ3BQLEdBQUcsQ0FBQ3pIO2dCQUMzQjtZQUNKO1FBQ0o7SUFDSjtJQUNBLE1BQU0rYyxjQUFjLElBQU1qVCxTQUFTME0sZ0JBQWdCLElBQy9DekQsc0JBQXNCekosU0FBUytSLGFBQWFoVSxPQUFPMkMsS0FBSztJQUM1RCxNQUFNZ1QsZUFBZSxDQUFDalg7UUFDbEIsSUFBSXJELFVBQVVxRCxXQUFXO1lBQ3JCNEcsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQUU5RztZQUFTO1lBQ2hDZ04sc0JBQXNCekosU0FBUyxDQUFDSCxLQUFLbko7Z0JBQ2pDLE1BQU1pVCxlQUFlN1EsSUFBSWtILFNBQVN0SjtnQkFDbEMsSUFBSWlULGNBQWM7b0JBQ2Q5SixJQUFJcEQsUUFBUSxHQUFHa04sYUFBYTFKLEVBQUUsQ0FBQ3hELFFBQVEsSUFBSUE7b0JBQzNDLElBQUl0RyxNQUFNQyxPQUFPLENBQUN1VCxhQUFhMUosRUFBRSxDQUFDNEgsSUFBSSxHQUFHO3dCQUNyQzhCLGFBQWExSixFQUFFLENBQUM0SCxJQUFJLENBQUN5RyxPQUFPLENBQUMsQ0FBQ2pEOzRCQUMxQkEsU0FBUzVPLFFBQVEsR0FBR2tOLGFBQWExSixFQUFFLENBQUN4RCxRQUFRLElBQUlBO3dCQUNwRDtvQkFDSjtnQkFDSjtZQUNKLEdBQUcsR0FBRztRQUNWO0lBQ0o7SUFDQSxNQUFNMkYsZUFBZSxDQUFDdVIsU0FBU0MsWUFBYyxPQUFPQztZQUNoRCxJQUFJQyxlQUFleGI7WUFDbkIsSUFBSXViLEdBQUc7Z0JBQ0hBLEVBQUVFLGNBQWMsSUFBSUYsRUFBRUUsY0FBYztnQkFDcENGLEVBQUVHLE9BQU8sSUFDTEgsRUFBRUcsT0FBTztZQUNqQjtZQUNBLElBQUlyRixjQUFjalgsWUFBWWtIO1lBQzlCeUUsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCK0osY0FBYztZQUNsQjtZQUNBLElBQUk5TSxTQUFTeU4sUUFBUSxFQUFFO2dCQUNuQixNQUFNLEVBQUUxUSxNQUFNLEVBQUVvQixNQUFNLEVBQUUsR0FBRyxNQUFNdVA7Z0JBQ2pDclIsV0FBV1UsTUFBTSxHQUFHQTtnQkFDcEJvUixjQUFjalgsWUFBWWlIO1lBQzlCLE9BQ0s7Z0JBQ0QsTUFBTXdQLHlCQUF5Qm5PO1lBQ25DO1lBQ0EsSUFBSWpDLE9BQU90QixRQUFRLENBQUN3WCxJQUFJLEVBQUU7Z0JBQ3RCLEtBQUssTUFBTXZkLFFBQVFxSCxPQUFPdEIsUUFBUSxDQUFFO29CQUNoQzBKLE1BQU13SSxhQUFhalk7Z0JBQ3ZCO1lBQ0o7WUFDQXlQLE1BQU10SixXQUFXVSxNQUFNLEVBQUU7WUFDekIsSUFBSTRILGNBQWN0SSxXQUFXVSxNQUFNLEdBQUc7Z0JBQ2xDOEYsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7b0JBQ2pCaEcsUUFBUSxDQUFDO2dCQUNiO2dCQUNBLElBQUk7b0JBQ0EsTUFBTW9XLFFBQVFoRixhQUFha0Y7Z0JBQy9CLEVBQ0EsT0FBT2xVLE9BQU87b0JBQ1ZtVSxlQUFlblU7Z0JBQ25CO1lBQ0osT0FDSztnQkFDRCxJQUFJaVUsV0FBVztvQkFDWCxNQUFNQSxVQUFVO3dCQUFFLEdBQUcvVyxXQUFXVSxNQUFNO29CQUFDLEdBQUdzVztnQkFDOUM7Z0JBQ0FKO2dCQUNBMUYsV0FBVzBGO1lBQ2Y7WUFDQXBRLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQmdILGFBQWE7Z0JBQ2IrQyxjQUFjO2dCQUNkOUosb0JBQW9CMkIsY0FBY3RJLFdBQVdVLE1BQU0sS0FBSyxDQUFDdVc7Z0JBQ3pEMUcsYUFBYXZRLFdBQVd1USxXQUFXLEdBQUc7Z0JBQ3RDN1AsUUFBUVYsV0FBV1UsTUFBTTtZQUM3QjtZQUNBLElBQUl1VyxjQUFjO2dCQUNkLE1BQU1BO1lBQ1Y7UUFDSjtJQUNBLE1BQU1JLGFBQWEsQ0FBQ3hkLE1BQU1zUSxVQUFVLENBQUMsQ0FBQztRQUNsQyxJQUFJbE8sSUFBSWtILFNBQVN0SixPQUFPO1lBQ3BCLElBQUkwQixZQUFZNE8sUUFBUS9OLFlBQVksR0FBRztnQkFDbkNrWSxTQUFTemEsTUFBTWdCLFlBQVlvQixJQUFJaUQsZ0JBQWdCckY7WUFDbkQsT0FDSztnQkFDRHlhLFNBQVN6YSxNQUFNc1EsUUFBUS9OLFlBQVk7Z0JBQ25DSSxJQUFJMEMsZ0JBQWdCckYsTUFBTWdCLFlBQVlzUCxRQUFRL04sWUFBWTtZQUM5RDtZQUNBLElBQUksQ0FBQytOLFFBQVFnTSxXQUFXLEVBQUU7Z0JBQ3RCN00sTUFBTXRKLFdBQVdNLGFBQWEsRUFBRXpHO1lBQ3BDO1lBQ0EsSUFBSSxDQUFDc1EsUUFBUStMLFNBQVMsRUFBRTtnQkFDcEI1TSxNQUFNdEosV0FBV0ssV0FBVyxFQUFFeEc7Z0JBQzlCbUcsV0FBV0csT0FBTyxHQUFHZ0ssUUFBUS9OLFlBQVksR0FDbkM2VixVQUFVcFksTUFBTWdCLFlBQVlvQixJQUFJaUQsZ0JBQWdCckYsVUFDaERvWTtZQUNWO1lBQ0EsSUFBSSxDQUFDOUgsUUFBUThMLFNBQVMsRUFBRTtnQkFDcEIzTSxNQUFNdEosV0FBV1UsTUFBTSxFQUFFN0c7Z0JBQ3pCeUYsZ0JBQWdCbUIsT0FBTyxJQUFJSztZQUMvQjtZQUNBMEYsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQUUsR0FBRzFHLFVBQVU7WUFBQztRQUN6QztJQUNKO0lBQ0EsTUFBTXNYLFNBQVMsQ0FBQ25XLFlBQVlvVyxtQkFBbUIsQ0FBQyxDQUFDO1FBQzdDLE1BQU1DLGdCQUFnQnJXLGFBQWF0RyxZQUFZc0csY0FBY2pDO1FBQzdELE1BQU11WSxxQkFBcUI1YyxZQUFZMmM7UUFDdkMsTUFBTUUscUJBQXFCcFAsY0FBY25IO1FBQ3pDLE1BQU1XLFNBQVM0VixxQkFBcUJ4WSxpQkFBaUJ1WTtRQUNyRCxJQUFJLENBQUNGLGlCQUFpQkksaUJBQWlCLEVBQUU7WUFDckN6WSxpQkFBaUJzWTtRQUNyQjtRQUNBLElBQUksQ0FBQ0QsaUJBQWlCSyxVQUFVLEVBQUU7WUFDOUIsSUFBSUwsaUJBQWlCTSxlQUFlLEVBQUU7Z0JBQ2xDLE1BQU1DLGdCQUFnQixJQUFJNWMsSUFBSTt1QkFDdkJnRyxPQUFPMkMsS0FBSzt1QkFDWjFFLE9BQU9vRixJQUFJLENBQUN3RixlQUFlN0ssZ0JBQWdCNkM7aUJBQ2pEO2dCQUNELEtBQUssTUFBTVAsYUFBYWxJLE1BQU1rWSxJQUFJLENBQUNzRyxlQUFnQjtvQkFDL0M3YixJQUFJK0QsV0FBV0ssV0FBVyxFQUFFbUIsYUFDdEJoRixJQUFJc0YsUUFBUU4sV0FBV3ZGLElBQUk4RixhQUFhUCxjQUN4QzhTLFNBQVM5UyxXQUFXdkYsSUFBSTZGLFFBQVFOO2dCQUMxQztZQUNKLE9BQ0s7Z0JBQ0QsSUFBSS9HLFNBQVNjLFlBQVk0RixhQUFhO29CQUNsQyxLQUFLLE1BQU10SCxRQUFRcUgsT0FBTzJDLEtBQUssQ0FBRTt3QkFDN0IsTUFBTVgsUUFBUWpILElBQUlrSCxTQUFTdEo7d0JBQzNCLElBQUlxSixTQUFTQSxNQUFNRSxFQUFFLEVBQUU7NEJBQ25CLE1BQU1nSixpQkFBaUI5UyxNQUFNQyxPQUFPLENBQUMySixNQUFNRSxFQUFFLENBQUM0SCxJQUFJLElBQzVDOUgsTUFBTUUsRUFBRSxDQUFDNEgsSUFBSSxDQUFDLEVBQUUsR0FDaEI5SCxNQUFNRSxFQUFFLENBQUNKLEdBQUc7NEJBQ2xCLElBQUl5RixjQUFjMkQsaUJBQWlCO2dDQUMvQixNQUFNMkwsT0FBTzNMLGVBQWU0TCxPQUFPLENBQUM7Z0NBQ3BDLElBQUlELE1BQU07b0NBQ05BLEtBQUtFLEtBQUs7b0NBQ1Y7Z0NBQ0o7NEJBQ0o7d0JBQ0o7b0JBQ0o7Z0JBQ0o7Z0JBQ0EsS0FBSyxNQUFNelcsYUFBYU4sT0FBTzJDLEtBQUssQ0FBRTtvQkFDbEMsTUFBTTVLLFNBQVFnRCxJQUFJNkYsUUFBUU4sV0FBV3ZGLElBQUlpRCxnQkFBZ0JzQztvQkFDekQsSUFBSSxDQUFDakcsWUFBWXRDLFNBQVE7d0JBQ3JCdUQsSUFBSXNGLFFBQVFOLFdBQVd2STt3QkFDdkJxYixTQUFTOVMsV0FBV3ZGLElBQUk2RixRQUFRTjtvQkFDcEM7Z0JBQ0o7WUFDSjtZQUNBTyxjQUFjbEgsWUFBWWlIO1lBQzFCMEUsVUFBVXBFLEtBQUssQ0FBQ3NFLElBQUksQ0FBQztnQkFDakI1RSxRQUFRO29CQUFFLEdBQUdBLE1BQU07Z0JBQUM7WUFDeEI7WUFDQTBFLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjVFLFFBQVE7b0JBQUUsR0FBR0EsTUFBTTtnQkFBQztZQUN4QjtRQUNKO1FBQ0FaLFNBQVM7WUFDTDJDLE9BQU8wVCxpQkFBaUJNLGVBQWUsR0FBRzNXLE9BQU8yQyxLQUFLLEdBQUcsSUFBSTNJO1lBQzdEd1YsU0FBUyxJQUFJeFY7WUFDYmtILE9BQU8sSUFBSWxIO1lBQ1gwRSxVQUFVLElBQUkxRTtZQUNkbUcsT0FBTyxJQUFJbkc7WUFDWHVHLFVBQVU7WUFDVjRCLE9BQU87UUFDWDtRQUNBUyxPQUFPRCxLQUFLLEdBQ1IsQ0FBQ3ZFLGdCQUFnQm1CLE9BQU8sSUFDcEIsQ0FBQyxDQUFDOFcsaUJBQWlCakIsV0FBVyxJQUM5QixDQUFDLENBQUNpQixpQkFBaUJNLGVBQWU7UUFDMUMvVCxPQUFPekMsS0FBSyxHQUFHLENBQUMsQ0FBQ3NDLFNBQVN6QixnQkFBZ0I7UUFDMUNzRSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQjZKLGFBQWFnSCxpQkFBaUJXLGVBQWUsR0FDdkNsWSxXQUFXdVEsV0FBVyxHQUN0QjtZQUNOcFEsU0FBU3VYLHFCQUNILFFBQ0FILGlCQUFpQnJCLFNBQVMsR0FDdEJsVyxXQUFXRyxPQUFPLEdBQ2xCLENBQUMsQ0FBRW9YLENBQUFBLGlCQUFpQkksaUJBQWlCLElBQ25DLENBQUMvUCxVQUFVekcsWUFBWWpDLGVBQWM7WUFDakR3TyxhQUFhNkosaUJBQWlCWSxlQUFlLEdBQ3ZDblksV0FBVzBOLFdBQVcsR0FDdEI7WUFDTnJOLGFBQWFxWCxxQkFDUCxDQUFDLElBQ0RILGlCQUFpQk0sZUFBZSxHQUM1Qk4saUJBQWlCSSxpQkFBaUIsSUFBSTVWLGNBQ2xDZ0ksZUFBZTdLLGdCQUFnQjZDLGVBQy9CL0IsV0FBV0ssV0FBVyxHQUMxQmtYLGlCQUFpQkksaUJBQWlCLElBQUl4VyxhQUNsQzRJLGVBQWU3SyxnQkFBZ0JpQyxjQUMvQm9XLGlCQUFpQnJCLFNBQVMsR0FDdEJsVyxXQUFXSyxXQUFXLEdBQ3RCLENBQUM7WUFDbkJDLGVBQWVpWCxpQkFBaUJwQixXQUFXLEdBQ3JDblcsV0FBV00sYUFBYSxHQUN4QixDQUFDO1lBQ1BJLFFBQVE2VyxpQkFBaUJhLFVBQVUsR0FBR3BZLFdBQVdVLE1BQU0sR0FBRyxDQUFDO1lBQzNEaUcsb0JBQW9CNFEsaUJBQWlCYyxzQkFBc0IsR0FDckRyWSxXQUFXMkcsa0JBQWtCLEdBQzdCO1lBQ044SixjQUFjO1FBQ2xCO0lBQ0o7SUFDQSxNQUFNd0gsUUFBUSxDQUFDOVcsWUFBWW9XLG1CQUFxQkQsT0FBTzlPLFdBQVdySCxjQUM1REEsV0FBV1ksZUFDWFosWUFBWW9XO0lBQ2xCLE1BQU1lLFdBQVcsQ0FBQ3plLE1BQU1zUSxVQUFVLENBQUMsQ0FBQztRQUNoQyxNQUFNakgsUUFBUWpILElBQUlrSCxTQUFTdEo7UUFDM0IsTUFBTXVTLGlCQUFpQmxKLFNBQVNBLE1BQU1FLEVBQUU7UUFDeEMsSUFBSWdKLGdCQUFnQjtZQUNoQixNQUFNcUssV0FBV3JLLGVBQWVwQixJQUFJLEdBQzlCb0IsZUFBZXBCLElBQUksQ0FBQyxFQUFFLEdBQ3RCb0IsZUFBZXBKLEdBQUc7WUFDeEIsSUFBSXlULFNBQVNwVCxLQUFLLEVBQUU7Z0JBQ2hCb1QsU0FBU3BULEtBQUs7Z0JBQ2Q4RyxRQUFRb08sWUFBWSxJQUNoQi9QLFdBQVdpTyxTQUFTblQsTUFBTSxLQUMxQm1ULFNBQVNuVCxNQUFNO1lBQ3ZCO1FBQ0o7SUFDSjtJQUNBLE1BQU11UyxnQkFBZ0IsQ0FBQzFDO1FBQ25CblQsYUFBYTtZQUNULEdBQUdBLFVBQVU7WUFDYixHQUFHbVQsZ0JBQWdCO1FBQ3ZCO0lBQ0o7SUFDQSxNQUFNcUYsc0JBQXNCLElBQU1oUSxXQUFXN0UsU0FBUzFFLGFBQWEsS0FDL0QwRSxTQUFTMUUsYUFBYSxHQUFHd1osSUFBSSxDQUFDLENBQUMzVztZQUMzQm1XLE1BQU1uVyxRQUFRNkIsU0FBUytVLFlBQVk7WUFDbkNsUyxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFDakJ0RyxXQUFXO1lBQ2Y7UUFDSjtJQUNKLE1BQU1ULFVBQVU7UUFDWmIsU0FBUztZQUNMeUQ7WUFDQXlCO1lBQ0FzUjtZQUNBL1A7WUFDQXFCO1lBQ0FqRztZQUNBMFE7WUFDQXVGO1lBQ0EvVTtZQUNBb1E7WUFDQW5SO1lBQ0E0UTtZQUNBek47WUFDQWtPO1lBQ0F5QjtZQUNBMEQ7WUFDQWtCO1lBQ0F4VztZQUNBNlU7WUFDQXJRO1lBQ0FsSDtZQUNBLElBQUk2RCxXQUFVO2dCQUNWLE9BQU9BO1lBQ1g7WUFDQSxJQUFJcEIsZUFBYztnQkFDZCxPQUFPQTtZQUNYO1lBQ0EsSUFBSStCLFVBQVM7Z0JBQ1QsT0FBT0E7WUFDWDtZQUNBLElBQUlBLFFBQU83SyxNQUFPO2dCQUNkNkssU0FBUzdLO1lBQ2I7WUFDQSxJQUFJaUcsa0JBQWlCO2dCQUNqQixPQUFPQTtZQUNYO1lBQ0EsSUFBSWdDLFVBQVM7Z0JBQ1QsT0FBT0E7WUFDWDtZQUNBLElBQUlBLFFBQU9qSSxNQUFPO2dCQUNkaUksU0FBU2pJO1lBQ2I7WUFDQSxJQUFJK0csY0FBYTtnQkFDYixPQUFPQTtZQUNYO1lBQ0EsSUFBSTJELFlBQVc7Z0JBQ1gsT0FBT0E7WUFDWDtZQUNBLElBQUlBLFVBQVMxSyxNQUFPO2dCQUNoQjBLLFdBQVc7b0JBQ1AsR0FBR0EsUUFBUTtvQkFDWCxHQUFHMUssS0FBSztnQkFDWjtZQUNKO1FBQ0o7UUFDQXFPO1FBQ0E2TTtRQUNBNVI7UUFDQWdEO1FBQ0FsRTtRQUNBaVQ7UUFDQVg7UUFDQXNFO1FBQ0FaO1FBQ0E5QjtRQUNBdlI7UUFDQTRDO1FBQ0EwUjtRQUNBaEQ7SUFDSjtJQUNBLE9BQU87UUFDSCxHQUFHM1YsT0FBTztRQUNWZ1osYUFBYWhaO0lBQ2pCO0FBQ0o7QUFFQSxJQUFJaVosYUFBYTtJQUNiLElBQUksT0FBT0MsV0FBVyxlQUFlQSxPQUFPQyxVQUFVLEVBQUU7UUFDcEQsT0FBT0QsT0FBT0MsVUFBVTtJQUM1QjtJQUNBLE1BQU1DLElBQUksT0FBT0MsZ0JBQWdCLGNBQWM5ZixLQUFLK2YsR0FBRyxLQUFLRCxZQUFZQyxHQUFHLEtBQUs7SUFDaEYsT0FBTyx1Q0FBdUNsZCxPQUFPLENBQUMsU0FBUyxDQUFDbWQ7UUFDNUQsTUFBTUMsSUFBSSxDQUFDQyxLQUFLQyxNQUFNLEtBQUssS0FBS04sQ0FBQUEsSUFBSyxLQUFLO1FBQzFDLE9BQU8sQ0FBQ0csS0FBSyxNQUFNQyxJQUFJLElBQUssTUFBTyxHQUFFLEVBQUdHLFFBQVEsQ0FBQztJQUNyRDtBQUNKO0FBRUEsSUFBSUMsb0JBQW9CLENBQUMxZixNQUFNNEMsT0FBTzBOLFVBQVUsQ0FBQyxDQUFDLEdBQUtBLFFBQVFrTCxXQUFXLElBQUk5WixZQUFZNE8sUUFBUWtMLFdBQVcsSUFDdkdsTCxRQUFRcVAsU0FBUyxJQUNmLENBQUMsRUFBRTNmLEtBQUssQ0FBQyxFQUFFMEIsWUFBWTRPLFFBQVFzUCxVQUFVLElBQUloZCxRQUFRME4sUUFBUXNQLFVBQVUsQ0FBQyxDQUFDLENBQUMsR0FDNUU7QUFFTixJQUFJQyxXQUFXLENBQUM1ZSxNQUFNN0IsU0FBVTtXQUN6QjZCO1dBQ0FvTSxzQkFBc0JqTztLQUM1QjtBQUVELElBQUkwZ0IsaUJBQWlCLENBQUMxZ0IsU0FBVUssTUFBTUMsT0FBTyxDQUFDTixVQUFTQSxPQUFNc0ksR0FBRyxDQUFDLElBQU05RixhQUFhQTtBQUVwRixTQUFTbWUsT0FBTzllLElBQUksRUFBRTJCLEtBQUssRUFBRXhELE1BQUs7SUFDOUIsT0FBTztXQUNBNkIsS0FBS3NPLEtBQUssQ0FBQyxHQUFHM007V0FDZHlLLHNCQUFzQmpPO1dBQ3RCNkIsS0FBS3NPLEtBQUssQ0FBQzNNO0tBQ2pCO0FBQ0w7QUFFQSxJQUFJb2QsY0FBYyxDQUFDL2UsTUFBTTBXLE1BQU1zSTtJQUMzQixJQUFJLENBQUN4Z0IsTUFBTUMsT0FBTyxDQUFDdUIsT0FBTztRQUN0QixPQUFPLEVBQUU7SUFDYjtJQUNBLElBQUlTLFlBQVlULElBQUksQ0FBQ2dmLEdBQUcsR0FBRztRQUN2QmhmLElBQUksQ0FBQ2dmLEdBQUcsR0FBR3JlO0lBQ2Y7SUFDQVgsS0FBS2lmLE1BQU0sQ0FBQ0QsSUFBSSxHQUFHaGYsS0FBS2lmLE1BQU0sQ0FBQ3ZJLE1BQU0sRUFBRSxDQUFDLEVBQUU7SUFDMUMsT0FBTzFXO0FBQ1g7QUFFQSxJQUFJa2YsWUFBWSxDQUFDbGYsTUFBTTdCLFNBQVU7V0FDMUJpTyxzQkFBc0JqTztXQUN0QmlPLHNCQUFzQnBNO0tBQzVCO0FBRUQsU0FBU21mLGdCQUFnQm5mLElBQUksRUFBRW9mLE9BQU87SUFDbEMsSUFBSUMsSUFBSTtJQUNSLE1BQU1DLE9BQU87V0FBSXRmO0tBQUs7SUFDdEIsS0FBSyxNQUFNMkIsU0FBU3lkLFFBQVM7UUFDekJFLEtBQUtMLE1BQU0sQ0FBQ3RkLFFBQVEwZCxHQUFHO1FBQ3ZCQTtJQUNKO0lBQ0EsT0FBT3plLFFBQVEwZSxNQUFNemQsTUFBTSxHQUFHeWQsT0FBTyxFQUFFO0FBQzNDO0FBQ0EsSUFBSUMsZ0JBQWdCLENBQUN2ZixNQUFNMkIsUUFBVWxCLFlBQVlrQixTQUMzQyxFQUFFLEdBQ0Z3ZCxnQkFBZ0JuZixNQUFNb00sc0JBQXNCekssT0FBTzZkLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztBQUU1RSxJQUFJQyxjQUFjLENBQUMzZixNQUFNNGYsUUFBUUM7SUFDN0IsQ0FBQzdmLElBQUksQ0FBQzRmLE9BQU8sRUFBRTVmLElBQUksQ0FBQzZmLE9BQU8sQ0FBQyxHQUFHO1FBQUM3ZixJQUFJLENBQUM2ZixPQUFPO1FBQUU3ZixJQUFJLENBQUM0ZixPQUFPO0tBQUM7QUFDL0Q7QUFFQSxJQUFJRSxXQUFXLENBQUM5SSxhQUFhclYsT0FBT3hEO0lBQ2hDNlksV0FBVyxDQUFDclYsTUFBTSxHQUFHeEQ7SUFDckIsT0FBTzZZO0FBQ1g7QUFFQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBb0NDLEdBQ0QsU0FBUytJLGNBQWNyYyxLQUFLO0lBQ3hCLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxFQUFFUyxVQUFVYSxRQUFRYixPQUFPLEVBQUVqRixJQUFJLEVBQUVpaEIsVUFBVSxJQUFJLEVBQUU1WSxnQkFBZ0IsRUFBRU0sS0FBSyxFQUFHLEdBQUdoRTtJQUN0RixNQUFNLENBQUNtTCxRQUFRb1IsVUFBVSxHQUFHbmlCLDJDQUF1QixDQUFDa0csUUFBUThVLGNBQWMsQ0FBQy9aO0lBQzNFLE1BQU1taEIsTUFBTXBpQix5Q0FBcUIsQ0FBQ2tHLFFBQVE4VSxjQUFjLENBQUMvWixNQUFNMEgsR0FBRyxDQUFDcVg7SUFDbkUsTUFBTXFDLFlBQVlyaUIseUNBQXFCLENBQUMrUTtJQUN4QyxNQUFNdVIsUUFBUXRpQix5Q0FBcUIsQ0FBQ2lCO0lBQ3BDLE1BQU1zaEIsWUFBWXZpQix5Q0FBcUIsQ0FBQztJQUN4Q3NpQixNQUFNdGEsT0FBTyxHQUFHL0c7SUFDaEJvaEIsVUFBVXJhLE9BQU8sR0FBRytJO0lBQ3BCN0ssUUFBUW9DLE1BQU0sQ0FBQ2tCLEtBQUssQ0FBQ2QsR0FBRyxDQUFDekg7SUFDekIySSxTQUNJMUQsUUFBUXlELFFBQVEsQ0FBQzFJLE1BQU0ySTtJQUMzQmpELDBCQUEwQixJQUFNVCxRQUFRMEgsU0FBUyxDQUFDcEUsS0FBSyxDQUFDa0YsU0FBUyxDQUFDO1lBQzlEWixNQUFNLENBQUMsRUFBRTVFLE1BQU0sRUFBRWpJLE1BQU11aEIsY0FBYyxFQUFHO2dCQUNwQyxJQUFJQSxtQkFBbUJGLE1BQU10YSxPQUFPLElBQUksQ0FBQ3dhLGdCQUFnQjtvQkFDckQsTUFBTXRKLGNBQWM3VixJQUFJNkYsUUFBUW9aLE1BQU10YSxPQUFPO29CQUM3QyxJQUFJdEgsTUFBTUMsT0FBTyxDQUFDdVksY0FBYzt3QkFDNUJpSixVQUFVako7d0JBQ1ZrSixJQUFJcGEsT0FBTyxHQUFHa1IsWUFBWXZRLEdBQUcsQ0FBQ3FYO29CQUNsQztnQkFDSjtZQUNKO1FBQ0osR0FBR3BSLFdBQVcsRUFBRTtRQUFDMUk7S0FBUTtJQUN6QixNQUFNdWMsZUFBZXppQiw4Q0FBMEIsQ0FBQyxDQUFDMGlCO1FBQzdDSCxVQUFVdmEsT0FBTyxHQUFHO1FBQ3BCOUIsUUFBUTRTLGNBQWMsQ0FBQzdYLE1BQU15aEI7SUFDakMsR0FBRztRQUFDeGM7UUFBU2pGO0tBQUs7SUFDbEIsTUFBTWtNLFNBQVMsQ0FBQzlNLFFBQU9rUjtRQUNuQixNQUFNb1IsY0FBY3JVLHNCQUFzQnJNLFlBQVk1QjtRQUN0RCxNQUFNcWlCLDBCQUEwQjVCLFNBQVM1YSxRQUFROFUsY0FBYyxDQUFDL1osT0FBTzBoQjtRQUN2RXpjLFFBQVFvQyxNQUFNLENBQUNtQyxLQUFLLEdBQUdrVyxrQkFBa0IxZixNQUFNeWhCLHdCQUF3QjNlLE1BQU0sR0FBRyxHQUFHd047UUFDbkY2USxJQUFJcGEsT0FBTyxHQUFHOFksU0FBU3NCLElBQUlwYSxPQUFPLEVBQUUyYSxZQUFZaGEsR0FBRyxDQUFDcVg7UUFDcER5QyxhQUFhQztRQUNiUCxVQUFVTztRQUNWeGMsUUFBUTRTLGNBQWMsQ0FBQzdYLE1BQU15aEIseUJBQXlCNUIsVUFBVTtZQUM1RDNILE1BQU00SCxlQUFlMWdCO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNdWlCLFVBQVUsQ0FBQ3ZpQixRQUFPa1I7UUFDcEIsTUFBTXNSLGVBQWV2VSxzQkFBc0JyTSxZQUFZNUI7UUFDdkQsTUFBTXFpQiwwQkFBMEJ0QixVQUFVbGIsUUFBUThVLGNBQWMsQ0FBQy9aLE9BQU80aEI7UUFDeEUzYyxRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxHQUFHa1csa0JBQWtCMWYsTUFBTSxHQUFHc1E7UUFDbEQ2USxJQUFJcGEsT0FBTyxHQUFHb1osVUFBVWdCLElBQUlwYSxPQUFPLEVBQUU2YSxhQUFhbGEsR0FBRyxDQUFDcVg7UUFDdER5QyxhQUFhQztRQUNiUCxVQUFVTztRQUNWeGMsUUFBUTRTLGNBQWMsQ0FBQzdYLE1BQU15aEIseUJBQXlCdEIsV0FBVztZQUM3RGpJLE1BQU00SCxlQUFlMWdCO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNeWlCLFNBQVMsQ0FBQ2pmO1FBQ1osTUFBTTZlLDBCQUEwQmpCLGNBQWN2YixRQUFROFUsY0FBYyxDQUFDL1osT0FBTzRDO1FBQzVFdWUsSUFBSXBhLE9BQU8sR0FBR3laLGNBQWNXLElBQUlwYSxPQUFPLEVBQUVuRTtRQUN6QzRlLGFBQWFDO1FBQ2JQLFVBQVVPO1FBQ1YsQ0FBQ2hpQixNQUFNQyxPQUFPLENBQUMwQyxJQUFJNkMsUUFBUXFFLE9BQU8sRUFBRXRKLFVBQ2hDMkMsSUFBSXNDLFFBQVFxRSxPQUFPLEVBQUV0SixNQUFNNEI7UUFDL0JxRCxRQUFRNFMsY0FBYyxDQUFDN1gsTUFBTXloQix5QkFBeUJqQixlQUFlO1lBQ2pFdEksTUFBTXRWO1FBQ1Y7SUFDSjtJQUNBLE1BQU1rZixXQUFXLENBQUNsZixPQUFPeEQsUUFBT2tSO1FBQzVCLE1BQU15UixjQUFjMVUsc0JBQXNCck0sWUFBWTVCO1FBQ3RELE1BQU1xaUIsMEJBQTBCMUIsT0FBTzlhLFFBQVE4VSxjQUFjLENBQUMvWixPQUFPNEMsT0FBT21mO1FBQzVFOWMsUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssR0FBR2tXLGtCQUFrQjFmLE1BQU00QyxPQUFPME47UUFDdEQ2USxJQUFJcGEsT0FBTyxHQUFHZ1osT0FBT29CLElBQUlwYSxPQUFPLEVBQUVuRSxPQUFPbWYsWUFBWXJhLEdBQUcsQ0FBQ3FYO1FBQ3pEeUMsYUFBYUM7UUFDYlAsVUFBVU87UUFDVnhjLFFBQVE0UyxjQUFjLENBQUM3WCxNQUFNeWhCLHlCQUF5QjFCLFFBQVE7WUFDMUQ3SCxNQUFNdFY7WUFDTnVWLE1BQU0ySCxlQUFlMWdCO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNNGlCLE9BQU8sQ0FBQ25CLFFBQVFDO1FBQ2xCLE1BQU1XLDBCQUEwQnhjLFFBQVE4VSxjQUFjLENBQUMvWjtRQUN2RDRnQixZQUFZYSx5QkFBeUJaLFFBQVFDO1FBQzdDRixZQUFZTyxJQUFJcGEsT0FBTyxFQUFFOFosUUFBUUM7UUFDakNVLGFBQWFDO1FBQ2JQLFVBQVVPO1FBQ1Z4YyxRQUFRNFMsY0FBYyxDQUFDN1gsTUFBTXloQix5QkFBeUJiLGFBQWE7WUFDL0QxSSxNQUFNMkk7WUFDTjFJLE1BQU0ySTtRQUNWLEdBQUc7SUFDUDtJQUNBLE1BQU1tQixPQUFPLENBQUN0SyxNQUFNc0k7UUFDaEIsTUFBTXdCLDBCQUEwQnhjLFFBQVE4VSxjQUFjLENBQUMvWjtRQUN2RGdnQixZQUFZeUIseUJBQXlCOUosTUFBTXNJO1FBQzNDRCxZQUFZbUIsSUFBSXBhLE9BQU8sRUFBRTRRLE1BQU1zSTtRQUMvQnVCLGFBQWFDO1FBQ2JQLFVBQVVPO1FBQ1Z4YyxRQUFRNFMsY0FBYyxDQUFDN1gsTUFBTXloQix5QkFBeUJ6QixhQUFhO1lBQy9EOUgsTUFBTVA7WUFDTlEsTUFBTThIO1FBQ1YsR0FBRztJQUNQO0lBQ0EsTUFBTWlDLFNBQVMsQ0FBQ3RmLE9BQU94RDtRQUNuQixNQUFNMkksY0FBYy9HLFlBQVk1QjtRQUNoQyxNQUFNcWlCLDBCQUEwQlYsU0FBUzliLFFBQVE4VSxjQUFjLENBQUMvWixPQUFPNEMsT0FBT21GO1FBQzlFb1osSUFBSXBhLE9BQU8sR0FBRztlQUFJMGE7U0FBd0IsQ0FBQy9aLEdBQUcsQ0FBQyxDQUFDeWEsTUFBTTdCLElBQU0sQ0FBQzZCLFFBQVE3QixNQUFNMWQsUUFBUW1jLGVBQWVvQyxJQUFJcGEsT0FBTyxDQUFDdVosRUFBRTtRQUNoSGtCLGFBQWFDO1FBQ2JQLFVBQVU7ZUFBSU87U0FBd0I7UUFDdEN4YyxRQUFRNFMsY0FBYyxDQUFDN1gsTUFBTXloQix5QkFBeUJWLFVBQVU7WUFDNUQ3SSxNQUFNdFY7WUFDTnVWLE1BQU1wUTtRQUNWLEdBQUcsTUFBTTtJQUNiO0lBQ0EsTUFBTTdGLFVBQVUsQ0FBQzlDO1FBQ2IsTUFBTXFpQiwwQkFBMEJwVSxzQkFBc0JyTSxZQUFZNUI7UUFDbEUraEIsSUFBSXBhLE9BQU8sR0FBRzBhLHdCQUF3Qi9aLEdBQUcsQ0FBQ3FYO1FBQzFDeUMsYUFBYTtlQUFJQztTQUF3QjtRQUN6Q1AsVUFBVTtlQUFJTztTQUF3QjtRQUN0Q3hjLFFBQVE0UyxjQUFjLENBQUM3WCxNQUFNO2VBQUl5aEI7U0FBd0IsRUFBRSxDQUFDeGdCLE9BQVNBLE1BQU0sQ0FBQyxHQUFHLE1BQU07SUFDekY7SUFDQWxDLDRDQUF3QixDQUFDO1FBQ3JCa0csUUFBUWdGLE1BQU0sQ0FBQ0MsTUFBTSxHQUFHO1FBQ3hCeUksVUFBVTNTLE1BQU1pRixRQUFRb0MsTUFBTSxLQUMxQnBDLFFBQVEwSCxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ3pCLEdBQUc1SCxRQUFRa0IsVUFBVTtRQUN6QjtRQUNKLElBQUltYixVQUFVdmEsT0FBTyxJQUNoQixFQUFDK0ssbUJBQW1CN00sUUFBUTZFLFFBQVEsQ0FBQ2lJLElBQUksRUFBRUMsVUFBVSxJQUNsRC9NLFFBQVFrQixVQUFVLENBQUMwTixXQUFXLEtBQ2xDLENBQUMvQixtQkFBbUI3TSxRQUFRNkUsUUFBUSxDQUFDZ0ssY0FBYyxFQUFFOUIsVUFBVSxFQUFFO1lBQ2pFLElBQUkvTSxRQUFRNkUsUUFBUSxDQUFDeU4sUUFBUSxFQUFFO2dCQUMzQnRTLFFBQVF1UyxVQUFVLENBQUM7b0JBQUN4WDtpQkFBSyxFQUFFNGUsSUFBSSxDQUFDLENBQUNwYztvQkFDN0IsTUFBTXlHLFFBQVE3RyxJQUFJSSxPQUFPcUUsTUFBTSxFQUFFN0c7b0JBQ2pDLE1BQU1vaUIsZ0JBQWdCaGdCLElBQUk2QyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUU3RztvQkFDckQsSUFBSW9pQixnQkFDRSxDQUFFblosU0FBU21aLGNBQWNsakIsSUFBSSxJQUMxQitKLFNBQ0ltWixDQUFBQSxjQUFjbGpCLElBQUksS0FBSytKLE1BQU0vSixJQUFJLElBQzlCa2pCLGNBQWN6WSxPQUFPLEtBQUtWLE1BQU1VLE9BQU8sSUFDakRWLFNBQVNBLE1BQU0vSixJQUFJLEVBQUU7d0JBQ3ZCK0osUUFDTXRHLElBQUlzQyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUU3RyxNQUFNaUosU0FDckN3RyxNQUFNeEssUUFBUWtCLFVBQVUsQ0FBQ1UsTUFBTSxFQUFFN0c7d0JBQ3ZDaUYsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7NEJBQ3pCaEcsUUFBUTVCLFFBQVFrQixVQUFVLENBQUNVLE1BQU07d0JBQ3JDO29CQUNKO2dCQUNKO1lBQ0osT0FDSztnQkFDRCxNQUFNd0MsUUFBUWpILElBQUk2QyxRQUFRcUUsT0FBTyxFQUFFdEo7Z0JBQ25DLElBQUlxSixTQUNBQSxNQUFNRSxFQUFFLElBQ1IsQ0FBRXVJLENBQUFBLG1CQUFtQjdNLFFBQVE2RSxRQUFRLENBQUNnSyxjQUFjLEVBQUU5QixVQUFVLElBQzVERixtQkFBbUI3TSxRQUFRNkUsUUFBUSxDQUFDaUksSUFBSSxFQUFFQyxVQUFVLEdBQUc7b0JBQzNEdUMsY0FBY2xMLE9BQU9wRSxRQUFRb0MsTUFBTSxDQUFDdEIsUUFBUSxFQUFFZCxRQUFRaUQsV0FBVyxFQUFFakQsUUFBUTZFLFFBQVEsQ0FBQ3lILFlBQVksS0FBS2hPLGdCQUFnQkssR0FBRyxFQUFFcUIsUUFBUTZFLFFBQVEsQ0FBQzBILHlCQUF5QixFQUFFLE1BQU1vTixJQUFJLENBQUMsQ0FBQzNWLFFBQVUsQ0FBQ3dGLGNBQWN4RixVQUN2TWhFLFFBQVEwSCxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDOzRCQUN6QmhHLFFBQVFtTiwwQkFBMEIvTyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUVvQyxPQUFPako7d0JBQ3hFO2dCQUNSO1lBQ0o7UUFDSjtRQUNBaUYsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDekI3TTtZQUNBaUksUUFBUWpILFlBQVlpRSxRQUFRaUQsV0FBVztRQUMzQztRQUNBakQsUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssSUFDaEJ1SixzQkFBc0I5TixRQUFRcUUsT0FBTyxFQUFFLENBQUNILEtBQUs1SDtZQUN6QyxJQUFJMEQsUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssSUFDcEJqSSxJQUFJdVIsVUFBVSxDQUFDN04sUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssS0FDbkNMLElBQUlLLEtBQUssRUFBRTtnQkFDWEwsSUFBSUssS0FBSztnQkFDVCxPQUFPO1lBQ1g7WUFDQTtRQUNKO1FBQ0p2RSxRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxHQUFHO1FBQ3ZCdkUsUUFBUWdDLFNBQVM7UUFDakJxYSxVQUFVdmEsT0FBTyxHQUFHO0lBQ3hCLEdBQUc7UUFBQytJO1FBQVE5UDtRQUFNaUY7S0FBUTtJQUMxQmxHLDRDQUF3QixDQUFDO1FBQ3JCLENBQUNxRCxJQUFJNkMsUUFBUWlELFdBQVcsRUFBRWxJLFNBQVNpRixRQUFRNFMsY0FBYyxDQUFDN1g7UUFDMUQsT0FBTztZQUNILE1BQU0rSixnQkFBZ0IsQ0FBQy9KLE1BQU1aO2dCQUN6QixNQUFNaUssUUFBUWpILElBQUk2QyxRQUFRcUUsT0FBTyxFQUFFdEo7Z0JBQ25DLElBQUlxSixTQUFTQSxNQUFNRSxFQUFFLEVBQUU7b0JBQ25CRixNQUFNRSxFQUFFLENBQUNTLEtBQUssR0FBRzVLO2dCQUNyQjtZQUNKO1lBQ0E2RixRQUFRNkUsUUFBUSxDQUFDekIsZ0JBQWdCLElBQUlBLG1CQUMvQnBELFFBQVFrRixVQUFVLENBQUNuSyxRQUNuQitKLGNBQWMvSixNQUFNO1FBQzlCO0lBQ0osR0FBRztRQUFDQTtRQUFNaUY7UUFBU2djO1FBQVM1WTtLQUFpQjtJQUM3QyxPQUFPO1FBQ0gyWixNQUFNampCLDhDQUEwQixDQUFDaWpCLE1BQU07WUFBQ1I7WUFBY3hoQjtZQUFNaUY7U0FBUTtRQUNwRWdkLE1BQU1sakIsOENBQTBCLENBQUNrakIsTUFBTTtZQUFDVDtZQUFjeGhCO1lBQU1pRjtTQUFRO1FBQ3BFMGMsU0FBUzVpQiw4Q0FBMEIsQ0FBQzRpQixTQUFTO1lBQUNIO1lBQWN4aEI7WUFBTWlGO1NBQVE7UUFDMUVpSCxRQUFRbk4sOENBQTBCLENBQUNtTixRQUFRO1lBQUNzVjtZQUFjeGhCO1lBQU1pRjtTQUFRO1FBQ3hFNGMsUUFBUTlpQiw4Q0FBMEIsQ0FBQzhpQixRQUFRO1lBQUNMO1lBQWN4aEI7WUFBTWlGO1NBQVE7UUFDeEU4YSxRQUFRaGhCLDhDQUEwQixDQUFDK2lCLFVBQVU7WUFBQ047WUFBY3hoQjtZQUFNaUY7U0FBUTtRQUMxRWlkLFFBQVFuakIsOENBQTBCLENBQUNtakIsUUFBUTtZQUFDVjtZQUFjeGhCO1lBQU1pRjtTQUFRO1FBQ3hFL0MsU0FBU25ELDhDQUEwQixDQUFDbUQsU0FBUztZQUFDc2Y7WUFBY3hoQjtZQUFNaUY7U0FBUTtRQUMxRTZLLFFBQVEvUSwwQ0FBc0IsQ0FBQyxJQUFNK1EsT0FBT3BJLEdBQUcsQ0FBQyxDQUFDMkIsT0FBT3pHLFFBQVc7b0JBQy9ELEdBQUd5RyxLQUFLO29CQUNSLENBQUM0WCxRQUFRLEVBQUVFLElBQUlwYSxPQUFPLENBQUNuRSxNQUFNLElBQUltYztnQkFDckMsS0FBSztZQUFDalA7WUFBUW1SO1NBQVE7SUFDMUI7QUFDSjtBQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNEJDLEdBQ0QsU0FBU29CLFFBQVExZCxRQUFRLENBQUMsQ0FBQztJQUN2QixNQUFNMmQsZUFBZXZqQix5Q0FBcUIsQ0FBQzZDO0lBQzNDLE1BQU0yZ0IsVUFBVXhqQix5Q0FBcUIsQ0FBQzZDO0lBQ3RDLE1BQU0sQ0FBQ29ELFdBQVdpQixnQkFBZ0IsR0FBR2xILDJDQUF1QixDQUFDO1FBQ3pEdUgsU0FBUztRQUNUSyxjQUFjO1FBQ2RKLFdBQVdvSSxXQUFXaEssTUFBTVMsYUFBYTtRQUN6Q3lPLGFBQWE7UUFDYitDLGNBQWM7UUFDZDlKLG9CQUFvQjtRQUNwQmxHLFNBQVM7UUFDVDhQLGFBQWE7UUFDYmxRLGFBQWEsQ0FBQztRQUNkQyxlQUFlLENBQUM7UUFDaEJDLGtCQUFrQixDQUFDO1FBQ25CRyxRQUFRbEMsTUFBTWtDLE1BQU0sSUFBSSxDQUFDO1FBQ3pCZCxVQUFVcEIsTUFBTW9CLFFBQVEsSUFBSTtRQUM1QjRRLFNBQVM7UUFDVHZSLGVBQWV1SixXQUFXaEssTUFBTVMsYUFBYSxJQUN2Q3hELFlBQ0ErQyxNQUFNUyxhQUFhO0lBQzdCO0lBQ0EsSUFBSSxDQUFDa2QsYUFBYXZiLE9BQU8sRUFBRTtRQUN2QixJQUFJcEMsTUFBTW1hLFdBQVcsRUFBRTtZQUNuQndELGFBQWF2YixPQUFPLEdBQUc7Z0JBQ25CLEdBQUdwQyxNQUFNbWEsV0FBVztnQkFDcEI5WjtZQUNKO1lBQ0EsSUFBSUwsTUFBTVMsYUFBYSxJQUFJLENBQUN1SixXQUFXaEssTUFBTVMsYUFBYSxHQUFHO2dCQUN6RFQsTUFBTW1hLFdBQVcsQ0FBQ1YsS0FBSyxDQUFDelosTUFBTVMsYUFBYSxFQUFFVCxNQUFNa2EsWUFBWTtZQUNuRTtRQUNKLE9BQ0s7WUFDRCxNQUFNLEVBQUVDLFdBQVcsRUFBRSxHQUFHdlQsTUFBTSxHQUFHa0wsa0JBQWtCOVI7WUFDbkQyZCxhQUFhdmIsT0FBTyxHQUFHO2dCQUNuQixHQUFHd0UsSUFBSTtnQkFDUHZHO1lBQ0o7UUFDSjtJQUNKO0lBQ0EsTUFBTUMsVUFBVXFkLGFBQWF2YixPQUFPLENBQUM5QixPQUFPO0lBQzVDQSxRQUFRNkUsUUFBUSxHQUFHbkY7SUFDbkJlLDBCQUEwQjtRQUN0QixNQUFNOGMsTUFBTXZkLFFBQVE2QixVQUFVLENBQUM7WUFDM0I5QixXQUFXQyxRQUFRUSxlQUFlO1lBQ2xDdUIsVUFBVSxJQUFNZixnQkFBZ0I7b0JBQUUsR0FBR2hCLFFBQVFrQixVQUFVO2dCQUFDO1lBQ3hEOFYsY0FBYztRQUNsQjtRQUNBaFcsZ0JBQWdCLENBQUNoRixPQUFVO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQMFYsU0FBUztZQUNiO1FBQ0ExUixRQUFRa0IsVUFBVSxDQUFDd1EsT0FBTyxHQUFHO1FBQzdCLE9BQU82TDtJQUNYLEdBQUc7UUFBQ3ZkO0tBQVE7SUFDWmxHLDRDQUF3QixDQUFDLElBQU1rRyxRQUFRK1gsWUFBWSxDQUFDclksTUFBTW9CLFFBQVEsR0FBRztRQUFDZDtRQUFTTixNQUFNb0IsUUFBUTtLQUFDO0lBQzlGaEgsNENBQXdCLENBQUM7UUFDckIsSUFBSTRGLE1BQU1vTixJQUFJLEVBQUU7WUFDWjlNLFFBQVE2RSxRQUFRLENBQUNpSSxJQUFJLEdBQUdwTixNQUFNb04sSUFBSTtRQUN0QztRQUNBLElBQUlwTixNQUFNbVAsY0FBYyxFQUFFO1lBQ3RCN08sUUFBUTZFLFFBQVEsQ0FBQ2dLLGNBQWMsR0FBR25QLE1BQU1tUCxjQUFjO1FBQzFEO0lBQ0osR0FBRztRQUFDN087UUFBU04sTUFBTW9OLElBQUk7UUFBRXBOLE1BQU1tUCxjQUFjO0tBQUM7SUFDOUMvVSw0Q0FBd0IsQ0FBQztRQUNyQixJQUFJNEYsTUFBTWtDLE1BQU0sRUFBRTtZQUNkNUIsUUFBUXFULFVBQVUsQ0FBQzNULE1BQU1rQyxNQUFNO1lBQy9CNUIsUUFBUThYLFdBQVc7UUFDdkI7SUFDSixHQUFHO1FBQUM5WDtRQUFTTixNQUFNa0MsTUFBTTtLQUFDO0lBQzFCOUgsNENBQXdCLENBQUM7UUFDckI0RixNQUFNMEQsZ0JBQWdCLElBQ2xCcEQsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDekI1RSxRQUFRaEQsUUFBUStDLFNBQVM7UUFDN0I7SUFDUixHQUFHO1FBQUMvQztRQUFTTixNQUFNMEQsZ0JBQWdCO0tBQUM7SUFDcEN0Siw0Q0FBd0IsQ0FBQztRQUNyQixJQUFJa0csUUFBUVEsZUFBZSxDQUFDYSxPQUFPLEVBQUU7WUFDakMsTUFBTUEsVUFBVXJCLFFBQVFtVCxTQUFTO1lBQ2pDLElBQUk5UixZQUFZdEIsVUFBVXNCLE9BQU8sRUFBRTtnQkFDL0JyQixRQUFRMEgsU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztvQkFDekJ2RztnQkFDSjtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQUNyQjtRQUFTRCxVQUFVc0IsT0FBTztLQUFDO0lBQy9CdkgsNENBQXdCLENBQUM7UUFDckIsSUFBSTRGLE1BQU1zRCxNQUFNLElBQUksQ0FBQzhGLFVBQVVwSixNQUFNc0QsTUFBTSxFQUFFc2EsUUFBUXhiLE9BQU8sR0FBRztZQUMzRDlCLFFBQVF3WSxNQUFNLENBQUM5WSxNQUFNc0QsTUFBTSxFQUFFaEQsUUFBUTZFLFFBQVEsQ0FBQytVLFlBQVk7WUFDMUQwRCxRQUFReGIsT0FBTyxHQUFHcEMsTUFBTXNELE1BQU07WUFDOUJoQyxnQkFBZ0IsQ0FBQzJHLFFBQVc7b0JBQUUsR0FBR0EsS0FBSztnQkFBQztRQUMzQyxPQUNLO1lBQ0QzSCxRQUFRMFosbUJBQW1CO1FBQy9CO0lBQ0osR0FBRztRQUFDMVo7UUFBU04sTUFBTXNELE1BQU07S0FBQztJQUMxQmxKLDRDQUF3QixDQUFDO1FBQ3JCLElBQUksQ0FBQ2tHLFFBQVFnRixNQUFNLENBQUNELEtBQUssRUFBRTtZQUN2Qi9FLFFBQVFnQyxTQUFTO1lBQ2pCaEMsUUFBUWdGLE1BQU0sQ0FBQ0QsS0FBSyxHQUFHO1FBQzNCO1FBQ0EsSUFBSS9FLFFBQVFnRixNQUFNLENBQUN6QyxLQUFLLEVBQUU7WUFDdEJ2QyxRQUFRZ0YsTUFBTSxDQUFDekMsS0FBSyxHQUFHO1lBQ3ZCdkMsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQUUsR0FBRzVILFFBQVFrQixVQUFVO1lBQUM7UUFDekQ7UUFDQWxCLFFBQVFrRCxnQkFBZ0I7SUFDNUI7SUFDQW1hLGFBQWF2YixPQUFPLENBQUMvQixTQUFTLEdBQUdELGtCQUFrQkMsV0FBV0M7SUFDOUQsT0FBT3FkLGFBQWF2YixPQUFPO0FBQy9CO0FBRW9LLENBQ3BLLHNDQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ttYS1zY2hlZHVsZS1uZ29zYW5nbnMvLi9ub2RlX21vZHVsZXMvcmVhY3QtaG9vay1mb3JtL2Rpc3QvaW5kZXguZXNtLm1qcz85ZGMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSZWFjdF9fZGVmYXVsdCBmcm9tICdyZWFjdCc7XG5cbnZhciBpc0NoZWNrQm94SW5wdXQgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSAnY2hlY2tib3gnO1xuXG52YXIgaXNEYXRlT2JqZWN0ID0gKHZhbHVlKSA9PiB2YWx1ZSBpbnN0YW5jZW9mIERhdGU7XG5cbnZhciBpc051bGxPclVuZGVmaW5lZCA9ICh2YWx1ZSkgPT4gdmFsdWUgPT0gbnVsbDtcblxuY29uc3QgaXNPYmplY3RUeXBlID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnO1xudmFyIGlzT2JqZWN0ID0gKHZhbHVlKSA9PiAhaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpICYmXG4gICAgIUFycmF5LmlzQXJyYXkodmFsdWUpICYmXG4gICAgaXNPYmplY3RUeXBlKHZhbHVlKSAmJlxuICAgICFpc0RhdGVPYmplY3QodmFsdWUpO1xuXG52YXIgZ2V0RXZlbnRWYWx1ZSA9IChldmVudCkgPT4gaXNPYmplY3QoZXZlbnQpICYmIGV2ZW50LnRhcmdldFxuICAgID8gaXNDaGVja0JveElucHV0KGV2ZW50LnRhcmdldClcbiAgICAgICAgPyBldmVudC50YXJnZXQuY2hlY2tlZFxuICAgICAgICA6IGV2ZW50LnRhcmdldC52YWx1ZVxuICAgIDogZXZlbnQ7XG5cbnZhciBnZXROb2RlUGFyZW50TmFtZSA9IChuYW1lKSA9PiBuYW1lLnN1YnN0cmluZygwLCBuYW1lLnNlYXJjaCgvXFwuXFxkKyhcXC58JCkvKSkgfHwgbmFtZTtcblxudmFyIGlzTmFtZUluRmllbGRBcnJheSA9IChuYW1lcywgbmFtZSkgPT4gbmFtZXMuaGFzKGdldE5vZGVQYXJlbnROYW1lKG5hbWUpKTtcblxudmFyIGlzUGxhaW5PYmplY3QgPSAodGVtcE9iamVjdCkgPT4ge1xuICAgIGNvbnN0IHByb3RvdHlwZUNvcHkgPSB0ZW1wT2JqZWN0LmNvbnN0cnVjdG9yICYmIHRlbXBPYmplY3QuY29uc3RydWN0b3IucHJvdG90eXBlO1xuICAgIHJldHVybiAoaXNPYmplY3QocHJvdG90eXBlQ29weSkgJiYgcHJvdG90eXBlQ29weS5oYXNPd25Qcm9wZXJ0eSgnaXNQcm90b3R5cGVPZicpKTtcbn07XG5cbnZhciBpc1dlYiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmXG4gICAgdHlwZW9mIHdpbmRvdy5IVE1MRWxlbWVudCAhPT0gJ3VuZGVmaW5lZCcgJiZcbiAgICB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnO1xuXG5mdW5jdGlvbiBjbG9uZU9iamVjdChkYXRhKSB7XG4gICAgbGV0IGNvcHk7XG4gICAgY29uc3QgaXNBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSk7XG4gICAgY29uc3QgaXNGaWxlTGlzdEluc3RhbmNlID0gdHlwZW9mIEZpbGVMaXN0ICE9PSAndW5kZWZpbmVkJyA/IGRhdGEgaW5zdGFuY2VvZiBGaWxlTGlzdCA6IGZhbHNlO1xuICAgIGlmIChkYXRhIGluc3RhbmNlb2YgRGF0ZSkge1xuICAgICAgICBjb3B5ID0gbmV3IERhdGUoZGF0YSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKGRhdGEgaW5zdGFuY2VvZiBTZXQpIHtcbiAgICAgICAgY29weSA9IG5ldyBTZXQoZGF0YSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKCEoaXNXZWIgJiYgKGRhdGEgaW5zdGFuY2VvZiBCbG9iIHx8IGlzRmlsZUxpc3RJbnN0YW5jZSkpICYmXG4gICAgICAgIChpc0FycmF5IHx8IGlzT2JqZWN0KGRhdGEpKSkge1xuICAgICAgICBjb3B5ID0gaXNBcnJheSA/IFtdIDoge307XG4gICAgICAgIGlmICghaXNBcnJheSAmJiAhaXNQbGFpbk9iamVjdChkYXRhKSkge1xuICAgICAgICAgICAgY29weSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgICAgICAgICAgICAgaWYgKGRhdGEuaGFzT3duUHJvcGVydHkoa2V5KSkge1xuICAgICAgICAgICAgICAgICAgICBjb3B5W2tleV0gPSBjbG9uZU9iamVjdChkYXRhW2tleV0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgfVxuICAgIHJldHVybiBjb3B5O1xufVxuXG52YXIgaXNLZXkgPSAodmFsdWUpID0+IC9eXFx3KiQvLnRlc3QodmFsdWUpO1xuXG52YXIgaXNVbmRlZmluZWQgPSAodmFsKSA9PiB2YWwgPT09IHVuZGVmaW5lZDtcblxudmFyIGNvbXBhY3QgPSAodmFsdWUpID0+IEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUuZmlsdGVyKEJvb2xlYW4pIDogW107XG5cbnZhciBzdHJpbmdUb1BhdGggPSAoaW5wdXQpID0+IGNvbXBhY3QoaW5wdXQucmVwbGFjZSgvW1wifCddfFxcXS9nLCAnJykuc3BsaXQoL1xcLnxcXFsvKSk7XG5cbnZhciBnZXQgPSAob2JqZWN0LCBwYXRoLCBkZWZhdWx0VmFsdWUpID0+IHtcbiAgICBpZiAoIXBhdGggfHwgIWlzT2JqZWN0KG9iamVjdCkpIHtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHRWYWx1ZTtcbiAgICB9XG4gICAgY29uc3QgcmVzdWx0ID0gKGlzS2V5KHBhdGgpID8gW3BhdGhdIDogc3RyaW5nVG9QYXRoKHBhdGgpKS5yZWR1Y2UoKHJlc3VsdCwga2V5KSA9PiBpc051bGxPclVuZGVmaW5lZChyZXN1bHQpID8gcmVzdWx0IDogcmVzdWx0W2tleV0sIG9iamVjdCk7XG4gICAgcmV0dXJuIGlzVW5kZWZpbmVkKHJlc3VsdCkgfHwgcmVzdWx0ID09PSBvYmplY3RcbiAgICAgICAgPyBpc1VuZGVmaW5lZChvYmplY3RbcGF0aF0pXG4gICAgICAgICAgICA/IGRlZmF1bHRWYWx1ZVxuICAgICAgICAgICAgOiBvYmplY3RbcGF0aF1cbiAgICAgICAgOiByZXN1bHQ7XG59O1xuXG52YXIgaXNCb29sZWFuID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJztcblxudmFyIHNldCA9IChvYmplY3QsIHBhdGgsIHZhbHVlKSA9PiB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgY29uc3QgdGVtcFBhdGggPSBpc0tleShwYXRoKSA/IFtwYXRoXSA6IHN0cmluZ1RvUGF0aChwYXRoKTtcbiAgICBjb25zdCBsZW5ndGggPSB0ZW1wUGF0aC5sZW5ndGg7XG4gICAgY29uc3QgbGFzdEluZGV4ID0gbGVuZ3RoIC0gMTtcbiAgICB3aGlsZSAoKytpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICBjb25zdCBrZXkgPSB0ZW1wUGF0aFtpbmRleF07XG4gICAgICAgIGxldCBuZXdWYWx1ZSA9IHZhbHVlO1xuICAgICAgICBpZiAoaW5kZXggIT09IGxhc3RJbmRleCkge1xuICAgICAgICAgICAgY29uc3Qgb2JqVmFsdWUgPSBvYmplY3Rba2V5XTtcbiAgICAgICAgICAgIG5ld1ZhbHVlID1cbiAgICAgICAgICAgICAgICBpc09iamVjdChvYmpWYWx1ZSkgfHwgQXJyYXkuaXNBcnJheShvYmpWYWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgPyBvYmpWYWx1ZVxuICAgICAgICAgICAgICAgICAgICA6ICFpc05hTigrdGVtcFBhdGhbaW5kZXggKyAxXSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gW11cbiAgICAgICAgICAgICAgICAgICAgICAgIDoge307XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGtleSA9PT0gJ19fcHJvdG9fXycgfHwga2V5ID09PSAnY29uc3RydWN0b3InIHx8IGtleSA9PT0gJ3Byb3RvdHlwZScpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBvYmplY3Rba2V5XSA9IG5ld1ZhbHVlO1xuICAgICAgICBvYmplY3QgPSBvYmplY3Rba2V5XTtcbiAgICB9XG59O1xuXG5jb25zdCBFVkVOVFMgPSB7XG4gICAgQkxVUjogJ2JsdXInLFxuICAgIEZPQ1VTX09VVDogJ2ZvY3Vzb3V0JyxcbiAgICBDSEFOR0U6ICdjaGFuZ2UnLFxufTtcbmNvbnN0IFZBTElEQVRJT05fTU9ERSA9IHtcbiAgICBvbkJsdXI6ICdvbkJsdXInLFxuICAgIG9uQ2hhbmdlOiAnb25DaGFuZ2UnLFxuICAgIG9uU3VibWl0OiAnb25TdWJtaXQnLFxuICAgIG9uVG91Y2hlZDogJ29uVG91Y2hlZCcsXG4gICAgYWxsOiAnYWxsJyxcbn07XG5jb25zdCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTID0ge1xuICAgIG1heDogJ21heCcsXG4gICAgbWluOiAnbWluJyxcbiAgICBtYXhMZW5ndGg6ICdtYXhMZW5ndGgnLFxuICAgIG1pbkxlbmd0aDogJ21pbkxlbmd0aCcsXG4gICAgcGF0dGVybjogJ3BhdHRlcm4nLFxuICAgIHJlcXVpcmVkOiAncmVxdWlyZWQnLFxuICAgIHZhbGlkYXRlOiAndmFsaWRhdGUnLFxufTtcblxuY29uc3QgSG9va0Zvcm1Db250ZXh0ID0gUmVhY3RfX2RlZmF1bHQuY3JlYXRlQ29udGV4dChudWxsKTtcbkhvb2tGb3JtQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdIb29rRm9ybUNvbnRleHQnO1xuLyoqXG4gKiBUaGlzIGN1c3RvbSBob29rIGFsbG93cyB5b3UgdG8gYWNjZXNzIHRoZSBmb3JtIGNvbnRleHQuIHVzZUZvcm1Db250ZXh0IGlzIGludGVuZGVkIHRvIGJlIHVzZWQgaW4gZGVlcGx5IG5lc3RlZCBzdHJ1Y3R1cmVzLCB3aGVyZSBpdCB3b3VsZCBiZWNvbWUgaW5jb252ZW5pZW50IHRvIHBhc3MgdGhlIGNvbnRleHQgYXMgYSBwcm9wLiBUbyBiZSB1c2VkIHdpdGgge0BsaW5rIEZvcm1Qcm92aWRlcn0uXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWZvcm1jb250ZXh0KSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tdjctZm9ybS1jb250ZXh0LXl0dWRpKVxuICpcbiAqIEByZXR1cm5zIHJldHVybiBhbGwgdXNlRm9ybSBtZXRob2RzXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCBtZXRob2RzID0gdXNlRm9ybSgpO1xuICogICBjb25zdCBvblN1Ym1pdCA9IGRhdGEgPT4gY29uc29sZS5sb2coZGF0YSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxGb3JtUHJvdmlkZXIgey4uLm1ldGhvZHN9ID5cbiAqICAgICAgIDxmb3JtIG9uU3VibWl0PXttZXRob2RzLmhhbmRsZVN1Ym1pdChvblN1Ym1pdCl9PlxuICogICAgICAgICA8TmVzdGVkSW5wdXQgLz5cbiAqICAgICAgICAgPGlucHV0IHR5cGU9XCJzdWJtaXRcIiAvPlxuICogICAgICAgPC9mb3JtPlxuICogICAgIDwvRm9ybVByb3ZpZGVyPlxuICogICApO1xuICogfVxuICpcbiAqICBmdW5jdGlvbiBOZXN0ZWRJbnB1dCgpIHtcbiAqICAgY29uc3QgeyByZWdpc3RlciB9ID0gdXNlRm9ybUNvbnRleHQoKTsgLy8gcmV0cmlldmUgYWxsIGhvb2sgbWV0aG9kc1xuICogICByZXR1cm4gPGlucHV0IHsuLi5yZWdpc3RlcihcInRlc3RcIil9IC8+O1xuICogfVxuICogYGBgXG4gKi9cbmNvbnN0IHVzZUZvcm1Db250ZXh0ID0gKCkgPT4gUmVhY3RfX2RlZmF1bHQudXNlQ29udGV4dChIb29rRm9ybUNvbnRleHQpO1xuLyoqXG4gKiBBIHByb3ZpZGVyIGNvbXBvbmVudCB0aGF0IHByb3BhZ2F0ZXMgdGhlIGB1c2VGb3JtYCBtZXRob2RzIHRvIGFsbCBjaGlsZHJlbiBjb21wb25lbnRzIHZpYSBbUmVhY3QgQ29udGV4dF0oaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL2NvbnRleHQuaHRtbCkgQVBJLiBUbyBiZSB1c2VkIHdpdGgge0BsaW5rIHVzZUZvcm1Db250ZXh0fS5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybWNvbnRleHQpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS12Ny1mb3JtLWNvbnRleHQteXR1ZGkpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gYWxsIHVzZUZvcm0gbWV0aG9kc1xuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm0oKTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSBkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Rm9ybVByb3ZpZGVyIHsuLi5tZXRob2RzfSA+XG4gKiAgICAgICA8Zm9ybSBvblN1Ym1pdD17bWV0aG9kcy5oYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgICAgPE5lc3RlZElucHV0IC8+XG4gKiAgICAgICAgIDxpbnB1dCB0eXBlPVwic3VibWl0XCIgLz5cbiAqICAgICAgIDwvZm9ybT5cbiAqICAgICA8L0Zvcm1Qcm92aWRlcj5cbiAqICAgKTtcbiAqIH1cbiAqXG4gKiAgZnVuY3Rpb24gTmVzdGVkSW5wdXQoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIgfSA9IHVzZUZvcm1Db250ZXh0KCk7IC8vIHJldHJpZXZlIGFsbCBob29rIG1ldGhvZHNcbiAqICAgcmV0dXJuIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJ0ZXN0XCIpfSAvPjtcbiAqIH1cbiAqIGBgYFxuICovXG5jb25zdCBGb3JtUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgICBjb25zdCB7IGNoaWxkcmVuLCAuLi5kYXRhIH0gPSBwcm9wcztcbiAgICByZXR1cm4gKFJlYWN0X19kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoSG9va0Zvcm1Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBkYXRhIH0sIGNoaWxkcmVuKSk7XG59O1xuXG52YXIgZ2V0UHJveHlGb3JtU3RhdGUgPSAoZm9ybVN0YXRlLCBjb250cm9sLCBsb2NhbFByb3h5Rm9ybVN0YXRlLCBpc1Jvb3QgPSB0cnVlKSA9PiB7XG4gICAgY29uc3QgcmVzdWx0ID0ge1xuICAgICAgICBkZWZhdWx0VmFsdWVzOiBjb250cm9sLl9kZWZhdWx0VmFsdWVzLFxuICAgIH07XG4gICAgZm9yIChjb25zdCBrZXkgaW4gZm9ybVN0YXRlKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShyZXN1bHQsIGtleSwge1xuICAgICAgICAgICAgZ2V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgX2tleSA9IGtleTtcbiAgICAgICAgICAgICAgICBpZiAoY29udHJvbC5fcHJveHlGb3JtU3RhdGVbX2tleV0gIT09IFZBTElEQVRJT05fTU9ERS5hbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbC5fcHJveHlGb3JtU3RhdGVbX2tleV0gPSAhaXNSb290IHx8IFZBTElEQVRJT05fTU9ERS5hbGw7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxvY2FsUHJveHlGb3JtU3RhdGUgJiYgKGxvY2FsUHJveHlGb3JtU3RhdGVbX2tleV0gPSB0cnVlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZm9ybVN0YXRlW19rZXldO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59O1xuXG5jb25zdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiBSZWFjdC51c2VFZmZlY3Q7XG5cbi8qKlxuICogVGhpcyBjdXN0b20gaG9vayBhbGxvd3MgeW91IHRvIHN1YnNjcmliZSB0byBlYWNoIGZvcm0gc3RhdGUsIGFuZCBpc29sYXRlIHRoZSByZS1yZW5kZXIgYXQgdGhlIGN1c3RvbSBob29rIGxldmVsLiBJdCBoYXMgaXRzIHNjb3BlIGluIHRlcm1zIG9mIGZvcm0gc3RhdGUgc3Vic2NyaXB0aW9uLCBzbyBpdCB3b3VsZCBub3QgYWZmZWN0IG90aGVyIHVzZUZvcm1TdGF0ZSBhbmQgdXNlRm9ybS4gVXNpbmcgdGhpcyBob29rIGNhbiByZWR1Y2UgdGhlIHJlLXJlbmRlciBpbXBhY3Qgb24gbGFyZ2UgYW5kIGNvbXBsZXggZm9ybSBhcHBsaWNhdGlvbi5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybXN0YXRlKSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy91c2Vmb3Jtc3RhdGUtNzV4bHkpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gaW5jbHVkZSBvcHRpb25zIG9uIHNwZWNpZnkgZmllbGRzIHRvIHN1YnNjcmliZS4ge0BsaW5rIFVzZUZvcm1TdGF0ZVJldHVybn1cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBmdW5jdGlvbiBBcHAoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIsIGhhbmRsZVN1Ym1pdCwgY29udHJvbCB9ID0gdXNlRm9ybSh7XG4gKiAgICAgZGVmYXVsdFZhbHVlczoge1xuICogICAgIGZpcnN0TmFtZTogXCJmaXJzdE5hbWVcIlxuICogICB9fSk7XG4gKiAgIGNvbnN0IHsgZGlydHlGaWVsZHMgfSA9IHVzZUZvcm1TdGF0ZSh7XG4gKiAgICAgY29udHJvbFxuICogICB9KTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSAoZGF0YSkgPT4gY29uc29sZS5sb2coZGF0YSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJmaXJzdE5hbWVcIil9IHBsYWNlaG9sZGVyPVwiRmlyc3QgTmFtZVwiIC8+XG4gKiAgICAgICB7ZGlydHlGaWVsZHMuZmlyc3ROYW1lICYmIDxwPkZpZWxkIGlzIGRpcnR5LjwvcD59XG4gKiAgICAgICA8aW5wdXQgdHlwZT1cInN1Ym1pdFwiIC8+XG4gKiAgICAgPC9mb3JtPlxuICogICApO1xuICogfVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIHVzZUZvcm1TdGF0ZShwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IHsgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgZGlzYWJsZWQsIG5hbWUsIGV4YWN0IH0gPSBwcm9wcyB8fCB7fTtcbiAgICBjb25zdCBbZm9ybVN0YXRlLCB1cGRhdGVGb3JtU3RhdGVdID0gUmVhY3RfX2RlZmF1bHQudXNlU3RhdGUoY29udHJvbC5fZm9ybVN0YXRlKTtcbiAgICBjb25zdCBfbG9jYWxQcm94eUZvcm1TdGF0ZSA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZih7XG4gICAgICAgIGlzRGlydHk6IGZhbHNlLFxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICBkaXJ0eUZpZWxkczogZmFsc2UsXG4gICAgICAgIHRvdWNoZWRGaWVsZHM6IGZhbHNlLFxuICAgICAgICB2YWxpZGF0aW5nRmllbGRzOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIGVycm9yczogZmFsc2UsXG4gICAgfSk7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiBjb250cm9sLl9zdWJzY3JpYmUoe1xuICAgICAgICBuYW1lLFxuICAgICAgICBmb3JtU3RhdGU6IF9sb2NhbFByb3h5Rm9ybVN0YXRlLmN1cnJlbnQsXG4gICAgICAgIGV4YWN0LFxuICAgICAgICBjYWxsYmFjazogKGZvcm1TdGF0ZSkgPT4ge1xuICAgICAgICAgICAgIWRpc2FibGVkICYmXG4gICAgICAgICAgICAgICAgdXBkYXRlRm9ybVN0YXRlKHtcbiAgICAgICAgICAgICAgICAgICAgLi4uY29udHJvbC5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgICAgICAuLi5mb3JtU3RhdGUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgfSksIFtuYW1lLCBkaXNhYmxlZCwgZXhhY3RdKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBfbG9jYWxQcm94eUZvcm1TdGF0ZS5jdXJyZW50LmlzVmFsaWQgJiYgY29udHJvbC5fc2V0VmFsaWQodHJ1ZSk7XG4gICAgfSwgW2NvbnRyb2xdKTtcbiAgICByZXR1cm4gUmVhY3RfX2RlZmF1bHQudXNlTWVtbygoKSA9PiBnZXRQcm94eUZvcm1TdGF0ZShmb3JtU3RhdGUsIGNvbnRyb2wsIF9sb2NhbFByb3h5Rm9ybVN0YXRlLmN1cnJlbnQsIGZhbHNlKSwgW2Zvcm1TdGF0ZSwgY29udHJvbF0pO1xufVxuXG52YXIgaXNTdHJpbmcgPSAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZyc7XG5cbnZhciBnZW5lcmF0ZVdhdGNoT3V0cHV0ID0gKG5hbWVzLCBfbmFtZXMsIGZvcm1WYWx1ZXMsIGlzR2xvYmFsLCBkZWZhdWx0VmFsdWUpID0+IHtcbiAgICBpZiAoaXNTdHJpbmcobmFtZXMpKSB7XG4gICAgICAgIGlzR2xvYmFsICYmIF9uYW1lcy53YXRjaC5hZGQobmFtZXMpO1xuICAgICAgICByZXR1cm4gZ2V0KGZvcm1WYWx1ZXMsIG5hbWVzLCBkZWZhdWx0VmFsdWUpO1xuICAgIH1cbiAgICBpZiAoQXJyYXkuaXNBcnJheShuYW1lcykpIHtcbiAgICAgICAgcmV0dXJuIG5hbWVzLm1hcCgoZmllbGROYW1lKSA9PiAoaXNHbG9iYWwgJiYgX25hbWVzLndhdGNoLmFkZChmaWVsZE5hbWUpLCBnZXQoZm9ybVZhbHVlcywgZmllbGROYW1lKSkpO1xuICAgIH1cbiAgICBpc0dsb2JhbCAmJiAoX25hbWVzLndhdGNoQWxsID0gdHJ1ZSk7XG4gICAgcmV0dXJuIGZvcm1WYWx1ZXM7XG59O1xuXG4vKipcbiAqIEN1c3RvbSBob29rIHRvIHN1YnNjcmliZSB0byBmaWVsZCBjaGFuZ2UgYW5kIGlzb2xhdGUgcmUtcmVuZGVyaW5nIGF0IHRoZSBjb21wb25lbnQgbGV2ZWwuXG4gKlxuICogQHJlbWFya3NcbiAqXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2V3YXRjaCkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXY3LXRzLXVzZXdhdGNoLWg5aTVlKVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGNvbnN0IHsgY29udHJvbCB9ID0gdXNlRm9ybSgpO1xuICogY29uc3QgdmFsdWVzID0gdXNlV2F0Y2goe1xuICogICBuYW1lOiBcImZpZWxkTmFtZVwiXG4gKiAgIGNvbnRyb2wsXG4gKiB9KVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIHVzZVdhdGNoKHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgeyBjb250cm9sID0gbWV0aG9kcy5jb250cm9sLCBuYW1lLCBkZWZhdWx0VmFsdWUsIGRpc2FibGVkLCBleGFjdCwgfSA9IHByb3BzIHx8IHt9O1xuICAgIGNvbnN0IF9kZWZhdWx0VmFsdWUgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYoZGVmYXVsdFZhbHVlKTtcbiAgICBjb25zdCBbdmFsdWUsIHVwZGF0ZVZhbHVlXSA9IFJlYWN0X19kZWZhdWx0LnVzZVN0YXRlKGNvbnRyb2wuX2dldFdhdGNoKG5hbWUsIF9kZWZhdWx0VmFsdWUuY3VycmVudCkpO1xuICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4gY29udHJvbC5fc3Vic2NyaWJlKHtcbiAgICAgICAgbmFtZSxcbiAgICAgICAgZm9ybVN0YXRlOiB7XG4gICAgICAgICAgICB2YWx1ZXM6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICAgIGV4YWN0LFxuICAgICAgICBjYWxsYmFjazogKGZvcm1TdGF0ZSkgPT4gIWRpc2FibGVkICYmXG4gICAgICAgICAgICB1cGRhdGVWYWx1ZShnZW5lcmF0ZVdhdGNoT3V0cHV0KG5hbWUsIGNvbnRyb2wuX25hbWVzLCBmb3JtU3RhdGUudmFsdWVzIHx8IGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIGZhbHNlLCBfZGVmYXVsdFZhbHVlLmN1cnJlbnQpKSxcbiAgICB9KSwgW25hbWUsIGNvbnRyb2wsIGRpc2FibGVkLCBleGFjdF0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiBjb250cm9sLl9yZW1vdmVVbm1vdW50ZWQoKSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuXG4vKipcbiAqIEN1c3RvbSBob29rIHRvIHdvcmsgd2l0aCBjb250cm9sbGVkIGNvbXBvbmVudCwgdGhpcyBmdW5jdGlvbiBwcm92aWRlIHlvdSB3aXRoIGJvdGggZm9ybSBhbmQgZmllbGQgbGV2ZWwgc3RhdGUuIFJlLXJlbmRlciBpcyBpc29sYXRlZCBhdCB0aGUgaG9vayBsZXZlbC5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlY29udHJvbGxlcikg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvdXNlY29udHJvbGxlci0wbzhweClcbiAqXG4gKiBAcGFyYW0gcHJvcHMgLSB0aGUgcGF0aCBuYW1lIHRvIHRoZSBmb3JtIGZpZWxkIHZhbHVlLCBhbmQgdmFsaWRhdGlvbiBydWxlcy5cbiAqXG4gKiBAcmV0dXJucyBmaWVsZCBwcm9wZXJ0aWVzLCBmaWVsZCBhbmQgZm9ybSBzdGF0ZS4ge0BsaW5rIFVzZUNvbnRyb2xsZXJSZXR1cm59XG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gSW5wdXQocHJvcHMpIHtcbiAqICAgY29uc3QgeyBmaWVsZCwgZmllbGRTdGF0ZSwgZm9ybVN0YXRlIH0gPSB1c2VDb250cm9sbGVyKHByb3BzKTtcbiAqICAgcmV0dXJuIChcbiAqICAgICA8ZGl2PlxuICogICAgICAgPGlucHV0IHsuLi5maWVsZH0gcGxhY2Vob2xkZXI9e3Byb3BzLm5hbWV9IC8+XG4gKiAgICAgICA8cD57ZmllbGRTdGF0ZS5pc1RvdWNoZWQgJiYgXCJUb3VjaGVkXCJ9PC9wPlxuICogICAgICAgPHA+e2Zvcm1TdGF0ZS5pc1N1Ym1pdHRlZCA/IFwic3VibWl0dGVkXCIgOiBcIlwifTwvcD5cbiAqICAgICA8L2Rpdj5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VDb250cm9sbGVyKHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgeyBuYW1lLCBkaXNhYmxlZCwgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgc2hvdWxkVW5yZWdpc3RlciB9ID0gcHJvcHM7XG4gICAgY29uc3QgaXNBcnJheUZpZWxkID0gaXNOYW1lSW5GaWVsZEFycmF5KGNvbnRyb2wuX25hbWVzLmFycmF5LCBuYW1lKTtcbiAgICBjb25zdCB2YWx1ZSA9IHVzZVdhdGNoKHtcbiAgICAgICAgY29udHJvbCxcbiAgICAgICAgbmFtZSxcbiAgICAgICAgZGVmYXVsdFZhbHVlOiBnZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSwgZ2V0KGNvbnRyb2wuX2RlZmF1bHRWYWx1ZXMsIG5hbWUsIHByb3BzLmRlZmF1bHRWYWx1ZSkpLFxuICAgICAgICBleGFjdDogdHJ1ZSxcbiAgICB9KTtcbiAgICBjb25zdCBmb3JtU3RhdGUgPSB1c2VGb3JtU3RhdGUoe1xuICAgICAgICBjb250cm9sLFxuICAgICAgICBuYW1lLFxuICAgICAgICBleGFjdDogdHJ1ZSxcbiAgICB9KTtcbiAgICBjb25zdCBfcHJvcHMgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYocHJvcHMpO1xuICAgIGNvbnN0IF9yZWdpc3RlclByb3BzID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKGNvbnRyb2wucmVnaXN0ZXIobmFtZSwge1xuICAgICAgICAuLi5wcm9wcy5ydWxlcyxcbiAgICAgICAgdmFsdWUsXG4gICAgICAgIC4uLihpc0Jvb2xlYW4ocHJvcHMuZGlzYWJsZWQpID8geyBkaXNhYmxlZDogcHJvcHMuZGlzYWJsZWQgfSA6IHt9KSxcbiAgICB9KSk7XG4gICAgY29uc3QgZmllbGRTdGF0ZSA9IFJlYWN0X19kZWZhdWx0LnVzZU1lbW8oKCkgPT4gT2JqZWN0LmRlZmluZVByb3BlcnRpZXMoe30sIHtcbiAgICAgICAgaW52YWxpZDoge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGdldDogKCkgPT4gISFnZXQoZm9ybVN0YXRlLmVycm9ycywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgICAgIGlzRGlydHk6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgICAgIGlzVG91Y2hlZDoge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGdldDogKCkgPT4gISFnZXQoZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpLFxuICAgICAgICB9LFxuICAgICAgICBpc1ZhbGlkYXRpbmc6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLCBuYW1lKSxcbiAgICAgICAgfSxcbiAgICAgICAgZXJyb3I6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+IGdldChmb3JtU3RhdGUuZXJyb3JzLCBuYW1lKSxcbiAgICAgICAgfSxcbiAgICB9KSwgW2Zvcm1TdGF0ZSwgbmFtZV0pO1xuICAgIGNvbnN0IG9uQ2hhbmdlID0gUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soKGV2ZW50KSA9PiBfcmVnaXN0ZXJQcm9wcy5jdXJyZW50Lm9uQ2hhbmdlKHtcbiAgICAgICAgdGFyZ2V0OiB7XG4gICAgICAgICAgICB2YWx1ZTogZ2V0RXZlbnRWYWx1ZShldmVudCksXG4gICAgICAgICAgICBuYW1lOiBuYW1lLFxuICAgICAgICB9LFxuICAgICAgICB0eXBlOiBFVkVOVFMuQ0hBTkdFLFxuICAgIH0pLCBbbmFtZV0pO1xuICAgIGNvbnN0IG9uQmx1ciA9IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKCgpID0+IF9yZWdpc3RlclByb3BzLmN1cnJlbnQub25CbHVyKHtcbiAgICAgICAgdGFyZ2V0OiB7XG4gICAgICAgICAgICB2YWx1ZTogZ2V0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIG5hbWUpLFxuICAgICAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgfSxcbiAgICAgICAgdHlwZTogRVZFTlRTLkJMVVIsXG4gICAgfSksIFtuYW1lLCBjb250cm9sLl9mb3JtVmFsdWVzXSk7XG4gICAgY29uc3QgcmVmID0gUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soKGVsbSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpO1xuICAgICAgICBpZiAoZmllbGQgJiYgZWxtKSB7XG4gICAgICAgICAgICBmaWVsZC5fZi5yZWYgPSB7XG4gICAgICAgICAgICAgICAgZm9jdXM6ICgpID0+IGVsbS5mb2N1cyAmJiBlbG0uZm9jdXMoKSxcbiAgICAgICAgICAgICAgICBzZWxlY3Q6ICgpID0+IGVsbS5zZWxlY3QgJiYgZWxtLnNlbGVjdCgpLFxuICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5OiAobWVzc2FnZSkgPT4gZWxtLnNldEN1c3RvbVZhbGlkaXR5KG1lc3NhZ2UpLFxuICAgICAgICAgICAgICAgIHJlcG9ydFZhbGlkaXR5OiAoKSA9PiBlbG0ucmVwb3J0VmFsaWRpdHkoKSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbC5fZmllbGRzLCBuYW1lXSk7XG4gICAgY29uc3QgZmllbGQgPSBSZWFjdF9fZGVmYXVsdC51c2VNZW1vKCgpID0+ICh7XG4gICAgICAgIG5hbWUsXG4gICAgICAgIHZhbHVlLFxuICAgICAgICAuLi4oaXNCb29sZWFuKGRpc2FibGVkKSB8fCBmb3JtU3RhdGUuZGlzYWJsZWRcbiAgICAgICAgICAgID8geyBkaXNhYmxlZDogZm9ybVN0YXRlLmRpc2FibGVkIHx8IGRpc2FibGVkIH1cbiAgICAgICAgICAgIDoge30pLFxuICAgICAgICBvbkNoYW5nZSxcbiAgICAgICAgb25CbHVyLFxuICAgICAgICByZWYsXG4gICAgfSksIFtuYW1lLCBkaXNhYmxlZCwgZm9ybVN0YXRlLmRpc2FibGVkLCBvbkNoYW5nZSwgb25CbHVyLCByZWYsIHZhbHVlXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgY29uc3QgX3Nob3VsZFVucmVnaXN0ZXJGaWVsZCA9IGNvbnRyb2wuX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciB8fCBzaG91bGRVbnJlZ2lzdGVyO1xuICAgICAgICBjb250cm9sLnJlZ2lzdGVyKG5hbWUsIHtcbiAgICAgICAgICAgIC4uLl9wcm9wcy5jdXJyZW50LnJ1bGVzLFxuICAgICAgICAgICAgLi4uKGlzQm9vbGVhbihfcHJvcHMuY3VycmVudC5kaXNhYmxlZClcbiAgICAgICAgICAgICAgICA/IHsgZGlzYWJsZWQ6IF9wcm9wcy5jdXJyZW50LmRpc2FibGVkIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IHVwZGF0ZU1vdW50ZWQgPSAobmFtZSwgdmFsdWUpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICBpZiAoZmllbGQgJiYgZmllbGQuX2YpIHtcbiAgICAgICAgICAgICAgICBmaWVsZC5fZi5tb3VudCA9IHZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICB1cGRhdGVNb3VudGVkKG5hbWUsIHRydWUpO1xuICAgICAgICBpZiAoX3Nob3VsZFVucmVnaXN0ZXJGaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBjbG9uZU9iamVjdChnZXQoY29udHJvbC5fb3B0aW9ucy5kZWZhdWx0VmFsdWVzLCBuYW1lKSk7XG4gICAgICAgICAgICBzZXQoY29udHJvbC5fZGVmYXVsdFZhbHVlcywgbmFtZSwgdmFsdWUpO1xuICAgICAgICAgICAgaWYgKGlzVW5kZWZpbmVkKGdldChjb250cm9sLl9mb3JtVmFsdWVzLCBuYW1lKSkpIHtcbiAgICAgICAgICAgICAgICBzZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSwgdmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgICFpc0FycmF5RmllbGQgJiYgY29udHJvbC5yZWdpc3RlcihuYW1lKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIChpc0FycmF5RmllbGRcbiAgICAgICAgICAgICAgICA/IF9zaG91bGRVbnJlZ2lzdGVyRmllbGQgJiYgIWNvbnRyb2wuX3N0YXRlLmFjdGlvblxuICAgICAgICAgICAgICAgIDogX3Nob3VsZFVucmVnaXN0ZXJGaWVsZClcbiAgICAgICAgICAgICAgICA/IGNvbnRyb2wudW5yZWdpc3RlcihuYW1lKVxuICAgICAgICAgICAgICAgIDogdXBkYXRlTW91bnRlZChuYW1lLCBmYWxzZSk7XG4gICAgICAgIH07XG4gICAgfSwgW25hbWUsIGNvbnRyb2wsIGlzQXJyYXlGaWVsZCwgc2hvdWxkVW5yZWdpc3Rlcl0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnRyb2wuX3NldERpc2FibGVkRmllbGQoe1xuICAgICAgICAgICAgZGlzYWJsZWQsXG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICB9KTtcbiAgICB9LCBbZGlzYWJsZWQsIG5hbWUsIGNvbnRyb2xdKTtcbiAgICByZXR1cm4gUmVhY3RfX2RlZmF1bHQudXNlTWVtbygoKSA9PiAoe1xuICAgICAgICBmaWVsZCxcbiAgICAgICAgZm9ybVN0YXRlLFxuICAgICAgICBmaWVsZFN0YXRlLFxuICAgIH0pLCBbZmllbGQsIGZvcm1TdGF0ZSwgZmllbGRTdGF0ZV0pO1xufVxuXG4vKipcbiAqIENvbXBvbmVudCBiYXNlZCBvbiBgdXNlQ29udHJvbGxlcmAgaG9vayB0byB3b3JrIHdpdGggY29udHJvbGxlZCBjb21wb25lbnQuXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWNvbnRyb2xsZXIvY29udHJvbGxlcikg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXY2LWNvbnRyb2xsZXItdHMtand5encpIOKAoiBbVmlkZW9dKGh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9TjJVTmtfVUNWeUEpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gdGhlIHBhdGggbmFtZSB0byB0aGUgZm9ybSBmaWVsZCB2YWx1ZSwgYW5kIHZhbGlkYXRpb24gcnVsZXMuXG4gKlxuICogQHJldHVybnMgcHJvdmlkZSBmaWVsZCBoYW5kbGVyIGZ1bmN0aW9ucywgZmllbGQgYW5kIGZvcm0gc3RhdGUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IGNvbnRyb2wgfSA9IHVzZUZvcm08Rm9ybVZhbHVlcz4oe1xuICogICAgIGRlZmF1bHRWYWx1ZXM6IHtcbiAqICAgICAgIHRlc3Q6IFwiXCJcbiAqICAgICB9XG4gKiAgIH0pO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Zm9ybT5cbiAqICAgICAgIDxDb250cm9sbGVyXG4gKiAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gKiAgICAgICAgIG5hbWU9XCJ0ZXN0XCJcbiAqICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZDogeyBvbkNoYW5nZSwgb25CbHVyLCB2YWx1ZSwgcmVmIH0sIGZvcm1TdGF0ZSwgZmllbGRTdGF0ZSB9KSA9PiAoXG4gKiAgICAgICAgICAgPD5cbiAqICAgICAgICAgICAgIDxpbnB1dFxuICogICAgICAgICAgICAgICBvbkNoYW5nZT17b25DaGFuZ2V9IC8vIHNlbmQgdmFsdWUgdG8gaG9vayBmb3JtXG4gKiAgICAgICAgICAgICAgIG9uQmx1cj17b25CbHVyfSAvLyBub3RpZnkgd2hlbiBpbnB1dCBpcyB0b3VjaGVkXG4gKiAgICAgICAgICAgICAgIHZhbHVlPXt2YWx1ZX0gLy8gcmV0dXJuIHVwZGF0ZWQgdmFsdWVcbiAqICAgICAgICAgICAgICAgcmVmPXtyZWZ9IC8vIHNldCByZWYgZm9yIGZvY3VzIG1hbmFnZW1lbnRcbiAqICAgICAgICAgICAgIC8+XG4gKiAgICAgICAgICAgICA8cD57Zm9ybVN0YXRlLmlzU3VibWl0dGVkID8gXCJzdWJtaXR0ZWRcIiA6IFwiXCJ9PC9wPlxuICogICAgICAgICAgICAgPHA+e2ZpZWxkU3RhdGUuaXNUb3VjaGVkID8gXCJ0b3VjaGVkXCIgOiBcIlwifTwvcD5cbiAqICAgICAgICAgICA8Lz5cbiAqICAgICAgICAgKX1cbiAqICAgICAgIC8+XG4gKiAgICAgPC9mb3JtPlxuICogICApO1xuICogfVxuICogYGBgXG4gKi9cbmNvbnN0IENvbnRyb2xsZXIgPSAocHJvcHMpID0+IHByb3BzLnJlbmRlcih1c2VDb250cm9sbGVyKHByb3BzKSk7XG5cbmNvbnN0IGZsYXR0ZW4gPSAob2JqKSA9PiB7XG4gICAgY29uc3Qgb3V0cHV0ID0ge307XG4gICAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgICAgICBpZiAoaXNPYmplY3RUeXBlKG9ialtrZXldKSAmJiBvYmpba2V5XSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgY29uc3QgbmVzdGVkID0gZmxhdHRlbihvYmpba2V5XSk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IG5lc3RlZEtleSBvZiBPYmplY3Qua2V5cyhuZXN0ZWQpKSB7XG4gICAgICAgICAgICAgICAgb3V0cHV0W2Ake2tleX0uJHtuZXN0ZWRLZXl9YF0gPSBuZXN0ZWRbbmVzdGVkS2V5XTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIG91dHB1dFtrZXldID0gb2JqW2tleV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG91dHB1dDtcbn07XG5cbmNvbnN0IFBPU1RfUkVRVUVTVCA9ICdwb3N0Jztcbi8qKlxuICogRm9ybSBjb21wb25lbnQgdG8gbWFuYWdlIHN1Ym1pc3Npb24uXG4gKlxuICogQHBhcmFtIHByb3BzIC0gdG8gc2V0dXAgc3VibWlzc2lvbiBkZXRhaWwuIHtAbGluayBGb3JtUHJvcHN9XG4gKlxuICogQHJldHVybnMgZm9ybSBjb21wb25lbnQgb3IgaGVhZGxlc3MgcmVuZGVyIHByb3AuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IGNvbnRyb2wsIGZvcm1TdGF0ZTogeyBlcnJvcnMgfSB9ID0gdXNlRm9ybSgpO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Rm9ybSBhY3Rpb249XCIvYXBpXCIgY29udHJvbD17Y29udHJvbH0+XG4gKiAgICAgICA8aW5wdXQgey4uLnJlZ2lzdGVyKFwibmFtZVwiKX0gLz5cbiAqICAgICAgIDxwPntlcnJvcnM/LnJvb3Q/LnNlcnZlciAmJiAnU2VydmVyIGVycm9yJ308L3A+XG4gKiAgICAgICA8YnV0dG9uPlN1Ym1pdDwvYnV0dG9uPlxuICogICAgIDwvRm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiBGb3JtKHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gUmVhY3RfX2RlZmF1bHQudXNlU3RhdGUoZmFsc2UpO1xuICAgIGNvbnN0IHsgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgb25TdWJtaXQsIGNoaWxkcmVuLCBhY3Rpb24sIG1ldGhvZCA9IFBPU1RfUkVRVUVTVCwgaGVhZGVycywgZW5jVHlwZSwgb25FcnJvciwgcmVuZGVyLCBvblN1Y2Nlc3MsIHZhbGlkYXRlU3RhdHVzLCAuLi5yZXN0IH0gPSBwcm9wcztcbiAgICBjb25zdCBzdWJtaXQgPSBhc3luYyAoZXZlbnQpID0+IHtcbiAgICAgICAgbGV0IGhhc0Vycm9yID0gZmFsc2U7XG4gICAgICAgIGxldCB0eXBlID0gJyc7XG4gICAgICAgIGF3YWl0IGNvbnRyb2wuaGFuZGxlU3VibWl0KGFzeW5jIChkYXRhKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xuICAgICAgICAgICAgbGV0IGZvcm1EYXRhSnNvbiA9ICcnO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBmb3JtRGF0YUpzb24gPSBKU09OLnN0cmluZ2lmeShkYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChfYSkgeyB9XG4gICAgICAgICAgICBjb25zdCBmbGF0dGVuRm9ybVZhbHVlcyA9IGZsYXR0ZW4oY29udHJvbC5fZm9ybVZhbHVlcyk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBmbGF0dGVuRm9ybVZhbHVlcykge1xuICAgICAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChrZXksIGZsYXR0ZW5Gb3JtVmFsdWVzW2tleV0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKG9uU3VibWl0KSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgb25TdWJtaXQoe1xuICAgICAgICAgICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgICAgICAgICBldmVudCxcbiAgICAgICAgICAgICAgICAgICAgbWV0aG9kLFxuICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YSxcbiAgICAgICAgICAgICAgICAgICAgZm9ybURhdGFKc29uLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGFjdGlvbikge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNob3VsZFN0cmluZ2lmeVN1Ym1pc3Npb25EYXRhID0gW1xuICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVycyAmJiBoZWFkZXJzWydDb250ZW50LVR5cGUnXSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGVuY1R5cGUsXG4gICAgICAgICAgICAgICAgICAgIF0uc29tZSgodmFsdWUpID0+IHZhbHVlICYmIHZhbHVlLmluY2x1ZGVzKCdqc29uJykpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFN0cmluZyhhY3Rpb24pLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBtZXRob2QsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uaGVhZGVycyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi4oZW5jVHlwZSA/IHsgJ0NvbnRlbnQtVHlwZSc6IGVuY1R5cGUgfSA6IHt9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICBib2R5OiBzaG91bGRTdHJpbmdpZnlTdWJtaXNzaW9uRGF0YSA/IGZvcm1EYXRhSnNvbiA6IGZvcm1EYXRhLFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAodmFsaWRhdGVTdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICF2YWxpZGF0ZVN0YXR1cyhyZXNwb25zZS5zdGF0dXMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByZXNwb25zZS5zdGF0dXMgPCAyMDAgfHwgcmVzcG9uc2Uuc3RhdHVzID49IDMwMCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhc0Vycm9yID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3IgJiYgb25FcnJvcih7IHJlc3BvbnNlIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZSA9IFN0cmluZyhyZXNwb25zZS5zdGF0dXMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgb25TdWNjZXNzICYmIG9uU3VjY2Vzcyh7IHJlc3BvbnNlIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICBoYXNFcnJvciA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIG9uRXJyb3IgJiYgb25FcnJvcih7IGVycm9yIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSkoZXZlbnQpO1xuICAgICAgICBpZiAoaGFzRXJyb3IgJiYgcHJvcHMuY29udHJvbCkge1xuICAgICAgICAgICAgcHJvcHMuY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgaXNTdWJtaXRTdWNjZXNzZnVsOiBmYWxzZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcHJvcHMuY29udHJvbC5zZXRFcnJvcigncm9vdC5zZXJ2ZXInLCB7XG4gICAgICAgICAgICAgICAgdHlwZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBzZXRNb3VudGVkKHRydWUpO1xuICAgIH0sIFtdKTtcbiAgICByZXR1cm4gcmVuZGVyID8gKFJlYWN0X19kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoUmVhY3RfX2RlZmF1bHQuRnJhZ21lbnQsIG51bGwsIHJlbmRlcih7XG4gICAgICAgIHN1Ym1pdCxcbiAgICB9KSkpIDogKFJlYWN0X19kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJmb3JtXCIsIHsgbm9WYWxpZGF0ZTogbW91bnRlZCwgYWN0aW9uOiBhY3Rpb24sIG1ldGhvZDogbWV0aG9kLCBlbmNUeXBlOiBlbmNUeXBlLCBvblN1Ym1pdDogc3VibWl0LCAuLi5yZXN0IH0sIGNoaWxkcmVuKSk7XG59XG5cbnZhciBhcHBlbmRFcnJvcnMgPSAobmFtZSwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBlcnJvcnMsIHR5cGUsIG1lc3NhZ2UpID0+IHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYVxuICAgID8ge1xuICAgICAgICAuLi5lcnJvcnNbbmFtZV0sXG4gICAgICAgIHR5cGVzOiB7XG4gICAgICAgICAgICAuLi4oZXJyb3JzW25hbWVdICYmIGVycm9yc1tuYW1lXS50eXBlcyA/IGVycm9yc1tuYW1lXS50eXBlcyA6IHt9KSxcbiAgICAgICAgICAgIFt0eXBlXTogbWVzc2FnZSB8fCB0cnVlLFxuICAgICAgICB9LFxuICAgIH1cbiAgICA6IHt9O1xuXG52YXIgY29udmVydFRvQXJyYXlQYXlsb2FkID0gKHZhbHVlKSA9PiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZSA6IFt2YWx1ZV0pO1xuXG52YXIgY3JlYXRlU3ViamVjdCA9ICgpID0+IHtcbiAgICBsZXQgX29ic2VydmVycyA9IFtdO1xuICAgIGNvbnN0IG5leHQgPSAodmFsdWUpID0+IHtcbiAgICAgICAgZm9yIChjb25zdCBvYnNlcnZlciBvZiBfb2JzZXJ2ZXJzKSB7XG4gICAgICAgICAgICBvYnNlcnZlci5uZXh0ICYmIG9ic2VydmVyLm5leHQodmFsdWUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBzdWJzY3JpYmUgPSAob2JzZXJ2ZXIpID0+IHtcbiAgICAgICAgX29ic2VydmVycy5wdXNoKG9ic2VydmVyKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHVuc3Vic2NyaWJlOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgX29ic2VydmVycyA9IF9vYnNlcnZlcnMuZmlsdGVyKChvKSA9PiBvICE9PSBvYnNlcnZlcik7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH07XG4gICAgY29uc3QgdW5zdWJzY3JpYmUgPSAoKSA9PiB7XG4gICAgICAgIF9vYnNlcnZlcnMgPSBbXTtcbiAgICB9O1xuICAgIHJldHVybiB7XG4gICAgICAgIGdldCBvYnNlcnZlcnMoKSB7XG4gICAgICAgICAgICByZXR1cm4gX29ic2VydmVycztcbiAgICAgICAgfSxcbiAgICAgICAgbmV4dCxcbiAgICAgICAgc3Vic2NyaWJlLFxuICAgICAgICB1bnN1YnNjcmliZSxcbiAgICB9O1xufTtcblxudmFyIGlzUHJpbWl0aXZlID0gKHZhbHVlKSA9PiBpc051bGxPclVuZGVmaW5lZCh2YWx1ZSkgfHwgIWlzT2JqZWN0VHlwZSh2YWx1ZSk7XG5cbmZ1bmN0aW9uIGRlZXBFcXVhbChvYmplY3QxLCBvYmplY3QyLCBfaW50ZXJuYWxfdmlzaXRlZCA9IG5ldyBXZWFrU2V0KCkpIHtcbiAgICBpZiAoaXNQcmltaXRpdmUob2JqZWN0MSkgfHwgaXNQcmltaXRpdmUob2JqZWN0MikpIHtcbiAgICAgICAgcmV0dXJuIG9iamVjdDEgPT09IG9iamVjdDI7XG4gICAgfVxuICAgIGlmIChpc0RhdGVPYmplY3Qob2JqZWN0MSkgJiYgaXNEYXRlT2JqZWN0KG9iamVjdDIpKSB7XG4gICAgICAgIHJldHVybiBvYmplY3QxLmdldFRpbWUoKSA9PT0gb2JqZWN0Mi5nZXRUaW1lKCk7XG4gICAgfVxuICAgIGNvbnN0IGtleXMxID0gT2JqZWN0LmtleXMob2JqZWN0MSk7XG4gICAgY29uc3Qga2V5czIgPSBPYmplY3Qua2V5cyhvYmplY3QyKTtcbiAgICBpZiAoa2V5czEubGVuZ3RoICE9PSBrZXlzMi5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoX2ludGVybmFsX3Zpc2l0ZWQuaGFzKG9iamVjdDEpIHx8IF9pbnRlcm5hbF92aXNpdGVkLmhhcyhvYmplY3QyKSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgX2ludGVybmFsX3Zpc2l0ZWQuYWRkKG9iamVjdDEpO1xuICAgIF9pbnRlcm5hbF92aXNpdGVkLmFkZChvYmplY3QyKTtcbiAgICBmb3IgKGNvbnN0IGtleSBvZiBrZXlzMSkge1xuICAgICAgICBjb25zdCB2YWwxID0gb2JqZWN0MVtrZXldO1xuICAgICAgICBpZiAoIWtleXMyLmluY2x1ZGVzKGtleSkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoa2V5ICE9PSAncmVmJykge1xuICAgICAgICAgICAgY29uc3QgdmFsMiA9IG9iamVjdDJba2V5XTtcbiAgICAgICAgICAgIGlmICgoaXNEYXRlT2JqZWN0KHZhbDEpICYmIGlzRGF0ZU9iamVjdCh2YWwyKSkgfHxcbiAgICAgICAgICAgICAgICAoaXNPYmplY3QodmFsMSkgJiYgaXNPYmplY3QodmFsMikpIHx8XG4gICAgICAgICAgICAgICAgKEFycmF5LmlzQXJyYXkodmFsMSkgJiYgQXJyYXkuaXNBcnJheSh2YWwyKSlcbiAgICAgICAgICAgICAgICA/ICFkZWVwRXF1YWwodmFsMSwgdmFsMiwgX2ludGVybmFsX3Zpc2l0ZWQpXG4gICAgICAgICAgICAgICAgOiB2YWwxICE9PSB2YWwyKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuXG52YXIgaXNFbXB0eU9iamVjdCA9ICh2YWx1ZSkgPT4gaXNPYmplY3QodmFsdWUpICYmICFPYmplY3Qua2V5cyh2YWx1ZSkubGVuZ3RoO1xuXG52YXIgaXNGaWxlSW5wdXQgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSAnZmlsZSc7XG5cbnZhciBpc0Z1bmN0aW9uID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbic7XG5cbnZhciBpc0hUTUxFbGVtZW50ID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKCFpc1dlYikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IG93bmVyID0gdmFsdWUgPyB2YWx1ZS5vd25lckRvY3VtZW50IDogMDtcbiAgICByZXR1cm4gKHZhbHVlIGluc3RhbmNlb2ZcbiAgICAgICAgKG93bmVyICYmIG93bmVyLmRlZmF1bHRWaWV3ID8gb3duZXIuZGVmYXVsdFZpZXcuSFRNTEVsZW1lbnQgOiBIVE1MRWxlbWVudCkpO1xufTtcblxudmFyIGlzTXVsdGlwbGVTZWxlY3QgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSBgc2VsZWN0LW11bHRpcGxlYDtcblxudmFyIGlzUmFkaW9JbnB1dCA9IChlbGVtZW50KSA9PiBlbGVtZW50LnR5cGUgPT09ICdyYWRpbyc7XG5cbnZhciBpc1JhZGlvT3JDaGVja2JveCA9IChyZWYpID0+IGlzUmFkaW9JbnB1dChyZWYpIHx8IGlzQ2hlY2tCb3hJbnB1dChyZWYpO1xuXG52YXIgbGl2ZSA9IChyZWYpID0+IGlzSFRNTEVsZW1lbnQocmVmKSAmJiByZWYuaXNDb25uZWN0ZWQ7XG5cbmZ1bmN0aW9uIGJhc2VHZXQob2JqZWN0LCB1cGRhdGVQYXRoKSB7XG4gICAgY29uc3QgbGVuZ3RoID0gdXBkYXRlUGF0aC5zbGljZSgwLCAtMSkubGVuZ3RoO1xuICAgIGxldCBpbmRleCA9IDA7XG4gICAgd2hpbGUgKGluZGV4IDwgbGVuZ3RoKSB7XG4gICAgICAgIG9iamVjdCA9IGlzVW5kZWZpbmVkKG9iamVjdCkgPyBpbmRleCsrIDogb2JqZWN0W3VwZGF0ZVBhdGhbaW5kZXgrK11dO1xuICAgIH1cbiAgICByZXR1cm4gb2JqZWN0O1xufVxuZnVuY3Rpb24gaXNFbXB0eUFycmF5KG9iaikge1xuICAgIGZvciAoY29uc3Qga2V5IGluIG9iaikge1xuICAgICAgICBpZiAob2JqLmhhc093blByb3BlcnR5KGtleSkgJiYgIWlzVW5kZWZpbmVkKG9ialtrZXldKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuZnVuY3Rpb24gdW5zZXQob2JqZWN0LCBwYXRoKSB7XG4gICAgY29uc3QgcGF0aHMgPSBBcnJheS5pc0FycmF5KHBhdGgpXG4gICAgICAgID8gcGF0aFxuICAgICAgICA6IGlzS2V5KHBhdGgpXG4gICAgICAgICAgICA/IFtwYXRoXVxuICAgICAgICAgICAgOiBzdHJpbmdUb1BhdGgocGF0aCk7XG4gICAgY29uc3QgY2hpbGRPYmplY3QgPSBwYXRocy5sZW5ndGggPT09IDEgPyBvYmplY3QgOiBiYXNlR2V0KG9iamVjdCwgcGF0aHMpO1xuICAgIGNvbnN0IGluZGV4ID0gcGF0aHMubGVuZ3RoIC0gMTtcbiAgICBjb25zdCBrZXkgPSBwYXRoc1tpbmRleF07XG4gICAgaWYgKGNoaWxkT2JqZWN0KSB7XG4gICAgICAgIGRlbGV0ZSBjaGlsZE9iamVjdFtrZXldO1xuICAgIH1cbiAgICBpZiAoaW5kZXggIT09IDAgJiZcbiAgICAgICAgKChpc09iamVjdChjaGlsZE9iamVjdCkgJiYgaXNFbXB0eU9iamVjdChjaGlsZE9iamVjdCkpIHx8XG4gICAgICAgICAgICAoQXJyYXkuaXNBcnJheShjaGlsZE9iamVjdCkgJiYgaXNFbXB0eUFycmF5KGNoaWxkT2JqZWN0KSkpKSB7XG4gICAgICAgIHVuc2V0KG9iamVjdCwgcGF0aHMuc2xpY2UoMCwgLTEpKTtcbiAgICB9XG4gICAgcmV0dXJuIG9iamVjdDtcbn1cblxudmFyIG9iamVjdEhhc0Z1bmN0aW9uID0gKGRhdGEpID0+IHtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgICAgIGlmIChpc0Z1bmN0aW9uKGRhdGFba2V5XSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn07XG5cbmZ1bmN0aW9uIG1hcmtGaWVsZHNEaXJ0eShkYXRhLCBmaWVsZHMgPSB7fSkge1xuICAgIGNvbnN0IGlzUGFyZW50Tm9kZUFycmF5ID0gQXJyYXkuaXNBcnJheShkYXRhKTtcbiAgICBpZiAoaXNPYmplY3QoZGF0YSkgfHwgaXNQYXJlbnROb2RlQXJyYXkpIHtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZGF0YSkge1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YVtrZXldKSB8fFxuICAgICAgICAgICAgICAgIChpc09iamVjdChkYXRhW2tleV0pICYmICFvYmplY3RIYXNGdW5jdGlvbihkYXRhW2tleV0pKSkge1xuICAgICAgICAgICAgICAgIGZpZWxkc1trZXldID0gQXJyYXkuaXNBcnJheShkYXRhW2tleV0pID8gW10gOiB7fTtcbiAgICAgICAgICAgICAgICBtYXJrRmllbGRzRGlydHkoZGF0YVtrZXldLCBmaWVsZHNba2V5XSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICghaXNOdWxsT3JVbmRlZmluZWQoZGF0YVtrZXldKSkge1xuICAgICAgICAgICAgICAgIGZpZWxkc1trZXldID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZmllbGRzO1xufVxuZnVuY3Rpb24gZ2V0RGlydHlGaWVsZHNGcm9tRGVmYXVsdFZhbHVlcyhkYXRhLCBmb3JtVmFsdWVzLCBkaXJ0eUZpZWxkc0Zyb21WYWx1ZXMpIHtcbiAgICBjb25zdCBpc1BhcmVudE5vZGVBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSk7XG4gICAgaWYgKGlzT2JqZWN0KGRhdGEpIHx8IGlzUGFyZW50Tm9kZUFycmF5KSB7XG4gICAgICAgIGZvciAoY29uc3Qga2V5IGluIGRhdGEpIHtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGFba2V5XSkgfHxcbiAgICAgICAgICAgICAgICAoaXNPYmplY3QoZGF0YVtrZXldKSAmJiAhb2JqZWN0SGFzRnVuY3Rpb24oZGF0YVtrZXldKSkpIHtcbiAgICAgICAgICAgICAgICBpZiAoaXNVbmRlZmluZWQoZm9ybVZhbHVlcykgfHxcbiAgICAgICAgICAgICAgICAgICAgaXNQcmltaXRpdmUoZGlydHlGaWVsZHNGcm9tVmFsdWVzW2tleV0pKSB7XG4gICAgICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldID0gQXJyYXkuaXNBcnJheShkYXRhW2tleV0pXG4gICAgICAgICAgICAgICAgICAgICAgICA/IG1hcmtGaWVsZHNEaXJ0eShkYXRhW2tleV0sIFtdKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB7IC4uLm1hcmtGaWVsZHNEaXJ0eShkYXRhW2tleV0pIH07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBnZXREaXJ0eUZpZWxkc0Zyb21EZWZhdWx0VmFsdWVzKGRhdGFba2V5XSwgaXNOdWxsT3JVbmRlZmluZWQoZm9ybVZhbHVlcykgPyB7fSA6IGZvcm1WYWx1ZXNba2V5XSwgZGlydHlGaWVsZHNGcm9tVmFsdWVzW2tleV0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldID0gIWRlZXBFcXVhbChkYXRhW2tleV0sIGZvcm1WYWx1ZXNba2V5XSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGRpcnR5RmllbGRzRnJvbVZhbHVlcztcbn1cbnZhciBnZXREaXJ0eUZpZWxkcyA9IChkZWZhdWx0VmFsdWVzLCBmb3JtVmFsdWVzKSA9PiBnZXREaXJ0eUZpZWxkc0Zyb21EZWZhdWx0VmFsdWVzKGRlZmF1bHRWYWx1ZXMsIGZvcm1WYWx1ZXMsIG1hcmtGaWVsZHNEaXJ0eShmb3JtVmFsdWVzKSk7XG5cbmNvbnN0IGRlZmF1bHRSZXN1bHQgPSB7XG4gICAgdmFsdWU6IGZhbHNlLFxuICAgIGlzVmFsaWQ6IGZhbHNlLFxufTtcbmNvbnN0IHZhbGlkUmVzdWx0ID0geyB2YWx1ZTogdHJ1ZSwgaXNWYWxpZDogdHJ1ZSB9O1xudmFyIGdldENoZWNrYm94VmFsdWUgPSAob3B0aW9ucykgPT4ge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG9wdGlvbnMpKSB7XG4gICAgICAgIGlmIChvcHRpb25zLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IG9wdGlvbnNcbiAgICAgICAgICAgICAgICAuZmlsdGVyKChvcHRpb24pID0+IG9wdGlvbiAmJiBvcHRpb24uY2hlY2tlZCAmJiAhb3B0aW9uLmRpc2FibGVkKVxuICAgICAgICAgICAgICAgIC5tYXAoKG9wdGlvbikgPT4gb3B0aW9uLnZhbHVlKTtcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiB2YWx1ZXMsIGlzVmFsaWQ6ICEhdmFsdWVzLmxlbmd0aCB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBvcHRpb25zWzBdLmNoZWNrZWQgJiYgIW9wdGlvbnNbMF0uZGlzYWJsZWRcbiAgICAgICAgICAgID8gLy8gQHRzLWV4cGVjdC1lcnJvciBleHBlY3RlZCB0byB3b3JrIGluIHRoZSBicm93c2VyXG4gICAgICAgICAgICAgICAgb3B0aW9uc1swXS5hdHRyaWJ1dGVzICYmICFpc1VuZGVmaW5lZChvcHRpb25zWzBdLmF0dHJpYnV0ZXMudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgID8gaXNVbmRlZmluZWQob3B0aW9uc1swXS52YWx1ZSkgfHwgb3B0aW9uc1swXS52YWx1ZSA9PT0gJydcbiAgICAgICAgICAgICAgICAgICAgICAgID8gdmFsaWRSZXN1bHRcbiAgICAgICAgICAgICAgICAgICAgICAgIDogeyB2YWx1ZTogb3B0aW9uc1swXS52YWx1ZSwgaXNWYWxpZDogdHJ1ZSB9XG4gICAgICAgICAgICAgICAgICAgIDogdmFsaWRSZXN1bHRcbiAgICAgICAgICAgIDogZGVmYXVsdFJlc3VsdDtcbiAgICB9XG4gICAgcmV0dXJuIGRlZmF1bHRSZXN1bHQ7XG59O1xuXG52YXIgZ2V0RmllbGRWYWx1ZUFzID0gKHZhbHVlLCB7IHZhbHVlQXNOdW1iZXIsIHZhbHVlQXNEYXRlLCBzZXRWYWx1ZUFzIH0pID0+IGlzVW5kZWZpbmVkKHZhbHVlKVxuICAgID8gdmFsdWVcbiAgICA6IHZhbHVlQXNOdW1iZXJcbiAgICAgICAgPyB2YWx1ZSA9PT0gJydcbiAgICAgICAgICAgID8gTmFOXG4gICAgICAgICAgICA6IHZhbHVlXG4gICAgICAgICAgICAgICAgPyArdmFsdWVcbiAgICAgICAgICAgICAgICA6IHZhbHVlXG4gICAgICAgIDogdmFsdWVBc0RhdGUgJiYgaXNTdHJpbmcodmFsdWUpXG4gICAgICAgICAgICA/IG5ldyBEYXRlKHZhbHVlKVxuICAgICAgICAgICAgOiBzZXRWYWx1ZUFzXG4gICAgICAgICAgICAgICAgPyBzZXRWYWx1ZUFzKHZhbHVlKVxuICAgICAgICAgICAgICAgIDogdmFsdWU7XG5cbmNvbnN0IGRlZmF1bHRSZXR1cm4gPSB7XG4gICAgaXNWYWxpZDogZmFsc2UsXG4gICAgdmFsdWU6IG51bGwsXG59O1xudmFyIGdldFJhZGlvVmFsdWUgPSAob3B0aW9ucykgPT4gQXJyYXkuaXNBcnJheShvcHRpb25zKVxuICAgID8gb3B0aW9ucy5yZWR1Y2UoKHByZXZpb3VzLCBvcHRpb24pID0+IG9wdGlvbiAmJiBvcHRpb24uY2hlY2tlZCAmJiAhb3B0aW9uLmRpc2FibGVkXG4gICAgICAgID8ge1xuICAgICAgICAgICAgaXNWYWxpZDogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiBvcHRpb24udmFsdWUsXG4gICAgICAgIH1cbiAgICAgICAgOiBwcmV2aW91cywgZGVmYXVsdFJldHVybilcbiAgICA6IGRlZmF1bHRSZXR1cm47XG5cbmZ1bmN0aW9uIGdldEZpZWxkVmFsdWUoX2YpIHtcbiAgICBjb25zdCByZWYgPSBfZi5yZWY7XG4gICAgaWYgKGlzRmlsZUlucHV0KHJlZikpIHtcbiAgICAgICAgcmV0dXJuIHJlZi5maWxlcztcbiAgICB9XG4gICAgaWYgKGlzUmFkaW9JbnB1dChyZWYpKSB7XG4gICAgICAgIHJldHVybiBnZXRSYWRpb1ZhbHVlKF9mLnJlZnMpLnZhbHVlO1xuICAgIH1cbiAgICBpZiAoaXNNdWx0aXBsZVNlbGVjdChyZWYpKSB7XG4gICAgICAgIHJldHVybiBbLi4ucmVmLnNlbGVjdGVkT3B0aW9uc10ubWFwKCh7IHZhbHVlIH0pID0+IHZhbHVlKTtcbiAgICB9XG4gICAgaWYgKGlzQ2hlY2tCb3hJbnB1dChyZWYpKSB7XG4gICAgICAgIHJldHVybiBnZXRDaGVja2JveFZhbHVlKF9mLnJlZnMpLnZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gZ2V0RmllbGRWYWx1ZUFzKGlzVW5kZWZpbmVkKHJlZi52YWx1ZSkgPyBfZi5yZWYudmFsdWUgOiByZWYudmFsdWUsIF9mKTtcbn1cblxudmFyIGdldFJlc29sdmVyT3B0aW9ucyA9IChmaWVsZHNOYW1lcywgX2ZpZWxkcywgY3JpdGVyaWFNb2RlLCBzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uKSA9PiB7XG4gICAgY29uc3QgZmllbGRzID0ge307XG4gICAgZm9yIChjb25zdCBuYW1lIG9mIGZpZWxkc05hbWVzKSB7XG4gICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICBmaWVsZCAmJiBzZXQoZmllbGRzLCBuYW1lLCBmaWVsZC5fZik7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICAgIGNyaXRlcmlhTW9kZSxcbiAgICAgICAgbmFtZXM6IFsuLi5maWVsZHNOYW1lc10sXG4gICAgICAgIGZpZWxkcyxcbiAgICAgICAgc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbixcbiAgICB9O1xufTtcblxudmFyIGlzUmVnZXggPSAodmFsdWUpID0+IHZhbHVlIGluc3RhbmNlb2YgUmVnRXhwO1xuXG52YXIgZ2V0UnVsZVZhbHVlID0gKHJ1bGUpID0+IGlzVW5kZWZpbmVkKHJ1bGUpXG4gICAgPyBydWxlXG4gICAgOiBpc1JlZ2V4KHJ1bGUpXG4gICAgICAgID8gcnVsZS5zb3VyY2VcbiAgICAgICAgOiBpc09iamVjdChydWxlKVxuICAgICAgICAgICAgPyBpc1JlZ2V4KHJ1bGUudmFsdWUpXG4gICAgICAgICAgICAgICAgPyBydWxlLnZhbHVlLnNvdXJjZVxuICAgICAgICAgICAgICAgIDogcnVsZS52YWx1ZVxuICAgICAgICAgICAgOiBydWxlO1xuXG52YXIgZ2V0VmFsaWRhdGlvbk1vZGVzID0gKG1vZGUpID0+ICh7XG4gICAgaXNPblN1Ym1pdDogIW1vZGUgfHwgbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uU3VibWl0LFxuICAgIGlzT25CbHVyOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUub25CbHVyLFxuICAgIGlzT25DaGFuZ2U6IG1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5vbkNoYW5nZSxcbiAgICBpc09uQWxsOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUuYWxsLFxuICAgIGlzT25Ub3VjaDogbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uVG91Y2hlZCxcbn0pO1xuXG5jb25zdCBBU1lOQ19GVU5DVElPTiA9ICdBc3luY0Z1bmN0aW9uJztcbnZhciBoYXNQcm9taXNlVmFsaWRhdGlvbiA9IChmaWVsZFJlZmVyZW5jZSkgPT4gISFmaWVsZFJlZmVyZW5jZSAmJlxuICAgICEhZmllbGRSZWZlcmVuY2UudmFsaWRhdGUgJiZcbiAgICAhISgoaXNGdW5jdGlvbihmaWVsZFJlZmVyZW5jZS52YWxpZGF0ZSkgJiZcbiAgICAgICAgZmllbGRSZWZlcmVuY2UudmFsaWRhdGUuY29uc3RydWN0b3IubmFtZSA9PT0gQVNZTkNfRlVOQ1RJT04pIHx8XG4gICAgICAgIChpc09iamVjdChmaWVsZFJlZmVyZW5jZS52YWxpZGF0ZSkgJiZcbiAgICAgICAgICAgIE9iamVjdC52YWx1ZXMoZmllbGRSZWZlcmVuY2UudmFsaWRhdGUpLmZpbmQoKHZhbGlkYXRlRnVuY3Rpb24pID0+IHZhbGlkYXRlRnVuY3Rpb24uY29uc3RydWN0b3IubmFtZSA9PT0gQVNZTkNfRlVOQ1RJT04pKSk7XG5cbnZhciBoYXNWYWxpZGF0aW9uID0gKG9wdGlvbnMpID0+IG9wdGlvbnMubW91bnQgJiZcbiAgICAob3B0aW9ucy5yZXF1aXJlZCB8fFxuICAgICAgICBvcHRpb25zLm1pbiB8fFxuICAgICAgICBvcHRpb25zLm1heCB8fFxuICAgICAgICBvcHRpb25zLm1heExlbmd0aCB8fFxuICAgICAgICBvcHRpb25zLm1pbkxlbmd0aCB8fFxuICAgICAgICBvcHRpb25zLnBhdHRlcm4gfHxcbiAgICAgICAgb3B0aW9ucy52YWxpZGF0ZSk7XG5cbnZhciBpc1dhdGNoZWQgPSAobmFtZSwgX25hbWVzLCBpc0JsdXJFdmVudCkgPT4gIWlzQmx1ckV2ZW50ICYmXG4gICAgKF9uYW1lcy53YXRjaEFsbCB8fFxuICAgICAgICBfbmFtZXMud2F0Y2guaGFzKG5hbWUpIHx8XG4gICAgICAgIFsuLi5fbmFtZXMud2F0Y2hdLnNvbWUoKHdhdGNoTmFtZSkgPT4gbmFtZS5zdGFydHNXaXRoKHdhdGNoTmFtZSkgJiZcbiAgICAgICAgICAgIC9eXFwuXFx3Ky8udGVzdChuYW1lLnNsaWNlKHdhdGNoTmFtZS5sZW5ndGgpKSkpO1xuXG5jb25zdCBpdGVyYXRlRmllbGRzQnlBY3Rpb24gPSAoZmllbGRzLCBhY3Rpb24sIGZpZWxkc05hbWVzLCBhYm9ydEVhcmx5KSA9PiB7XG4gICAgZm9yIChjb25zdCBrZXkgb2YgZmllbGRzTmFtZXMgfHwgT2JqZWN0LmtleXMoZmllbGRzKSkge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChmaWVsZHMsIGtleSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgeyBfZiwgLi4uY3VycmVudEZpZWxkIH0gPSBmaWVsZDtcbiAgICAgICAgICAgIGlmIChfZikge1xuICAgICAgICAgICAgICAgIGlmIChfZi5yZWZzICYmIF9mLnJlZnNbMF0gJiYgYWN0aW9uKF9mLnJlZnNbMF0sIGtleSkgJiYgIWFib3J0RWFybHkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKF9mLnJlZiAmJiBhY3Rpb24oX2YucmVmLCBfZi5uYW1lKSAmJiAhYWJvcnRFYXJseSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVyYXRlRmllbGRzQnlBY3Rpb24oY3VycmVudEZpZWxkLCBhY3Rpb24pKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGlzT2JqZWN0KGN1cnJlbnRGaWVsZCkpIHtcbiAgICAgICAgICAgICAgICBpZiAoaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKGN1cnJlbnRGaWVsZCwgYWN0aW9uKSkge1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuO1xufTtcblxuZnVuY3Rpb24gc2NoZW1hRXJyb3JMb29rdXAoZXJyb3JzLCBfZmllbGRzLCBuYW1lKSB7XG4gICAgY29uc3QgZXJyb3IgPSBnZXQoZXJyb3JzLCBuYW1lKTtcbiAgICBpZiAoZXJyb3IgfHwgaXNLZXkobmFtZSkpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29uc3QgbmFtZXMgPSBuYW1lLnNwbGl0KCcuJyk7XG4gICAgd2hpbGUgKG5hbWVzLmxlbmd0aCkge1xuICAgICAgICBjb25zdCBmaWVsZE5hbWUgPSBuYW1lcy5qb2luKCcuJyk7XG4gICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgIGNvbnN0IGZvdW5kRXJyb3IgPSBnZXQoZXJyb3JzLCBmaWVsZE5hbWUpO1xuICAgICAgICBpZiAoZmllbGQgJiYgIUFycmF5LmlzQXJyYXkoZmllbGQpICYmIG5hbWUgIT09IGZpZWxkTmFtZSkge1xuICAgICAgICAgICAgcmV0dXJuIHsgbmFtZSB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChmb3VuZEVycm9yICYmIGZvdW5kRXJyb3IudHlwZSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBuYW1lOiBmaWVsZE5hbWUsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGZvdW5kRXJyb3IsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChmb3VuZEVycm9yICYmIGZvdW5kRXJyb3Iucm9vdCAmJiBmb3VuZEVycm9yLnJvb3QudHlwZSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBuYW1lOiBgJHtmaWVsZE5hbWV9LnJvb3RgLFxuICAgICAgICAgICAgICAgIGVycm9yOiBmb3VuZEVycm9yLnJvb3QsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIG5hbWVzLnBvcCgpO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICBuYW1lLFxuICAgIH07XG59XG5cbnZhciBzaG91bGRSZW5kZXJGb3JtU3RhdGUgPSAoZm9ybVN0YXRlRGF0YSwgX3Byb3h5Rm9ybVN0YXRlLCB1cGRhdGVGb3JtU3RhdGUsIGlzUm9vdCkgPT4ge1xuICAgIHVwZGF0ZUZvcm1TdGF0ZShmb3JtU3RhdGVEYXRhKTtcbiAgICBjb25zdCB7IG5hbWUsIC4uLmZvcm1TdGF0ZSB9ID0gZm9ybVN0YXRlRGF0YTtcbiAgICByZXR1cm4gKGlzRW1wdHlPYmplY3QoZm9ybVN0YXRlKSB8fFxuICAgICAgICBPYmplY3Qua2V5cyhmb3JtU3RhdGUpLmxlbmd0aCA+PSBPYmplY3Qua2V5cyhfcHJveHlGb3JtU3RhdGUpLmxlbmd0aCB8fFxuICAgICAgICBPYmplY3Qua2V5cyhmb3JtU3RhdGUpLmZpbmQoKGtleSkgPT4gX3Byb3h5Rm9ybVN0YXRlW2tleV0gPT09XG4gICAgICAgICAgICAoIWlzUm9vdCB8fCBWQUxJREFUSU9OX01PREUuYWxsKSkpO1xufTtcblxudmFyIHNob3VsZFN1YnNjcmliZUJ5TmFtZSA9IChuYW1lLCBzaWduYWxOYW1lLCBleGFjdCkgPT4gIW5hbWUgfHxcbiAgICAhc2lnbmFsTmFtZSB8fFxuICAgIG5hbWUgPT09IHNpZ25hbE5hbWUgfHxcbiAgICBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSkuc29tZSgoY3VycmVudE5hbWUpID0+IGN1cnJlbnROYW1lICYmXG4gICAgICAgIChleGFjdFxuICAgICAgICAgICAgPyBjdXJyZW50TmFtZSA9PT0gc2lnbmFsTmFtZVxuICAgICAgICAgICAgOiBjdXJyZW50TmFtZS5zdGFydHNXaXRoKHNpZ25hbE5hbWUpIHx8XG4gICAgICAgICAgICAgICAgc2lnbmFsTmFtZS5zdGFydHNXaXRoKGN1cnJlbnROYW1lKSkpO1xuXG52YXIgc2tpcFZhbGlkYXRpb24gPSAoaXNCbHVyRXZlbnQsIGlzVG91Y2hlZCwgaXNTdWJtaXR0ZWQsIHJlVmFsaWRhdGVNb2RlLCBtb2RlKSA9PiB7XG4gICAgaWYgKG1vZGUuaXNPbkFsbCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGVsc2UgaWYgKCFpc1N1Ym1pdHRlZCAmJiBtb2RlLmlzT25Ub3VjaCkge1xuICAgICAgICByZXR1cm4gIShpc1RvdWNoZWQgfHwgaXNCbHVyRXZlbnQpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1N1Ym1pdHRlZCA/IHJlVmFsaWRhdGVNb2RlLmlzT25CbHVyIDogbW9kZS5pc09uQmx1cikge1xuICAgICAgICByZXR1cm4gIWlzQmx1ckV2ZW50O1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1N1Ym1pdHRlZCA/IHJlVmFsaWRhdGVNb2RlLmlzT25DaGFuZ2UgOiBtb2RlLmlzT25DaGFuZ2UpIHtcbiAgICAgICAgcmV0dXJuIGlzQmx1ckV2ZW50O1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn07XG5cbnZhciB1bnNldEVtcHR5QXJyYXkgPSAocmVmLCBuYW1lKSA9PiAhY29tcGFjdChnZXQocmVmLCBuYW1lKSkubGVuZ3RoICYmIHVuc2V0KHJlZiwgbmFtZSk7XG5cbnZhciB1cGRhdGVGaWVsZEFycmF5Um9vdEVycm9yID0gKGVycm9ycywgZXJyb3IsIG5hbWUpID0+IHtcbiAgICBjb25zdCBmaWVsZEFycmF5RXJyb3JzID0gY29udmVydFRvQXJyYXlQYXlsb2FkKGdldChlcnJvcnMsIG5hbWUpKTtcbiAgICBzZXQoZmllbGRBcnJheUVycm9ycywgJ3Jvb3QnLCBlcnJvcltuYW1lXSk7XG4gICAgc2V0KGVycm9ycywgbmFtZSwgZmllbGRBcnJheUVycm9ycyk7XG4gICAgcmV0dXJuIGVycm9ycztcbn07XG5cbnZhciBpc01lc3NhZ2UgPSAodmFsdWUpID0+IGlzU3RyaW5nKHZhbHVlKTtcblxuZnVuY3Rpb24gZ2V0VmFsaWRhdGVFcnJvcihyZXN1bHQsIHJlZiwgdHlwZSA9ICd2YWxpZGF0ZScpIHtcbiAgICBpZiAoaXNNZXNzYWdlKHJlc3VsdCkgfHxcbiAgICAgICAgKEFycmF5LmlzQXJyYXkocmVzdWx0KSAmJiByZXN1bHQuZXZlcnkoaXNNZXNzYWdlKSkgfHxcbiAgICAgICAgKGlzQm9vbGVhbihyZXN1bHQpICYmICFyZXN1bHQpKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0eXBlLFxuICAgICAgICAgICAgbWVzc2FnZTogaXNNZXNzYWdlKHJlc3VsdCkgPyByZXN1bHQgOiAnJyxcbiAgICAgICAgICAgIHJlZixcbiAgICAgICAgfTtcbiAgICB9XG59XG5cbnZhciBnZXRWYWx1ZUFuZE1lc3NhZ2UgPSAodmFsaWRhdGlvbkRhdGEpID0+IGlzT2JqZWN0KHZhbGlkYXRpb25EYXRhKSAmJiAhaXNSZWdleCh2YWxpZGF0aW9uRGF0YSlcbiAgICA/IHZhbGlkYXRpb25EYXRhXG4gICAgOiB7XG4gICAgICAgIHZhbHVlOiB2YWxpZGF0aW9uRGF0YSxcbiAgICAgICAgbWVzc2FnZTogJycsXG4gICAgfTtcblxudmFyIHZhbGlkYXRlRmllbGQgPSBhc3luYyAoZmllbGQsIGRpc2FibGVkRmllbGROYW1lcywgZm9ybVZhbHVlcywgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uLCBpc0ZpZWxkQXJyYXkpID0+IHtcbiAgICBjb25zdCB7IHJlZiwgcmVmcywgcmVxdWlyZWQsIG1heExlbmd0aCwgbWluTGVuZ3RoLCBtaW4sIG1heCwgcGF0dGVybiwgdmFsaWRhdGUsIG5hbWUsIHZhbHVlQXNOdW1iZXIsIG1vdW50LCB9ID0gZmllbGQuX2Y7XG4gICAgY29uc3QgaW5wdXRWYWx1ZSA9IGdldChmb3JtVmFsdWVzLCBuYW1lKTtcbiAgICBpZiAoIW1vdW50IHx8IGRpc2FibGVkRmllbGROYW1lcy5oYXMobmFtZSkpIHtcbiAgICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgICBjb25zdCBpbnB1dFJlZiA9IHJlZnMgPyByZWZzWzBdIDogcmVmO1xuICAgIGNvbnN0IHNldEN1c3RvbVZhbGlkaXR5ID0gKG1lc3NhZ2UpID0+IHtcbiAgICAgICAgaWYgKHNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24gJiYgaW5wdXRSZWYucmVwb3J0VmFsaWRpdHkpIHtcbiAgICAgICAgICAgIGlucHV0UmVmLnNldEN1c3RvbVZhbGlkaXR5KGlzQm9vbGVhbihtZXNzYWdlKSA/ICcnIDogbWVzc2FnZSB8fCAnJyk7XG4gICAgICAgICAgICBpbnB1dFJlZi5yZXBvcnRWYWxpZGl0eSgpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBlcnJvciA9IHt9O1xuICAgIGNvbnN0IGlzUmFkaW8gPSBpc1JhZGlvSW5wdXQocmVmKTtcbiAgICBjb25zdCBpc0NoZWNrQm94ID0gaXNDaGVja0JveElucHV0KHJlZik7XG4gICAgY29uc3QgaXNSYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvIHx8IGlzQ2hlY2tCb3g7XG4gICAgY29uc3QgaXNFbXB0eSA9ICgodmFsdWVBc051bWJlciB8fCBpc0ZpbGVJbnB1dChyZWYpKSAmJlxuICAgICAgICBpc1VuZGVmaW5lZChyZWYudmFsdWUpICYmXG4gICAgICAgIGlzVW5kZWZpbmVkKGlucHV0VmFsdWUpKSB8fFxuICAgICAgICAoaXNIVE1MRWxlbWVudChyZWYpICYmIHJlZi52YWx1ZSA9PT0gJycpIHx8XG4gICAgICAgIGlucHV0VmFsdWUgPT09ICcnIHx8XG4gICAgICAgIChBcnJheS5pc0FycmF5KGlucHV0VmFsdWUpICYmICFpbnB1dFZhbHVlLmxlbmd0aCk7XG4gICAgY29uc3QgYXBwZW5kRXJyb3JzQ3VycnkgPSBhcHBlbmRFcnJvcnMuYmluZChudWxsLCBuYW1lLCB2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEsIGVycm9yKTtcbiAgICBjb25zdCBnZXRNaW5NYXhNZXNzYWdlID0gKGV4Y2VlZE1heCwgbWF4TGVuZ3RoTWVzc2FnZSwgbWluTGVuZ3RoTWVzc2FnZSwgbWF4VHlwZSA9IElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWF4TGVuZ3RoLCBtaW5UeXBlID0gSU5QVVRfVkFMSURBVElPTl9SVUxFUy5taW5MZW5ndGgpID0+IHtcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9IGV4Y2VlZE1heCA/IG1heExlbmd0aE1lc3NhZ2UgOiBtaW5MZW5ndGhNZXNzYWdlO1xuICAgICAgICBlcnJvcltuYW1lXSA9IHtcbiAgICAgICAgICAgIHR5cGU6IGV4Y2VlZE1heCA/IG1heFR5cGUgOiBtaW5UeXBlLFxuICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgIHJlZixcbiAgICAgICAgICAgIC4uLmFwcGVuZEVycm9yc0N1cnJ5KGV4Y2VlZE1heCA/IG1heFR5cGUgOiBtaW5UeXBlLCBtZXNzYWdlKSxcbiAgICAgICAgfTtcbiAgICB9O1xuICAgIGlmIChpc0ZpZWxkQXJyYXlcbiAgICAgICAgPyAhQXJyYXkuaXNBcnJheShpbnB1dFZhbHVlKSB8fCAhaW5wdXRWYWx1ZS5sZW5ndGhcbiAgICAgICAgOiByZXF1aXJlZCAmJlxuICAgICAgICAgICAgKCghaXNSYWRpb09yQ2hlY2tib3ggJiYgKGlzRW1wdHkgfHwgaXNOdWxsT3JVbmRlZmluZWQoaW5wdXRWYWx1ZSkpKSB8fFxuICAgICAgICAgICAgICAgIChpc0Jvb2xlYW4oaW5wdXRWYWx1ZSkgJiYgIWlucHV0VmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgKGlzQ2hlY2tCb3ggJiYgIWdldENoZWNrYm94VmFsdWUocmVmcykuaXNWYWxpZCkgfHxcbiAgICAgICAgICAgICAgICAoaXNSYWRpbyAmJiAhZ2V0UmFkaW9WYWx1ZShyZWZzKS5pc1ZhbGlkKSkpIHtcbiAgICAgICAgY29uc3QgeyB2YWx1ZSwgbWVzc2FnZSB9ID0gaXNNZXNzYWdlKHJlcXVpcmVkKVxuICAgICAgICAgICAgPyB7IHZhbHVlOiAhIXJlcXVpcmVkLCBtZXNzYWdlOiByZXF1aXJlZCB9XG4gICAgICAgICAgICA6IGdldFZhbHVlQW5kTWVzc2FnZShyZXF1aXJlZCk7XG4gICAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB7XG4gICAgICAgICAgICAgICAgdHlwZTogSU5QVVRfVkFMSURBVElPTl9SVUxFUy5yZXF1aXJlZCxcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIHJlZjogaW5wdXRSZWYsXG4gICAgICAgICAgICAgICAgLi4uYXBwZW5kRXJyb3JzQ3VycnkoSU5QVVRfVkFMSURBVElPTl9SVUxFUy5yZXF1aXJlZCwgbWVzc2FnZSksXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICBzZXRDdXN0b21WYWxpZGl0eShtZXNzYWdlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKCFpc0VtcHR5ICYmICghaXNOdWxsT3JVbmRlZmluZWQobWluKSB8fCAhaXNOdWxsT3JVbmRlZmluZWQobWF4KSkpIHtcbiAgICAgICAgbGV0IGV4Y2VlZE1heDtcbiAgICAgICAgbGV0IGV4Y2VlZE1pbjtcbiAgICAgICAgY29uc3QgbWF4T3V0cHV0ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1heCk7XG4gICAgICAgIGNvbnN0IG1pbk91dHB1dCA9IGdldFZhbHVlQW5kTWVzc2FnZShtaW4pO1xuICAgICAgICBpZiAoIWlzTnVsbE9yVW5kZWZpbmVkKGlucHV0VmFsdWUpICYmICFpc05hTihpbnB1dFZhbHVlKSkge1xuICAgICAgICAgICAgY29uc3QgdmFsdWVOdW1iZXIgPSByZWYudmFsdWVBc051bWJlciB8fFxuICAgICAgICAgICAgICAgIChpbnB1dFZhbHVlID8gK2lucHV0VmFsdWUgOiBpbnB1dFZhbHVlKTtcbiAgICAgICAgICAgIGlmICghaXNOdWxsT3JVbmRlZmluZWQobWF4T3V0cHV0LnZhbHVlKSkge1xuICAgICAgICAgICAgICAgIGV4Y2VlZE1heCA9IHZhbHVlTnVtYmVyID4gbWF4T3V0cHV0LnZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFpc051bGxPclVuZGVmaW5lZChtaW5PdXRwdXQudmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgZXhjZWVkTWluID0gdmFsdWVOdW1iZXIgPCBtaW5PdXRwdXQudmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZURhdGUgPSByZWYudmFsdWVBc0RhdGUgfHwgbmV3IERhdGUoaW5wdXRWYWx1ZSk7XG4gICAgICAgICAgICBjb25zdCBjb252ZXJ0VGltZVRvRGF0ZSA9ICh0aW1lKSA9PiBuZXcgRGF0ZShuZXcgRGF0ZSgpLnRvRGF0ZVN0cmluZygpICsgJyAnICsgdGltZSk7XG4gICAgICAgICAgICBjb25zdCBpc1RpbWUgPSByZWYudHlwZSA9PSAndGltZSc7XG4gICAgICAgICAgICBjb25zdCBpc1dlZWsgPSByZWYudHlwZSA9PSAnd2Vlayc7XG4gICAgICAgICAgICBpZiAoaXNTdHJpbmcobWF4T3V0cHV0LnZhbHVlKSAmJiBpbnB1dFZhbHVlKSB7XG4gICAgICAgICAgICAgICAgZXhjZWVkTWF4ID0gaXNUaW1lXG4gICAgICAgICAgICAgICAgICAgID8gY29udmVydFRpbWVUb0RhdGUoaW5wdXRWYWx1ZSkgPiBjb252ZXJ0VGltZVRvRGF0ZShtYXhPdXRwdXQudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgIDogaXNXZWVrXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGlucHV0VmFsdWUgPiBtYXhPdXRwdXQudmFsdWVcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdmFsdWVEYXRlID4gbmV3IERhdGUobWF4T3V0cHV0LnZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc1N0cmluZyhtaW5PdXRwdXQudmFsdWUpICYmIGlucHV0VmFsdWUpIHtcbiAgICAgICAgICAgICAgICBleGNlZWRNaW4gPSBpc1RpbWVcbiAgICAgICAgICAgICAgICAgICAgPyBjb252ZXJ0VGltZVRvRGF0ZShpbnB1dFZhbHVlKSA8IGNvbnZlcnRUaW1lVG9EYXRlKG1pbk91dHB1dC52YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgOiBpc1dlZWtcbiAgICAgICAgICAgICAgICAgICAgICAgID8gaW5wdXRWYWx1ZSA8IG1pbk91dHB1dC52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB2YWx1ZURhdGUgPCBuZXcgRGF0ZShtaW5PdXRwdXQudmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XG4gICAgICAgICAgICBnZXRNaW5NYXhNZXNzYWdlKCEhZXhjZWVkTWF4LCBtYXhPdXRwdXQubWVzc2FnZSwgbWluT3V0cHV0Lm1lc3NhZ2UsIElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWF4LCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLm1pbik7XG4gICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5KGVycm9yW25hbWVdLm1lc3NhZ2UpO1xuICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoKG1heExlbmd0aCB8fCBtaW5MZW5ndGgpICYmXG4gICAgICAgICFpc0VtcHR5ICYmXG4gICAgICAgIChpc1N0cmluZyhpbnB1dFZhbHVlKSB8fCAoaXNGaWVsZEFycmF5ICYmIEFycmF5LmlzQXJyYXkoaW5wdXRWYWx1ZSkpKSkge1xuICAgICAgICBjb25zdCBtYXhMZW5ndGhPdXRwdXQgPSBnZXRWYWx1ZUFuZE1lc3NhZ2UobWF4TGVuZ3RoKTtcbiAgICAgICAgY29uc3QgbWluTGVuZ3RoT3V0cHV0ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1pbkxlbmd0aCk7XG4gICAgICAgIGNvbnN0IGV4Y2VlZE1heCA9ICFpc051bGxPclVuZGVmaW5lZChtYXhMZW5ndGhPdXRwdXQudmFsdWUpICYmXG4gICAgICAgICAgICBpbnB1dFZhbHVlLmxlbmd0aCA+ICttYXhMZW5ndGhPdXRwdXQudmFsdWU7XG4gICAgICAgIGNvbnN0IGV4Y2VlZE1pbiA9ICFpc051bGxPclVuZGVmaW5lZChtaW5MZW5ndGhPdXRwdXQudmFsdWUpICYmXG4gICAgICAgICAgICBpbnB1dFZhbHVlLmxlbmd0aCA8ICttaW5MZW5ndGhPdXRwdXQudmFsdWU7XG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XG4gICAgICAgICAgICBnZXRNaW5NYXhNZXNzYWdlKGV4Y2VlZE1heCwgbWF4TGVuZ3RoT3V0cHV0Lm1lc3NhZ2UsIG1pbkxlbmd0aE91dHB1dC5tZXNzYWdlKTtcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkoZXJyb3JbbmFtZV0ubWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChwYXR0ZXJuICYmICFpc0VtcHR5ICYmIGlzU3RyaW5nKGlucHV0VmFsdWUpKSB7XG4gICAgICAgIGNvbnN0IHsgdmFsdWU6IHBhdHRlcm5WYWx1ZSwgbWVzc2FnZSB9ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKHBhdHRlcm4pO1xuICAgICAgICBpZiAoaXNSZWdleChwYXR0ZXJuVmFsdWUpICYmICFpbnB1dFZhbHVlLm1hdGNoKHBhdHRlcm5WYWx1ZSkpIHtcbiAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgIHR5cGU6IElOUFVUX1ZBTElEQVRJT05fUlVMRVMucGF0dGVybixcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIHJlZixcbiAgICAgICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnBhdHRlcm4sIG1lc3NhZ2UpLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkobWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmICh2YWxpZGF0ZSkge1xuICAgICAgICBpZiAoaXNGdW5jdGlvbih2YWxpZGF0ZSkpIHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHZhbGlkYXRlKGlucHV0VmFsdWUsIGZvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgY29uc3QgdmFsaWRhdGVFcnJvciA9IGdldFZhbGlkYXRlRXJyb3IocmVzdWx0LCBpbnB1dFJlZik7XG4gICAgICAgICAgICBpZiAodmFsaWRhdGVFcnJvcikge1xuICAgICAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgICAgICAuLi52YWxpZGF0ZUVycm9yLFxuICAgICAgICAgICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnZhbGlkYXRlLCB2YWxpZGF0ZUVycm9yLm1lc3NhZ2UpLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkodmFsaWRhdGVFcnJvci5tZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc09iamVjdCh2YWxpZGF0ZSkpIHtcbiAgICAgICAgICAgIGxldCB2YWxpZGF0aW9uUmVzdWx0ID0ge307XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiB2YWxpZGF0ZSkge1xuICAgICAgICAgICAgICAgIGlmICghaXNFbXB0eU9iamVjdCh2YWxpZGF0aW9uUmVzdWx0KSAmJiAhdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCB2YWxpZGF0ZUVycm9yID0gZ2V0VmFsaWRhdGVFcnJvcihhd2FpdCB2YWxpZGF0ZVtrZXldKGlucHV0VmFsdWUsIGZvcm1WYWx1ZXMpLCBpbnB1dFJlZiwga2V5KTtcbiAgICAgICAgICAgICAgICBpZiAodmFsaWRhdGVFcnJvcikge1xuICAgICAgICAgICAgICAgICAgICB2YWxpZGF0aW9uUmVzdWx0ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4udmFsaWRhdGVFcnJvcixcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmFwcGVuZEVycm9yc0N1cnJ5KGtleSwgdmFsaWRhdGVFcnJvci5tZXNzYWdlKSxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkodmFsaWRhdGVFcnJvci5tZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB2YWxpZGF0aW9uUmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFpc0VtcHR5T2JqZWN0KHZhbGlkYXRpb25SZXN1bHQpKSB7XG4gICAgICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZjogaW5wdXRSZWYsXG4gICAgICAgICAgICAgICAgICAgIC4uLnZhbGlkYXRpb25SZXN1bHQsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNldEN1c3RvbVZhbGlkaXR5KHRydWUpO1xuICAgIHJldHVybiBlcnJvcjtcbn07XG5cbmNvbnN0IGRlZmF1bHRPcHRpb25zID0ge1xuICAgIG1vZGU6IFZBTElEQVRJT05fTU9ERS5vblN1Ym1pdCxcbiAgICByZVZhbGlkYXRlTW9kZTogVkFMSURBVElPTl9NT0RFLm9uQ2hhbmdlLFxuICAgIHNob3VsZEZvY3VzRXJyb3I6IHRydWUsXG59O1xuZnVuY3Rpb24gY3JlYXRlRm9ybUNvbnRyb2wocHJvcHMgPSB7fSkge1xuICAgIGxldCBfb3B0aW9ucyA9IHtcbiAgICAgICAgLi4uZGVmYXVsdE9wdGlvbnMsXG4gICAgICAgIC4uLnByb3BzLFxuICAgIH07XG4gICAgbGV0IF9mb3JtU3RhdGUgPSB7XG4gICAgICAgIHN1Ym1pdENvdW50OiAwLFxuICAgICAgICBpc0RpcnR5OiBmYWxzZSxcbiAgICAgICAgaXNSZWFkeTogZmFsc2UsXG4gICAgICAgIGlzTG9hZGluZzogaXNGdW5jdGlvbihfb3B0aW9ucy5kZWZhdWx0VmFsdWVzKSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXR0ZWQ6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdHRpbmc6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgdG91Y2hlZEZpZWxkczoge30sXG4gICAgICAgIGRpcnR5RmllbGRzOiB7fSxcbiAgICAgICAgdmFsaWRhdGluZ0ZpZWxkczoge30sXG4gICAgICAgIGVycm9yczogX29wdGlvbnMuZXJyb3JzIHx8IHt9LFxuICAgICAgICBkaXNhYmxlZDogX29wdGlvbnMuZGlzYWJsZWQgfHwgZmFsc2UsXG4gICAgfTtcbiAgICBjb25zdCBfZmllbGRzID0ge307XG4gICAgbGV0IF9kZWZhdWx0VmFsdWVzID0gaXNPYmplY3QoX29wdGlvbnMuZGVmYXVsdFZhbHVlcykgfHwgaXNPYmplY3QoX29wdGlvbnMudmFsdWVzKVxuICAgICAgICA/IGNsb25lT2JqZWN0KF9vcHRpb25zLmRlZmF1bHRWYWx1ZXMgfHwgX29wdGlvbnMudmFsdWVzKSB8fCB7fVxuICAgICAgICA6IHt9O1xuICAgIGxldCBfZm9ybVZhbHVlcyA9IF9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXJcbiAgICAgICAgPyB7fVxuICAgICAgICA6IGNsb25lT2JqZWN0KF9kZWZhdWx0VmFsdWVzKTtcbiAgICBsZXQgX3N0YXRlID0ge1xuICAgICAgICBhY3Rpb246IGZhbHNlLFxuICAgICAgICBtb3VudDogZmFsc2UsXG4gICAgICAgIHdhdGNoOiBmYWxzZSxcbiAgICB9O1xuICAgIGxldCBfbmFtZXMgPSB7XG4gICAgICAgIG1vdW50OiBuZXcgU2V0KCksXG4gICAgICAgIGRpc2FibGVkOiBuZXcgU2V0KCksXG4gICAgICAgIHVuTW91bnQ6IG5ldyBTZXQoKSxcbiAgICAgICAgYXJyYXk6IG5ldyBTZXQoKSxcbiAgICAgICAgd2F0Y2g6IG5ldyBTZXQoKSxcbiAgICB9O1xuICAgIGxldCBkZWxheUVycm9yQ2FsbGJhY2s7XG4gICAgbGV0IHRpbWVyID0gMDtcbiAgICBjb25zdCBfcHJveHlGb3JtU3RhdGUgPSB7XG4gICAgICAgIGlzRGlydHk6IGZhbHNlLFxuICAgICAgICBkaXJ0eUZpZWxkczogZmFsc2UsXG4gICAgICAgIHZhbGlkYXRpbmdGaWVsZHM6IGZhbHNlLFxuICAgICAgICB0b3VjaGVkRmllbGRzOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIGVycm9yczogZmFsc2UsXG4gICAgfTtcbiAgICBsZXQgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlID0ge1xuICAgICAgICAuLi5fcHJveHlGb3JtU3RhdGUsXG4gICAgfTtcbiAgICBjb25zdCBfc3ViamVjdHMgPSB7XG4gICAgICAgIGFycmF5OiBjcmVhdGVTdWJqZWN0KCksXG4gICAgICAgIHN0YXRlOiBjcmVhdGVTdWJqZWN0KCksXG4gICAgfTtcbiAgICBjb25zdCBzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycyA9IF9vcHRpb25zLmNyaXRlcmlhTW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLmFsbDtcbiAgICBjb25zdCBkZWJvdW5jZSA9IChjYWxsYmFjaykgPT4gKHdhaXQpID0+IHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICAgICAgdGltZXIgPSBzZXRUaW1lb3V0KGNhbGxiYWNrLCB3YWl0KTtcbiAgICB9O1xuICAgIGNvbnN0IF9zZXRWYWxpZCA9IGFzeW5jIChzaG91bGRVcGRhdGVWYWxpZCkgPT4ge1xuICAgICAgICBpZiAoIV9vcHRpb25zLmRpc2FibGVkICYmXG4gICAgICAgICAgICAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNWYWxpZCB8fFxuICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZVZhbGlkKSkge1xuICAgICAgICAgICAgY29uc3QgaXNWYWxpZCA9IF9vcHRpb25zLnJlc29sdmVyXG4gICAgICAgICAgICAgICAgPyBpc0VtcHR5T2JqZWN0KChhd2FpdCBfcnVuU2NoZW1hKCkpLmVycm9ycylcbiAgICAgICAgICAgICAgICA6IGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihfZmllbGRzLCB0cnVlKTtcbiAgICAgICAgICAgIGlmIChpc1ZhbGlkICE9PSBfZm9ybVN0YXRlLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWQsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IF91cGRhdGVJc1ZhbGlkYXRpbmcgPSAobmFtZXMsIGlzVmFsaWRhdGluZykgPT4ge1xuICAgICAgICBpZiAoIV9vcHRpb25zLmRpc2FibGVkICYmXG4gICAgICAgICAgICAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWRhdGluZyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eUZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWRhdGluZyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzKSkge1xuICAgICAgICAgICAgKG5hbWVzIHx8IEFycmF5LmZyb20oX25hbWVzLm1vdW50KSkuZm9yRWFjaCgobmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChuYW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWRhdGluZ1xuICAgICAgICAgICAgICAgICAgICAgICAgPyBzZXQoX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLCBuYW1lLCBpc1ZhbGlkYXRpbmcpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgdmFsaWRhdGluZ0ZpZWxkczogX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLFxuICAgICAgICAgICAgICAgIGlzVmFsaWRhdGluZzogIWlzRW1wdHlPYmplY3QoX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzKSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfc2V0RmllbGRBcnJheSA9IChuYW1lLCB2YWx1ZXMgPSBbXSwgbWV0aG9kLCBhcmdzLCBzaG91bGRTZXRWYWx1ZXMgPSB0cnVlLCBzaG91bGRVcGRhdGVGaWVsZHNBbmRTdGF0ZSA9IHRydWUpID0+IHtcbiAgICAgICAgaWYgKGFyZ3MgJiYgbWV0aG9kICYmICFfb3B0aW9ucy5kaXNhYmxlZCkge1xuICAgICAgICAgICAgX3N0YXRlLmFjdGlvbiA9IHRydWU7XG4gICAgICAgICAgICBpZiAoc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgJiYgQXJyYXkuaXNBcnJheShnZXQoX2ZpZWxkcywgbmFtZSkpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRWYWx1ZXMgPSBtZXRob2QoZ2V0KF9maWVsZHMsIG5hbWUpLCBhcmdzLmFyZ0EsIGFyZ3MuYXJnQik7XG4gICAgICAgICAgICAgICAgc2hvdWxkU2V0VmFsdWVzICYmIHNldChfZmllbGRzLCBuYW1lLCBmaWVsZFZhbHVlcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgJiZcbiAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSkpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3JzID0gbWV0aG9kKGdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSksIGFyZ3MuYXJnQSwgYXJncy5hcmdCKTtcbiAgICAgICAgICAgICAgICBzaG91bGRTZXRWYWx1ZXMgJiYgc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lLCBlcnJvcnMpO1xuICAgICAgICAgICAgICAgIHVuc2V0RW1wdHlBcnJheShfZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKF9wcm94eUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMpICYmXG4gICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgJiZcbiAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRvdWNoZWRGaWVsZHMgPSBtZXRob2QoZ2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSksIGFyZ3MuYXJnQSwgYXJncy5hcmdCKTtcbiAgICAgICAgICAgICAgICBzaG91bGRTZXRWYWx1ZXMgJiYgc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSwgdG91Y2hlZEZpZWxkcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoX3Byb3h5Rm9ybVN0YXRlLmRpcnR5RmllbGRzIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcykge1xuICAgICAgICAgICAgICAgIF9mb3JtU3RhdGUuZGlydHlGaWVsZHMgPSBnZXREaXJ0eUZpZWxkcyhfZGVmYXVsdFZhbHVlcywgX2Zvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgaXNEaXJ0eTogX2dldERpcnR5KG5hbWUsIHZhbHVlcyksXG4gICAgICAgICAgICAgICAgZGlydHlGaWVsZHM6IF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsXG4gICAgICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgICAgICBpc1ZhbGlkOiBfZm9ybVN0YXRlLmlzVmFsaWQsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHNldChfZm9ybVZhbHVlcywgbmFtZSwgdmFsdWVzKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgdXBkYXRlRXJyb3JzID0gKG5hbWUsIGVycm9yKSA9PiB7XG4gICAgICAgIHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3IpO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IF9zZXRFcnJvcnMgPSAoZXJyb3JzKSA9PiB7XG4gICAgICAgIF9mb3JtU3RhdGUuZXJyb3JzID0gZXJyb3JzO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgdXBkYXRlVmFsaWRBbmRWYWx1ZSA9IChuYW1lLCBzaG91bGRTa2lwU2V0VmFsdWVBcywgdmFsdWUsIHJlZikgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICBjb25zdCBkZWZhdWx0VmFsdWUgPSBnZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGlzVW5kZWZpbmVkKHZhbHVlKSA/IGdldChfZGVmYXVsdFZhbHVlcywgbmFtZSkgOiB2YWx1ZSk7XG4gICAgICAgICAgICBpc1VuZGVmaW5lZChkZWZhdWx0VmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgKHJlZiAmJiByZWYuZGVmYXVsdENoZWNrZWQpIHx8XG4gICAgICAgICAgICAgICAgc2hvdWxkU2tpcFNldFZhbHVlQXNcbiAgICAgICAgICAgICAgICA/IHNldChfZm9ybVZhbHVlcywgbmFtZSwgc2hvdWxkU2tpcFNldFZhbHVlQXMgPyBkZWZhdWx0VmFsdWUgOiBnZXRGaWVsZFZhbHVlKGZpZWxkLl9mKSlcbiAgICAgICAgICAgICAgICA6IHNldEZpZWxkVmFsdWUobmFtZSwgZGVmYXVsdFZhbHVlKTtcbiAgICAgICAgICAgIF9zdGF0ZS5tb3VudCAmJiBfc2V0VmFsaWQoKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgdXBkYXRlVG91Y2hBbmREaXJ0eSA9IChuYW1lLCBmaWVsZFZhbHVlLCBpc0JsdXJFdmVudCwgc2hvdWxkRGlydHksIHNob3VsZFJlbmRlcikgPT4ge1xuICAgICAgICBsZXQgc2hvdWxkVXBkYXRlRmllbGQgPSBmYWxzZTtcbiAgICAgICAgbGV0IGlzUHJldmlvdXNEaXJ0eSA9IGZhbHNlO1xuICAgICAgICBjb25zdCBvdXRwdXQgPSB7XG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICB9O1xuICAgICAgICBpZiAoIV9vcHRpb25zLmRpc2FibGVkKSB7XG4gICAgICAgICAgICBpZiAoIWlzQmx1ckV2ZW50IHx8IHNob3VsZERpcnR5KSB7XG4gICAgICAgICAgICAgICAgaWYgKF9wcm94eUZvcm1TdGF0ZS5pc0RpcnR5IHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc0RpcnR5KSB7XG4gICAgICAgICAgICAgICAgICAgIGlzUHJldmlvdXNEaXJ0eSA9IF9mb3JtU3RhdGUuaXNEaXJ0eTtcbiAgICAgICAgICAgICAgICAgICAgX2Zvcm1TdGF0ZS5pc0RpcnR5ID0gb3V0cHV0LmlzRGlydHkgPSBfZ2V0RGlydHkoKTtcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgPSBpc1ByZXZpb3VzRGlydHkgIT09IG91dHB1dC5pc0RpcnR5O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBpc0N1cnJlbnRGaWVsZFByaXN0aW5lID0gZGVlcEVxdWFsKGdldChfZGVmYXVsdFZhbHVlcywgbmFtZSksIGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgICAgIGlzUHJldmlvdXNEaXJ0eSA9ICEhZ2V0KF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIGlzQ3VycmVudEZpZWxkUHJpc3RpbmVcbiAgICAgICAgICAgICAgICAgICAgPyB1bnNldChfZm9ybVN0YXRlLmRpcnR5RmllbGRzLCBuYW1lKVxuICAgICAgICAgICAgICAgICAgICA6IHNldChfZm9ybVN0YXRlLmRpcnR5RmllbGRzLCBuYW1lLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBvdXRwdXQuZGlydHlGaWVsZHMgPSBfZm9ybVN0YXRlLmRpcnR5RmllbGRzO1xuICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkID1cbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICgoX3Byb3h5Rm9ybVN0YXRlLmRpcnR5RmllbGRzIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmRpcnR5RmllbGRzKSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUHJldmlvdXNEaXJ0eSAhPT0gIWlzQ3VycmVudEZpZWxkUHJpc3RpbmUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaXNQcmV2aW91c0ZpZWxkVG91Y2hlZCA9IGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIGlmICghaXNQcmV2aW91c0ZpZWxkVG91Y2hlZCkge1xuICAgICAgICAgICAgICAgICAgICBzZXQoX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lLCBpc0JsdXJFdmVudCk7XG4gICAgICAgICAgICAgICAgICAgIG91dHB1dC50b3VjaGVkRmllbGRzID0gX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzO1xuICAgICAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCA9XG4gICAgICAgICAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICgoX3Byb3h5Rm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMpICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUHJldmlvdXNGaWVsZFRvdWNoZWQgIT09IGlzQmx1ckV2ZW50KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCAmJiBzaG91bGRSZW5kZXIgJiYgX3N1YmplY3RzLnN0YXRlLm5leHQob3V0cHV0KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc2hvdWxkVXBkYXRlRmllbGQgPyBvdXRwdXQgOiB7fTtcbiAgICB9O1xuICAgIGNvbnN0IHNob3VsZFJlbmRlckJ5RXJyb3IgPSAobmFtZSwgaXNWYWxpZCwgZXJyb3IsIGZpZWxkU3RhdGUpID0+IHtcbiAgICAgICAgY29uc3QgcHJldmlvdXNGaWVsZEVycm9yID0gZ2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgY29uc3Qgc2hvdWxkVXBkYXRlVmFsaWQgPSAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHwgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpICYmXG4gICAgICAgICAgICBpc0Jvb2xlYW4oaXNWYWxpZCkgJiZcbiAgICAgICAgICAgIF9mb3JtU3RhdGUuaXNWYWxpZCAhPT0gaXNWYWxpZDtcbiAgICAgICAgaWYgKF9vcHRpb25zLmRlbGF5RXJyb3IgJiYgZXJyb3IpIHtcbiAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayA9IGRlYm91bmNlKCgpID0+IHVwZGF0ZUVycm9ycyhuYW1lLCBlcnJvcikpO1xuICAgICAgICAgICAgZGVsYXlFcnJvckNhbGxiYWNrKF9vcHRpb25zLmRlbGF5RXJyb3IpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayA9IG51bGw7XG4gICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgID8gc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lLCBlcnJvcilcbiAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKGVycm9yID8gIWRlZXBFcXVhbChwcmV2aW91c0ZpZWxkRXJyb3IsIGVycm9yKSA6IHByZXZpb3VzRmllbGRFcnJvcikgfHxcbiAgICAgICAgICAgICFpc0VtcHR5T2JqZWN0KGZpZWxkU3RhdGUpIHx8XG4gICAgICAgICAgICBzaG91bGRVcGRhdGVWYWxpZCkge1xuICAgICAgICAgICAgY29uc3QgdXBkYXRlZEZvcm1TdGF0ZSA9IHtcbiAgICAgICAgICAgICAgICAuLi5maWVsZFN0YXRlLFxuICAgICAgICAgICAgICAgIC4uLihzaG91bGRVcGRhdGVWYWxpZCAmJiBpc0Jvb2xlYW4oaXNWYWxpZCkgPyB7IGlzVmFsaWQgfSA6IHt9KSxcbiAgICAgICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgX2Zvcm1TdGF0ZSA9IHtcbiAgICAgICAgICAgICAgICAuLi5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgIC4uLnVwZGF0ZWRGb3JtU3RhdGUsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQodXBkYXRlZEZvcm1TdGF0ZSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IF9ydW5TY2hlbWEgPSBhc3luYyAobmFtZSkgPT4ge1xuICAgICAgICBfdXBkYXRlSXNWYWxpZGF0aW5nKG5hbWUsIHRydWUpO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBfb3B0aW9ucy5yZXNvbHZlcihfZm9ybVZhbHVlcywgX29wdGlvbnMuY29udGV4dCwgZ2V0UmVzb2x2ZXJPcHRpb25zKG5hbWUgfHwgX25hbWVzLm1vdW50LCBfZmllbGRzLCBfb3B0aW9ucy5jcml0ZXJpYU1vZGUsIF9vcHRpb25zLnNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24pKTtcbiAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhuYW1lKTtcbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9O1xuICAgIGNvbnN0IGV4ZWN1dGVTY2hlbWFBbmRVcGRhdGVTdGF0ZSA9IGFzeW5jIChuYW1lcykgPT4ge1xuICAgICAgICBjb25zdCB7IGVycm9ycyB9ID0gYXdhaXQgX3J1blNjaGVtYShuYW1lcyk7XG4gICAgICAgIGlmIChuYW1lcykge1xuICAgICAgICAgICAgZm9yIChjb25zdCBuYW1lIG9mIG5hbWVzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBnZXQoZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgICAgICA/IHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3IpXG4gICAgICAgICAgICAgICAgICAgIDogdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgX2Zvcm1TdGF0ZS5lcnJvcnMgPSBlcnJvcnM7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGVycm9ycztcbiAgICB9O1xuICAgIGNvbnN0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbiA9IGFzeW5jIChmaWVsZHMsIHNob3VsZE9ubHlDaGVja1ZhbGlkLCBjb250ZXh0ID0ge1xuICAgICAgICB2YWxpZDogdHJ1ZSxcbiAgICB9KSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgbmFtZSBpbiBmaWVsZHMpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZmllbGRzW25hbWVdO1xuICAgICAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBfZiwgLi4uZmllbGRWYWx1ZSB9ID0gZmllbGQ7XG4gICAgICAgICAgICAgICAgaWYgKF9mKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzRmllbGRBcnJheVJvb3QgPSBfbmFtZXMuYXJyYXkuaGFzKF9mLm5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBpc1Byb21pc2VGdW5jdGlvbiA9IGZpZWxkLl9mICYmIGhhc1Byb21pc2VWYWxpZGF0aW9uKGZpZWxkLl9mKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzUHJvbWlzZUZ1bmN0aW9uICYmIF9wcm94eUZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfdXBkYXRlSXNWYWxpZGF0aW5nKFtuYW1lXSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRFcnJvciA9IGF3YWl0IHZhbGlkYXRlRmllbGQoZmllbGQsIF9uYW1lcy5kaXNhYmxlZCwgX2Zvcm1WYWx1ZXMsIHNob3VsZERpc3BsYXlBbGxBc3NvY2lhdGVkRXJyb3JzLCBfb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uICYmICFzaG91bGRPbmx5Q2hlY2tWYWxpZCwgaXNGaWVsZEFycmF5Um9vdCk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc1Byb21pc2VGdW5jdGlvbiAmJiBfcHJveHlGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZEVycm9yW19mLm5hbWVdKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250ZXh0LnZhbGlkID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2hvdWxkT25seUNoZWNrVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAhc2hvdWxkT25seUNoZWNrVmFsaWQgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIChnZXQoZmllbGRFcnJvciwgX2YubmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGlzRmllbGRBcnJheVJvb3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB1cGRhdGVGaWVsZEFycmF5Um9vdEVycm9yKF9mb3JtU3RhdGUuZXJyb3JzLCBmaWVsZEVycm9yLCBfZi5uYW1lKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHNldChfZm9ybVN0YXRlLmVycm9ycywgX2YubmFtZSwgZmllbGRFcnJvcltfZi5uYW1lXSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBfZi5uYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICFpc0VtcHR5T2JqZWN0KGZpZWxkVmFsdWUpICYmXG4gICAgICAgICAgICAgICAgICAgIChhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oZmllbGRWYWx1ZSwgc2hvdWxkT25seUNoZWNrVmFsaWQsIGNvbnRleHQpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY29udGV4dC52YWxpZDtcbiAgICB9O1xuICAgIGNvbnN0IF9yZW1vdmVVbm1vdW50ZWQgPSAoKSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMudW5Nb3VudCkge1xuICAgICAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICBmaWVsZCAmJlxuICAgICAgICAgICAgICAgIChmaWVsZC5fZi5yZWZzXG4gICAgICAgICAgICAgICAgICAgID8gZmllbGQuX2YucmVmcy5ldmVyeSgocmVmKSA9PiAhbGl2ZShyZWYpKVxuICAgICAgICAgICAgICAgICAgICA6ICFsaXZlKGZpZWxkLl9mLnJlZikpICYmXG4gICAgICAgICAgICAgICAgdW5yZWdpc3RlcihuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBfbmFtZXMudW5Nb3VudCA9IG5ldyBTZXQoKTtcbiAgICB9O1xuICAgIGNvbnN0IF9nZXREaXJ0eSA9IChuYW1lLCBkYXRhKSA9PiAhX29wdGlvbnMuZGlzYWJsZWQgJiZcbiAgICAgICAgKG5hbWUgJiYgZGF0YSAmJiBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGRhdGEpLFxuICAgICAgICAgICAgIWRlZXBFcXVhbChnZXRWYWx1ZXMoKSwgX2RlZmF1bHRWYWx1ZXMpKTtcbiAgICBjb25zdCBfZ2V0V2F0Y2ggPSAobmFtZXMsIGRlZmF1bHRWYWx1ZSwgaXNHbG9iYWwpID0+IGdlbmVyYXRlV2F0Y2hPdXRwdXQobmFtZXMsIF9uYW1lcywge1xuICAgICAgICAuLi4oX3N0YXRlLm1vdW50XG4gICAgICAgICAgICA/IF9mb3JtVmFsdWVzXG4gICAgICAgICAgICA6IGlzVW5kZWZpbmVkKGRlZmF1bHRWYWx1ZSlcbiAgICAgICAgICAgICAgICA/IF9kZWZhdWx0VmFsdWVzXG4gICAgICAgICAgICAgICAgOiBpc1N0cmluZyhuYW1lcylcbiAgICAgICAgICAgICAgICAgICAgPyB7IFtuYW1lc106IGRlZmF1bHRWYWx1ZSB9XG4gICAgICAgICAgICAgICAgICAgIDogZGVmYXVsdFZhbHVlKSxcbiAgICB9LCBpc0dsb2JhbCwgZGVmYXVsdFZhbHVlKTtcbiAgICBjb25zdCBfZ2V0RmllbGRBcnJheSA9IChuYW1lKSA9PiBjb21wYWN0KGdldChfc3RhdGUubW91bnQgPyBfZm9ybVZhbHVlcyA6IF9kZWZhdWx0VmFsdWVzLCBuYW1lLCBfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyID8gZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lLCBbXSkgOiBbXSkpO1xuICAgIGNvbnN0IHNldEZpZWxkVmFsdWUgPSAobmFtZSwgdmFsdWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgbGV0IGZpZWxkVmFsdWUgPSB2YWx1ZTtcbiAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZFJlZmVyZW5jZSA9IGZpZWxkLl9mO1xuICAgICAgICAgICAgaWYgKGZpZWxkUmVmZXJlbmNlKSB7XG4gICAgICAgICAgICAgICAgIWZpZWxkUmVmZXJlbmNlLmRpc2FibGVkICYmXG4gICAgICAgICAgICAgICAgICAgIHNldChfZm9ybVZhbHVlcywgbmFtZSwgZ2V0RmllbGRWYWx1ZUFzKHZhbHVlLCBmaWVsZFJlZmVyZW5jZSkpO1xuICAgICAgICAgICAgICAgIGZpZWxkVmFsdWUgPVxuICAgICAgICAgICAgICAgICAgICBpc0hUTUxFbGVtZW50KGZpZWxkUmVmZXJlbmNlLnJlZikgJiYgaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICcnXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHZhbHVlO1xuICAgICAgICAgICAgICAgIGlmIChpc011bHRpcGxlU2VsZWN0KGZpZWxkUmVmZXJlbmNlLnJlZikpIHtcbiAgICAgICAgICAgICAgICAgICAgWy4uLmZpZWxkUmVmZXJlbmNlLnJlZi5vcHRpb25zXS5mb3JFYWNoKChvcHRpb25SZWYpID0+IChvcHRpb25SZWYuc2VsZWN0ZWQgPSBmaWVsZFZhbHVlLmluY2x1ZGVzKG9wdGlvblJlZi52YWx1ZSkpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZmllbGRSZWZlcmVuY2UucmVmcykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoaXNDaGVja0JveElucHV0KGZpZWxkUmVmZXJlbmNlLnJlZikpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZnMuZm9yRWFjaCgoY2hlY2tib3hSZWYpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWNoZWNrYm94UmVmLmRlZmF1bHRDaGVja2VkIHx8ICFjaGVja2JveFJlZi5kaXNhYmxlZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmaWVsZFZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tib3hSZWYuY2hlY2tlZCA9ICEhZmllbGRWYWx1ZS5maW5kKChkYXRhKSA9PiBkYXRhID09PSBjaGVja2JveFJlZi52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2JveFJlZi5jaGVja2VkID1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFZhbHVlID09PSBjaGVja2JveFJlZi52YWx1ZSB8fCAhIWZpZWxkVmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZnMuZm9yRWFjaCgocmFkaW9SZWYpID0+IChyYWRpb1JlZi5jaGVja2VkID0gcmFkaW9SZWYudmFsdWUgPT09IGZpZWxkVmFsdWUpKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChpc0ZpbGVJbnB1dChmaWVsZFJlZmVyZW5jZS5yZWYpKSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZi52YWx1ZSA9ICcnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgZmllbGRSZWZlcmVuY2UucmVmLnZhbHVlID0gZmllbGRWYWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFmaWVsZFJlZmVyZW5jZS5yZWYudHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAob3B0aW9ucy5zaG91bGREaXJ0eSB8fCBvcHRpb25zLnNob3VsZFRvdWNoKSAmJlxuICAgICAgICAgICAgdXBkYXRlVG91Y2hBbmREaXJ0eShuYW1lLCBmaWVsZFZhbHVlLCBvcHRpb25zLnNob3VsZFRvdWNoLCBvcHRpb25zLnNob3VsZERpcnR5LCB0cnVlKTtcbiAgICAgICAgb3B0aW9ucy5zaG91bGRWYWxpZGF0ZSAmJiB0cmlnZ2VyKG5hbWUpO1xuICAgIH07XG4gICAgY29uc3Qgc2V0VmFsdWVzID0gKG5hbWUsIHZhbHVlLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgZmllbGRLZXkgaW4gdmFsdWUpIHtcbiAgICAgICAgICAgIGlmICghdmFsdWUuaGFzT3duUHJvcGVydHkoZmllbGRLZXkpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZmllbGRWYWx1ZSA9IHZhbHVlW2ZpZWxkS2V5XTtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkTmFtZSA9IG5hbWUgKyAnLicgKyBmaWVsZEtleTtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAoX25hbWVzLmFycmF5LmhhcyhuYW1lKSB8fFxuICAgICAgICAgICAgICAgIGlzT2JqZWN0KGZpZWxkVmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgKGZpZWxkICYmICFmaWVsZC5fZikpICYmXG4gICAgICAgICAgICAgICAgIWlzRGF0ZU9iamVjdChmaWVsZFZhbHVlKVxuICAgICAgICAgICAgICAgID8gc2V0VmFsdWVzKGZpZWxkTmFtZSwgZmllbGRWYWx1ZSwgb3B0aW9ucylcbiAgICAgICAgICAgICAgICA6IHNldEZpZWxkVmFsdWUoZmllbGROYW1lLCBmaWVsZFZhbHVlLCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3Qgc2V0VmFsdWUgPSAobmFtZSwgdmFsdWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgY29uc3QgaXNGaWVsZEFycmF5ID0gX25hbWVzLmFycmF5LmhhcyhuYW1lKTtcbiAgICAgICAgY29uc3QgY2xvbmVWYWx1ZSA9IGNsb25lT2JqZWN0KHZhbHVlKTtcbiAgICAgICAgc2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBjbG9uZVZhbHVlKTtcbiAgICAgICAgaWYgKGlzRmllbGRBcnJheSkge1xuICAgICAgICAgICAgX3N1YmplY3RzLmFycmF5Lm5leHQoe1xuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgdmFsdWVzOiBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmICgoX3Byb3h5Rm9ybVN0YXRlLmlzRGlydHkgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlGb3JtU3RhdGUuZGlydHlGaWVsZHMgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNEaXJ0eSB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcykgJiZcbiAgICAgICAgICAgICAgICBvcHRpb25zLnNob3VsZERpcnR5KSB7XG4gICAgICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgICAgICBkaXJ0eUZpZWxkczogZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIF9mb3JtVmFsdWVzKSxcbiAgICAgICAgICAgICAgICAgICAgaXNEaXJ0eTogX2dldERpcnR5KG5hbWUsIGNsb25lVmFsdWUpLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgZmllbGQgJiYgIWZpZWxkLl9mICYmICFpc051bGxPclVuZGVmaW5lZChjbG9uZVZhbHVlKVxuICAgICAgICAgICAgICAgID8gc2V0VmFsdWVzKG5hbWUsIGNsb25lVmFsdWUsIG9wdGlvbnMpXG4gICAgICAgICAgICAgICAgOiBzZXRGaWVsZFZhbHVlKG5hbWUsIGNsb25lVmFsdWUsIG9wdGlvbnMpO1xuICAgICAgICB9XG4gICAgICAgIGlzV2F0Y2hlZChuYW1lLCBfbmFtZXMpICYmIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgLi4uX2Zvcm1TdGF0ZSB9KTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgbmFtZTogX3N0YXRlLm1vdW50ID8gbmFtZSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IG9uQ2hhbmdlID0gYXN5bmMgKGV2ZW50KSA9PiB7XG4gICAgICAgIF9zdGF0ZS5tb3VudCA9IHRydWU7XG4gICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldDtcbiAgICAgICAgbGV0IG5hbWUgPSB0YXJnZXQubmFtZTtcbiAgICAgICAgbGV0IGlzRmllbGRWYWx1ZVVwZGF0ZWQgPSB0cnVlO1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgY29uc3QgX3VwZGF0ZUlzRmllbGRWYWx1ZVVwZGF0ZWQgPSAoZmllbGRWYWx1ZSkgPT4ge1xuICAgICAgICAgICAgaXNGaWVsZFZhbHVlVXBkYXRlZCA9XG4gICAgICAgICAgICAgICAgTnVtYmVyLmlzTmFOKGZpZWxkVmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgICAgIChpc0RhdGVPYmplY3QoZmllbGRWYWx1ZSkgJiYgaXNOYU4oZmllbGRWYWx1ZS5nZXRUaW1lKCkpKSB8fFxuICAgICAgICAgICAgICAgICAgICBkZWVwRXF1YWwoZmllbGRWYWx1ZSwgZ2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBmaWVsZFZhbHVlKSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHZhbGlkYXRpb25Nb2RlQmVmb3JlU3VibWl0ID0gZ2V0VmFsaWRhdGlvbk1vZGVzKF9vcHRpb25zLm1vZGUpO1xuICAgICAgICBjb25zdCB2YWxpZGF0aW9uTW9kZUFmdGVyU3VibWl0ID0gZ2V0VmFsaWRhdGlvbk1vZGVzKF9vcHRpb25zLnJlVmFsaWRhdGVNb2RlKTtcbiAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICBsZXQgZXJyb3I7XG4gICAgICAgICAgICBsZXQgaXNWYWxpZDtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWUgPSB0YXJnZXQudHlwZVxuICAgICAgICAgICAgICAgID8gZ2V0RmllbGRWYWx1ZShmaWVsZC5fZilcbiAgICAgICAgICAgICAgICA6IGdldEV2ZW50VmFsdWUoZXZlbnQpO1xuICAgICAgICAgICAgY29uc3QgaXNCbHVyRXZlbnQgPSBldmVudC50eXBlID09PSBFVkVOVFMuQkxVUiB8fCBldmVudC50eXBlID09PSBFVkVOVFMuRk9DVVNfT1VUO1xuICAgICAgICAgICAgY29uc3Qgc2hvdWxkU2tpcFZhbGlkYXRpb24gPSAoIWhhc1ZhbGlkYXRpb24oZmllbGQuX2YpICYmXG4gICAgICAgICAgICAgICAgIV9vcHRpb25zLnJlc29sdmVyICYmXG4gICAgICAgICAgICAgICAgIWdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSkgJiZcbiAgICAgICAgICAgICAgICAhZmllbGQuX2YuZGVwcykgfHxcbiAgICAgICAgICAgICAgICBza2lwVmFsaWRhdGlvbihpc0JsdXJFdmVudCwgZ2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSksIF9mb3JtU3RhdGUuaXNTdWJtaXR0ZWQsIHZhbGlkYXRpb25Nb2RlQWZ0ZXJTdWJtaXQsIHZhbGlkYXRpb25Nb2RlQmVmb3JlU3VibWl0KTtcbiAgICAgICAgICAgIGNvbnN0IHdhdGNoZWQgPSBpc1dhdGNoZWQobmFtZSwgX25hbWVzLCBpc0JsdXJFdmVudCk7XG4gICAgICAgICAgICBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgaWYgKGlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2Yub25CbHVyICYmIGZpZWxkLl9mLm9uQmx1cihldmVudCk7XG4gICAgICAgICAgICAgICAgZGVsYXlFcnJvckNhbGxiYWNrICYmIGRlbGF5RXJyb3JDYWxsYmFjaygwKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGZpZWxkLl9mLm9uQ2hhbmdlKSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2Yub25DaGFuZ2UoZXZlbnQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZmllbGRTdGF0ZSA9IHVwZGF0ZVRvdWNoQW5kRGlydHkobmFtZSwgZmllbGRWYWx1ZSwgaXNCbHVyRXZlbnQpO1xuICAgICAgICAgICAgY29uc3Qgc2hvdWxkUmVuZGVyID0gIWlzRW1wdHlPYmplY3QoZmllbGRTdGF0ZSkgfHwgd2F0Y2hlZDtcbiAgICAgICAgICAgICFpc0JsdXJFdmVudCAmJlxuICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogZXZlbnQudHlwZSxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoc2hvdWxkU2tpcFZhbGlkYXRpb24pIHtcbiAgICAgICAgICAgICAgICBpZiAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHwgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKF9vcHRpb25zLm1vZGUgPT09ICdvbkJsdXInKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoaXNCbHVyRXZlbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfc2V0VmFsaWQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmICghaXNCbHVyRXZlbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9zZXRWYWxpZCgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiAoc2hvdWxkUmVuZGVyICYmXG4gICAgICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgbmFtZSwgLi4uKHdhdGNoZWQgPyB7fSA6IGZpZWxkU3RhdGUpIH0pKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgICFpc0JsdXJFdmVudCAmJiB3YXRjaGVkICYmIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgLi4uX2Zvcm1TdGF0ZSB9KTtcbiAgICAgICAgICAgIGlmIChfb3B0aW9ucy5yZXNvbHZlcikge1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgZXJyb3JzIH0gPSBhd2FpdCBfcnVuU2NoZW1hKFtuYW1lXSk7XG4gICAgICAgICAgICAgICAgX3VwZGF0ZUlzRmllbGRWYWx1ZVVwZGF0ZWQoZmllbGRWYWx1ZSk7XG4gICAgICAgICAgICAgICAgaWYgKGlzRmllbGRWYWx1ZVVwZGF0ZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJldmlvdXNFcnJvckxvb2t1cFJlc3VsdCA9IHNjaGVtYUVycm9yTG9va3VwKF9mb3JtU3RhdGUuZXJyb3JzLCBfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3JMb29rdXBSZXN1bHQgPSBzY2hlbWFFcnJvckxvb2t1cChlcnJvcnMsIF9maWVsZHMsIHByZXZpb3VzRXJyb3JMb29rdXBSZXN1bHQubmFtZSB8fCBuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgZXJyb3IgPSBlcnJvckxvb2t1cFJlc3VsdC5lcnJvcjtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSA9IGVycm9yTG9va3VwUmVzdWx0Lm5hbWU7XG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgPSBpc0VtcHR5T2JqZWN0KGVycm9ycyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0sIHRydWUpO1xuICAgICAgICAgICAgICAgIGVycm9yID0gKGF3YWl0IHZhbGlkYXRlRmllbGQoZmllbGQsIF9uYW1lcy5kaXNhYmxlZCwgX2Zvcm1WYWx1ZXMsIHNob3VsZERpc3BsYXlBbGxBc3NvY2lhdGVkRXJyb3JzLCBfb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uKSlbbmFtZV07XG4gICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0pO1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc0ZpZWxkVmFsdWVVcGRhdGVkKGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgICAgIGlmIChpc0ZpZWxkVmFsdWVVcGRhdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgaXNWYWxpZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNWYWxpZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaXNWYWxpZCA9IGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihfZmllbGRzLCB0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc0ZpZWxkVmFsdWVVcGRhdGVkKSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2YuZGVwcyAmJlxuICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyKGZpZWxkLl9mLmRlcHMpO1xuICAgICAgICAgICAgICAgIHNob3VsZFJlbmRlckJ5RXJyb3IobmFtZSwgaXNWYWxpZCwgZXJyb3IsIGZpZWxkU3RhdGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfZm9jdXNJbnB1dCA9IChyZWYsIGtleSkgPT4ge1xuICAgICAgICBpZiAoZ2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBrZXkpICYmIHJlZi5mb2N1cykge1xuICAgICAgICAgICAgcmVmLmZvY3VzKCk7XG4gICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47XG4gICAgfTtcbiAgICBjb25zdCB0cmlnZ2VyID0gYXN5bmMgKG5hbWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBsZXQgaXNWYWxpZDtcbiAgICAgICAgbGV0IHZhbGlkYXRpb25SZXN1bHQ7XG4gICAgICAgIGNvbnN0IGZpZWxkTmFtZXMgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSk7XG4gICAgICAgIGlmIChfb3B0aW9ucy5yZXNvbHZlcikge1xuICAgICAgICAgICAgY29uc3QgZXJyb3JzID0gYXdhaXQgZXhlY3V0ZVNjaGVtYUFuZFVwZGF0ZVN0YXRlKGlzVW5kZWZpbmVkKG5hbWUpID8gbmFtZSA6IGZpZWxkTmFtZXMpO1xuICAgICAgICAgICAgaXNWYWxpZCA9IGlzRW1wdHlPYmplY3QoZXJyb3JzKTtcbiAgICAgICAgICAgIHZhbGlkYXRpb25SZXN1bHQgPSBuYW1lXG4gICAgICAgICAgICAgICAgPyAhZmllbGROYW1lcy5zb21lKChuYW1lKSA9PiBnZXQoZXJyb3JzLCBuYW1lKSlcbiAgICAgICAgICAgICAgICA6IGlzVmFsaWQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAobmFtZSkge1xuICAgICAgICAgICAgdmFsaWRhdGlvblJlc3VsdCA9IChhd2FpdCBQcm9taXNlLmFsbChmaWVsZE5hbWVzLm1hcChhc3luYyAoZmllbGROYW1lKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gYXdhaXQgZXhlY3V0ZUJ1aWx0SW5WYWxpZGF0aW9uKGZpZWxkICYmIGZpZWxkLl9mID8geyBbZmllbGROYW1lXTogZmllbGQgfSA6IGZpZWxkKTtcbiAgICAgICAgICAgIH0pKSkuZXZlcnkoQm9vbGVhbik7XG4gICAgICAgICAgICAhKCF2YWxpZGF0aW9uUmVzdWx0ICYmICFfZm9ybVN0YXRlLmlzVmFsaWQpICYmIF9zZXRWYWxpZCgpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdmFsaWRhdGlvblJlc3VsdCA9IGlzVmFsaWQgPSBhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oX2ZpZWxkcyk7XG4gICAgICAgIH1cbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgLi4uKCFpc1N0cmluZyhuYW1lKSB8fFxuICAgICAgICAgICAgICAgICgoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHwgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpICYmXG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgIT09IF9mb3JtU3RhdGUuaXNWYWxpZClcbiAgICAgICAgICAgICAgICA/IHt9XG4gICAgICAgICAgICAgICAgOiB7IG5hbWUgfSksXG4gICAgICAgICAgICAuLi4oX29wdGlvbnMucmVzb2x2ZXIgfHwgIW5hbWUgPyB7IGlzVmFsaWQgfSA6IHt9KSxcbiAgICAgICAgICAgIGVycm9yczogX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgIH0pO1xuICAgICAgICBvcHRpb25zLnNob3VsZEZvY3VzICYmXG4gICAgICAgICAgICAhdmFsaWRhdGlvblJlc3VsdCAmJlxuICAgICAgICAgICAgaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKF9maWVsZHMsIF9mb2N1c0lucHV0LCBuYW1lID8gZmllbGROYW1lcyA6IF9uYW1lcy5tb3VudCk7XG4gICAgICAgIHJldHVybiB2YWxpZGF0aW9uUmVzdWx0O1xuICAgIH07XG4gICAgY29uc3QgZ2V0VmFsdWVzID0gKGZpZWxkTmFtZXMpID0+IHtcbiAgICAgICAgY29uc3QgdmFsdWVzID0ge1xuICAgICAgICAgICAgLi4uKF9zdGF0ZS5tb3VudCA/IF9mb3JtVmFsdWVzIDogX2RlZmF1bHRWYWx1ZXMpLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gaXNVbmRlZmluZWQoZmllbGROYW1lcylcbiAgICAgICAgICAgID8gdmFsdWVzXG4gICAgICAgICAgICA6IGlzU3RyaW5nKGZpZWxkTmFtZXMpXG4gICAgICAgICAgICAgICAgPyBnZXQodmFsdWVzLCBmaWVsZE5hbWVzKVxuICAgICAgICAgICAgICAgIDogZmllbGROYW1lcy5tYXAoKG5hbWUpID0+IGdldCh2YWx1ZXMsIG5hbWUpKTtcbiAgICB9O1xuICAgIGNvbnN0IGdldEZpZWxkU3RhdGUgPSAobmFtZSwgZm9ybVN0YXRlKSA9PiAoe1xuICAgICAgICBpbnZhbGlkOiAhIWdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLmVycm9ycywgbmFtZSksXG4gICAgICAgIGlzRGlydHk6ICEhZ2V0KChmb3JtU3RhdGUgfHwgX2Zvcm1TdGF0ZSkuZGlydHlGaWVsZHMsIG5hbWUpLFxuICAgICAgICBlcnJvcjogZ2V0KChmb3JtU3RhdGUgfHwgX2Zvcm1TdGF0ZSkuZXJyb3JzLCBuYW1lKSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiAhIWdldChfZm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMsIG5hbWUpLFxuICAgICAgICBpc1RvdWNoZWQ6ICEhZ2V0KChmb3JtU3RhdGUgfHwgX2Zvcm1TdGF0ZSkudG91Y2hlZEZpZWxkcywgbmFtZSksXG4gICAgfSk7XG4gICAgY29uc3QgY2xlYXJFcnJvcnMgPSAobmFtZSkgPT4ge1xuICAgICAgICBuYW1lICYmXG4gICAgICAgICAgICBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSkuZm9yRWFjaCgoaW5wdXROYW1lKSA9PiB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgaW5wdXROYW1lKSk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIGVycm9yczogbmFtZSA/IF9mb3JtU3RhdGUuZXJyb3JzIDoge30sXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3Qgc2V0RXJyb3IgPSAobmFtZSwgZXJyb3IsIG9wdGlvbnMpID0+IHtcbiAgICAgICAgY29uc3QgcmVmID0gKGdldChfZmllbGRzLCBuYW1lLCB7IF9mOiB7fSB9KS5fZiB8fCB7fSkucmVmO1xuICAgICAgICBjb25zdCBjdXJyZW50RXJyb3IgPSBnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpIHx8IHt9O1xuICAgICAgICAvLyBEb24ndCBvdmVycmlkZSBleGlzdGluZyBlcnJvciBtZXNzYWdlcyBlbHNld2hlcmUgaW4gdGhlIG9iamVjdCB0cmVlLlxuICAgICAgICBjb25zdCB7IHJlZjogY3VycmVudFJlZiwgbWVzc2FnZSwgdHlwZSwgLi4ucmVzdE9mRXJyb3JUcmVlIH0gPSBjdXJyZW50RXJyb3I7XG4gICAgICAgIHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwge1xuICAgICAgICAgICAgLi4ucmVzdE9mRXJyb3JUcmVlLFxuICAgICAgICAgICAgLi4uZXJyb3IsXG4gICAgICAgICAgICByZWYsXG4gICAgICAgIH0pO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgICAgb3B0aW9ucyAmJiBvcHRpb25zLnNob3VsZEZvY3VzICYmIHJlZiAmJiByZWYuZm9jdXMgJiYgcmVmLmZvY3VzKCk7XG4gICAgfTtcbiAgICBjb25zdCB3YXRjaCA9IChuYW1lLCBkZWZhdWx0VmFsdWUpID0+IGlzRnVuY3Rpb24obmFtZSlcbiAgICAgICAgPyBfc3ViamVjdHMuc3RhdGUuc3Vic2NyaWJlKHtcbiAgICAgICAgICAgIG5leHQ6IChwYXlsb2FkKSA9PiBuYW1lKF9nZXRXYXRjaCh1bmRlZmluZWQsIGRlZmF1bHRWYWx1ZSksIHBheWxvYWQpLFxuICAgICAgICB9KVxuICAgICAgICA6IF9nZXRXYXRjaChuYW1lLCBkZWZhdWx0VmFsdWUsIHRydWUpO1xuICAgIGNvbnN0IF9zdWJzY3JpYmUgPSAocHJvcHMpID0+IF9zdWJqZWN0cy5zdGF0ZS5zdWJzY3JpYmUoe1xuICAgICAgICBuZXh0OiAoZm9ybVN0YXRlKSA9PiB7XG4gICAgICAgICAgICBpZiAoc2hvdWxkU3Vic2NyaWJlQnlOYW1lKHByb3BzLm5hbWUsIGZvcm1TdGF0ZS5uYW1lLCBwcm9wcy5leGFjdCkgJiZcbiAgICAgICAgICAgICAgICBzaG91bGRSZW5kZXJGb3JtU3RhdGUoZm9ybVN0YXRlLCBwcm9wcy5mb3JtU3RhdGUgfHwgX3Byb3h5Rm9ybVN0YXRlLCBfc2V0Rm9ybVN0YXRlLCBwcm9wcy5yZVJlbmRlclJvb3QpKSB7XG4gICAgICAgICAgICAgICAgcHJvcHMuY2FsbGJhY2soe1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZXM6IHsgLi4uX2Zvcm1WYWx1ZXMgfSxcbiAgICAgICAgICAgICAgICAgICAgLi4uX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgLi4uZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH0pLnVuc3Vic2NyaWJlO1xuICAgIGNvbnN0IHN1YnNjcmliZSA9IChwcm9wcykgPT4ge1xuICAgICAgICBfc3RhdGUubW91bnQgPSB0cnVlO1xuICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAuLi5fcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi5wcm9wcy5mb3JtU3RhdGUsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBfc3Vic2NyaWJlKHtcbiAgICAgICAgICAgIC4uLnByb3BzLFxuICAgICAgICAgICAgZm9ybVN0YXRlOiBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgdW5yZWdpc3RlciA9IChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgZm9yIChjb25zdCBmaWVsZE5hbWUgb2YgbmFtZSA/IGNvbnZlcnRUb0FycmF5UGF5bG9hZChuYW1lKSA6IF9uYW1lcy5tb3VudCkge1xuICAgICAgICAgICAgX25hbWVzLm1vdW50LmRlbGV0ZShmaWVsZE5hbWUpO1xuICAgICAgICAgICAgX25hbWVzLmFycmF5LmRlbGV0ZShmaWVsZE5hbWUpO1xuICAgICAgICAgICAgaWYgKCFvcHRpb25zLmtlZXBWYWx1ZSkge1xuICAgICAgICAgICAgICAgIHVuc2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1WYWx1ZXMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwRXJyb3IgJiYgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwRGlydHkgJiYgdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICFvcHRpb25zLmtlZXBUb3VjaGVkICYmIHVuc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICFvcHRpb25zLmtlZXBJc1ZhbGlkYXRpbmcgJiZcbiAgICAgICAgICAgICAgICB1bnNldChfZm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciAmJlxuICAgICAgICAgICAgICAgICFvcHRpb25zLmtlZXBEZWZhdWx0VmFsdWUgJiZcbiAgICAgICAgICAgICAgICB1bnNldChfZGVmYXVsdFZhbHVlcywgZmllbGROYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICB2YWx1ZXM6IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKSxcbiAgICAgICAgfSk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIC4uLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi4oIW9wdGlvbnMua2VlcERpcnR5ID8ge30gOiB7IGlzRGlydHk6IF9nZXREaXJ0eSgpIH0pLFxuICAgICAgICB9KTtcbiAgICAgICAgIW9wdGlvbnMua2VlcElzVmFsaWQgJiYgX3NldFZhbGlkKCk7XG4gICAgfTtcbiAgICBjb25zdCBfc2V0RGlzYWJsZWRGaWVsZCA9ICh7IGRpc2FibGVkLCBuYW1lLCB9KSA9PiB7XG4gICAgICAgIGlmICgoaXNCb29sZWFuKGRpc2FibGVkKSAmJiBfc3RhdGUubW91bnQpIHx8XG4gICAgICAgICAgICAhIWRpc2FibGVkIHx8XG4gICAgICAgICAgICBfbmFtZXMuZGlzYWJsZWQuaGFzKG5hbWUpKSB7XG4gICAgICAgICAgICBkaXNhYmxlZCA/IF9uYW1lcy5kaXNhYmxlZC5hZGQobmFtZSkgOiBfbmFtZXMuZGlzYWJsZWQuZGVsZXRlKG5hbWUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCByZWdpc3RlciA9IChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgbGV0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICBjb25zdCBkaXNhYmxlZElzRGVmaW5lZCA9IGlzQm9vbGVhbihvcHRpb25zLmRpc2FibGVkKSB8fCBpc0Jvb2xlYW4oX29wdGlvbnMuZGlzYWJsZWQpO1xuICAgICAgICBzZXQoX2ZpZWxkcywgbmFtZSwge1xuICAgICAgICAgICAgLi4uKGZpZWxkIHx8IHt9KSxcbiAgICAgICAgICAgIF9mOiB7XG4gICAgICAgICAgICAgICAgLi4uKGZpZWxkICYmIGZpZWxkLl9mID8gZmllbGQuX2YgOiB7IHJlZjogeyBuYW1lIH0gfSksXG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICBtb3VudDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICAgIF9uYW1lcy5tb3VudC5hZGQobmFtZSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgX3NldERpc2FibGVkRmllbGQoe1xuICAgICAgICAgICAgICAgIGRpc2FibGVkOiBpc0Jvb2xlYW4ob3B0aW9ucy5kaXNhYmxlZClcbiAgICAgICAgICAgICAgICAgICAgPyBvcHRpb25zLmRpc2FibGVkXG4gICAgICAgICAgICAgICAgICAgIDogX29wdGlvbnMuZGlzYWJsZWQsXG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdXBkYXRlVmFsaWRBbmRWYWx1ZShuYW1lLCB0cnVlLCBvcHRpb25zLnZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uKGRpc2FibGVkSXNEZWZpbmVkXG4gICAgICAgICAgICAgICAgPyB7IGRpc2FibGVkOiBvcHRpb25zLmRpc2FibGVkIHx8IF9vcHRpb25zLmRpc2FibGVkIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgICAgIC4uLihfb3B0aW9ucy5wcm9ncmVzc2l2ZVxuICAgICAgICAgICAgICAgID8ge1xuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogISFvcHRpb25zLnJlcXVpcmVkLFxuICAgICAgICAgICAgICAgICAgICBtaW46IGdldFJ1bGVWYWx1ZShvcHRpb25zLm1pbiksXG4gICAgICAgICAgICAgICAgICAgIG1heDogZ2V0UnVsZVZhbHVlKG9wdGlvbnMubWF4KSxcbiAgICAgICAgICAgICAgICAgICAgbWluTGVuZ3RoOiBnZXRSdWxlVmFsdWUob3B0aW9ucy5taW5MZW5ndGgpLFxuICAgICAgICAgICAgICAgICAgICBtYXhMZW5ndGg6IGdldFJ1bGVWYWx1ZShvcHRpb25zLm1heExlbmd0aCksXG4gICAgICAgICAgICAgICAgICAgIHBhdHRlcm46IGdldFJ1bGVWYWx1ZShvcHRpb25zLnBhdHRlcm4pLFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICBvbkNoYW5nZSxcbiAgICAgICAgICAgIG9uQmx1cjogb25DaGFuZ2UsXG4gICAgICAgICAgICByZWY6IChyZWYpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAocmVmKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZ2lzdGVyKG5hbWUsIG9wdGlvbnMpO1xuICAgICAgICAgICAgICAgICAgICBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRSZWYgPSBpc1VuZGVmaW5lZChyZWYudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgICAgICA/IHJlZi5xdWVyeVNlbGVjdG9yQWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyByZWYucXVlcnlTZWxlY3RvckFsbCgnaW5wdXQsc2VsZWN0LHRleHRhcmVhJylbMF0gfHwgcmVmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByZWZcbiAgICAgICAgICAgICAgICAgICAgICAgIDogcmVmO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvT3JDaGVja2JveChmaWVsZFJlZik7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlZnMgPSBmaWVsZC5fZi5yZWZzIHx8IFtdO1xuICAgICAgICAgICAgICAgICAgICBpZiAocmFkaW9PckNoZWNrYm94XG4gICAgICAgICAgICAgICAgICAgICAgICA/IHJlZnMuZmluZCgob3B0aW9uKSA9PiBvcHRpb24gPT09IGZpZWxkUmVmKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBmaWVsZFJlZiA9PT0gZmllbGQuX2YucmVmKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgc2V0KF9maWVsZHMsIG5hbWUsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9mOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZmllbGQuX2YsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uKHJhZGlvT3JDaGVja2JveFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZnM6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZWZzLmZpbHRlcihsaXZlKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi4oQXJyYXkuaXNBcnJheShnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpKSA/IFt7fV0gOiBbXSksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmOiB7IHR5cGU6IGZpZWxkUmVmLnR5cGUsIG5hbWUgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHsgcmVmOiBmaWVsZFJlZiB9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB1cGRhdGVWYWxpZEFuZFZhbHVlKG5hbWUsIGZhbHNlLCB1bmRlZmluZWQsIGZpZWxkUmVmKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUsIHt9KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWVsZC5fZi5tb3VudCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIChfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyIHx8IG9wdGlvbnMuc2hvdWxkVW5yZWdpc3RlcikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICEoaXNOYW1lSW5GaWVsZEFycmF5KF9uYW1lcy5hcnJheSwgbmFtZSkgJiYgX3N0YXRlLmFjdGlvbikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIF9uYW1lcy51bk1vdW50LmFkZChuYW1lKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH07XG4gICAgY29uc3QgX2ZvY3VzRXJyb3IgPSAoKSA9PiBfb3B0aW9ucy5zaG91bGRGb2N1c0Vycm9yICYmXG4gICAgICAgIGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihfZmllbGRzLCBfZm9jdXNJbnB1dCwgX25hbWVzLm1vdW50KTtcbiAgICBjb25zdCBfZGlzYWJsZUZvcm0gPSAoZGlzYWJsZWQpID0+IHtcbiAgICAgICAgaWYgKGlzQm9vbGVhbihkaXNhYmxlZCkpIHtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgZGlzYWJsZWQgfSk7XG4gICAgICAgICAgICBpdGVyYXRlRmllbGRzQnlBY3Rpb24oX2ZpZWxkcywgKHJlZiwgbmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRGaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoY3VycmVudEZpZWxkKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZi5kaXNhYmxlZCA9IGN1cnJlbnRGaWVsZC5fZi5kaXNhYmxlZCB8fCBkaXNhYmxlZDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoY3VycmVudEZpZWxkLl9mLnJlZnMpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50RmllbGQuX2YucmVmcy5mb3JFYWNoKChpbnB1dFJlZikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0UmVmLmRpc2FibGVkID0gY3VycmVudEZpZWxkLl9mLmRpc2FibGVkIHx8IGRpc2FibGVkO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LCAwLCBmYWxzZSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChvblZhbGlkLCBvbkludmFsaWQpID0+IGFzeW5jIChlKSA9PiB7XG4gICAgICAgIGxldCBvblZhbGlkRXJyb3IgPSB1bmRlZmluZWQ7XG4gICAgICAgIGlmIChlKSB7XG4gICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0ICYmIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIGUucGVyc2lzdCAmJlxuICAgICAgICAgICAgICAgIGUucGVyc2lzdCgpO1xuICAgICAgICB9XG4gICAgICAgIGxldCBmaWVsZFZhbHVlcyA9IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgaXNTdWJtaXR0aW5nOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKF9vcHRpb25zLnJlc29sdmVyKSB7XG4gICAgICAgICAgICBjb25zdCB7IGVycm9ycywgdmFsdWVzIH0gPSBhd2FpdCBfcnVuU2NoZW1hKCk7XG4gICAgICAgICAgICBfZm9ybVN0YXRlLmVycm9ycyA9IGVycm9ycztcbiAgICAgICAgICAgIGZpZWxkVmFsdWVzID0gY2xvbmVPYmplY3QodmFsdWVzKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihfZmllbGRzKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoX25hbWVzLmRpc2FibGVkLnNpemUpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMuZGlzYWJsZWQpIHtcbiAgICAgICAgICAgICAgICB1bnNldChmaWVsZFZhbHVlcywgbmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsICdyb290Jyk7XG4gICAgICAgIGlmIChpc0VtcHR5T2JqZWN0KF9mb3JtU3RhdGUuZXJyb3JzKSkge1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIGVycm9yczoge30sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgb25WYWxpZChmaWVsZFZhbHVlcywgZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBvblZhbGlkRXJyb3IgPSBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlmIChvbkludmFsaWQpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBvbkludmFsaWQoeyAuLi5fZm9ybVN0YXRlLmVycm9ycyB9LCBlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF9mb2N1c0Vycm9yKCk7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KF9mb2N1c0Vycm9yKTtcbiAgICAgICAgfVxuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBpc1N1Ym1pdHRlZDogdHJ1ZSxcbiAgICAgICAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGlzRW1wdHlPYmplY3QoX2Zvcm1TdGF0ZS5lcnJvcnMpICYmICFvblZhbGlkRXJyb3IsXG4gICAgICAgICAgICBzdWJtaXRDb3VudDogX2Zvcm1TdGF0ZS5zdWJtaXRDb3VudCArIDEsXG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKG9uVmFsaWRFcnJvcikge1xuICAgICAgICAgICAgdGhyb3cgb25WYWxpZEVycm9yO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCByZXNldEZpZWxkID0gKG5hbWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBpZiAoZ2V0KF9maWVsZHMsIG5hbWUpKSB7XG4gICAgICAgICAgICBpZiAoaXNVbmRlZmluZWQob3B0aW9ucy5kZWZhdWx0VmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgc2V0VmFsdWUobmFtZSwgY2xvbmVPYmplY3QoZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lKSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgc2V0VmFsdWUobmFtZSwgb3B0aW9ucy5kZWZhdWx0VmFsdWUpO1xuICAgICAgICAgICAgICAgIHNldChfZGVmYXVsdFZhbHVlcywgbmFtZSwgY2xvbmVPYmplY3Qob3B0aW9ucy5kZWZhdWx0VmFsdWUpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghb3B0aW9ucy5rZWVwVG91Y2hlZCkge1xuICAgICAgICAgICAgICAgIHVuc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIW9wdGlvbnMua2VlcERpcnR5KSB7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgX2Zvcm1TdGF0ZS5pc0RpcnR5ID0gb3B0aW9ucy5kZWZhdWx0VmFsdWVcbiAgICAgICAgICAgICAgICAgICAgPyBfZ2V0RGlydHkobmFtZSwgY2xvbmVPYmplY3QoZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lKSkpXG4gICAgICAgICAgICAgICAgICAgIDogX2dldERpcnR5KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIW9wdGlvbnMua2VlcEVycm9yKSB7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkICYmIF9zZXRWYWxpZCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoeyAuLi5fZm9ybVN0YXRlIH0pO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfcmVzZXQgPSAoZm9ybVZhbHVlcywga2VlcFN0YXRlT3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRWYWx1ZXMgPSBmb3JtVmFsdWVzID8gY2xvbmVPYmplY3QoZm9ybVZhbHVlcykgOiBfZGVmYXVsdFZhbHVlcztcbiAgICAgICAgY29uc3QgY2xvbmVVcGRhdGVkVmFsdWVzID0gY2xvbmVPYmplY3QodXBkYXRlZFZhbHVlcyk7XG4gICAgICAgIGNvbnN0IGlzRW1wdHlSZXNldFZhbHVlcyA9IGlzRW1wdHlPYmplY3QoZm9ybVZhbHVlcyk7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IGlzRW1wdHlSZXNldFZhbHVlcyA/IF9kZWZhdWx0VmFsdWVzIDogY2xvbmVVcGRhdGVkVmFsdWVzO1xuICAgICAgICBpZiAoIWtlZXBTdGF0ZU9wdGlvbnMua2VlcERlZmF1bHRWYWx1ZXMpIHtcbiAgICAgICAgICAgIF9kZWZhdWx0VmFsdWVzID0gdXBkYXRlZFZhbHVlcztcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWtlZXBTdGF0ZU9wdGlvbnMua2VlcFZhbHVlcykge1xuICAgICAgICAgICAgaWYgKGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5VmFsdWVzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRzVG9DaGVjayA9IG5ldyBTZXQoW1xuICAgICAgICAgICAgICAgICAgICAuLi5fbmFtZXMubW91bnQsXG4gICAgICAgICAgICAgICAgICAgIC4uLk9iamVjdC5rZXlzKGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBfZm9ybVZhbHVlcykpLFxuICAgICAgICAgICAgICAgIF0pO1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZmllbGROYW1lIG9mIEFycmF5LmZyb20oZmllbGRzVG9DaGVjaykpIHtcbiAgICAgICAgICAgICAgICAgICAgZ2V0KF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsIGZpZWxkTmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gc2V0KHZhbHVlcywgZmllbGROYW1lLCBnZXQoX2Zvcm1WYWx1ZXMsIGZpZWxkTmFtZSkpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHNldFZhbHVlKGZpZWxkTmFtZSwgZ2V0KHZhbHVlcywgZmllbGROYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzV2ViICYmIGlzVW5kZWZpbmVkKGZvcm1WYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMubW91bnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRSZWZlcmVuY2UgPSBBcnJheS5pc0FycmF5KGZpZWxkLl9mLnJlZnMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZmllbGQuX2YucmVmc1swXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGZpZWxkLl9mLnJlZjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaXNIVE1MRWxlbWVudChmaWVsZFJlZmVyZW5jZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZm9ybSA9IGZpZWxkUmVmZXJlbmNlLmNsb3Nlc3QoJ2Zvcm0nKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZvcm0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm0ucmVzZXQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZmllbGROYW1lIG9mIF9uYW1lcy5tb3VudCkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IGdldCh2YWx1ZXMsIGZpZWxkTmFtZSwgZ2V0KF9kZWZhdWx0VmFsdWVzLCBmaWVsZE5hbWUpKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFpc1VuZGVmaW5lZCh2YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldCh2YWx1ZXMsIGZpZWxkTmFtZSwgdmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgc2V0VmFsdWUoZmllbGROYW1lLCBnZXQodmFsdWVzLCBmaWVsZE5hbWUpKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF9mb3JtVmFsdWVzID0gY2xvbmVPYmplY3QodmFsdWVzKTtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5hcnJheS5uZXh0KHtcbiAgICAgICAgICAgICAgICB2YWx1ZXM6IHsgLi4udmFsdWVzIH0sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICB2YWx1ZXM6IHsgLi4udmFsdWVzIH0sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBfbmFtZXMgPSB7XG4gICAgICAgICAgICBtb3VudDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGlydHlWYWx1ZXMgPyBfbmFtZXMubW91bnQgOiBuZXcgU2V0KCksXG4gICAgICAgICAgICB1bk1vdW50OiBuZXcgU2V0KCksXG4gICAgICAgICAgICBhcnJheTogbmV3IFNldCgpLFxuICAgICAgICAgICAgZGlzYWJsZWQ6IG5ldyBTZXQoKSxcbiAgICAgICAgICAgIHdhdGNoOiBuZXcgU2V0KCksXG4gICAgICAgICAgICB3YXRjaEFsbDogZmFsc2UsXG4gICAgICAgICAgICBmb2N1czogJycsXG4gICAgICAgIH07XG4gICAgICAgIF9zdGF0ZS5tb3VudCA9XG4gICAgICAgICAgICAhX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICAhIWtlZXBTdGF0ZU9wdGlvbnMua2VlcElzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICAhIWtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5VmFsdWVzO1xuICAgICAgICBfc3RhdGUud2F0Y2ggPSAhIV9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXI7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIHN1Ym1pdENvdW50OiBrZWVwU3RhdGVPcHRpb25zLmtlZXBTdWJtaXRDb3VudFxuICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5zdWJtaXRDb3VudFxuICAgICAgICAgICAgICAgIDogMCxcbiAgICAgICAgICAgIGlzRGlydHk6IGlzRW1wdHlSZXNldFZhbHVlc1xuICAgICAgICAgICAgICAgID8gZmFsc2VcbiAgICAgICAgICAgICAgICA6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5XG4gICAgICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5pc0RpcnR5XG4gICAgICAgICAgICAgICAgICAgIDogISEoa2VlcFN0YXRlT3B0aW9ucy5rZWVwRGVmYXVsdFZhbHVlcyAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgIWRlZXBFcXVhbChmb3JtVmFsdWVzLCBfZGVmYXVsdFZhbHVlcykpLFxuICAgICAgICAgICAgaXNTdWJtaXR0ZWQ6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcElzU3VibWl0dGVkXG4gICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLmlzU3VibWl0dGVkXG4gICAgICAgICAgICAgICAgOiBmYWxzZSxcbiAgICAgICAgICAgIGRpcnR5RmllbGRzOiBpc0VtcHR5UmVzZXRWYWx1ZXNcbiAgICAgICAgICAgICAgICA/IHt9XG4gICAgICAgICAgICAgICAgOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBEaXJ0eVZhbHVlc1xuICAgICAgICAgICAgICAgICAgICA/IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERlZmF1bHRWYWx1ZXMgJiYgX2Zvcm1WYWx1ZXNcbiAgICAgICAgICAgICAgICAgICAgICAgID8gZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIF9mb3JtVmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBfZm9ybVN0YXRlLmRpcnR5RmllbGRzXG4gICAgICAgICAgICAgICAgICAgIDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGVmYXVsdFZhbHVlcyAmJiBmb3JtVmFsdWVzXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBmb3JtVmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBEaXJ0eVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDoge30sXG4gICAgICAgICAgICB0b3VjaGVkRmllbGRzOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBUb3VjaGVkXG4gICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHNcbiAgICAgICAgICAgICAgICA6IHt9LFxuICAgICAgICAgICAgZXJyb3JzOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBFcnJvcnMgPyBfZm9ybVN0YXRlLmVycm9ycyA6IHt9LFxuICAgICAgICAgICAgaXNTdWJtaXRTdWNjZXNzZnVsOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBJc1N1Ym1pdFN1Y2Nlc3NmdWxcbiAgICAgICAgICAgICAgICA/IF9mb3JtU3RhdGUuaXNTdWJtaXRTdWNjZXNzZnVsXG4gICAgICAgICAgICAgICAgOiBmYWxzZSxcbiAgICAgICAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgcmVzZXQgPSAoZm9ybVZhbHVlcywga2VlcFN0YXRlT3B0aW9ucykgPT4gX3Jlc2V0KGlzRnVuY3Rpb24oZm9ybVZhbHVlcylcbiAgICAgICAgPyBmb3JtVmFsdWVzKF9mb3JtVmFsdWVzKVxuICAgICAgICA6IGZvcm1WYWx1ZXMsIGtlZXBTdGF0ZU9wdGlvbnMpO1xuICAgIGNvbnN0IHNldEZvY3VzID0gKG5hbWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgY29uc3QgZmllbGRSZWZlcmVuY2UgPSBmaWVsZCAmJiBmaWVsZC5fZjtcbiAgICAgICAgaWYgKGZpZWxkUmVmZXJlbmNlKSB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZFJlZiA9IGZpZWxkUmVmZXJlbmNlLnJlZnNcbiAgICAgICAgICAgICAgICA/IGZpZWxkUmVmZXJlbmNlLnJlZnNbMF1cbiAgICAgICAgICAgICAgICA6IGZpZWxkUmVmZXJlbmNlLnJlZjtcbiAgICAgICAgICAgIGlmIChmaWVsZFJlZi5mb2N1cykge1xuICAgICAgICAgICAgICAgIGZpZWxkUmVmLmZvY3VzKCk7XG4gICAgICAgICAgICAgICAgb3B0aW9ucy5zaG91bGRTZWxlY3QgJiZcbiAgICAgICAgICAgICAgICAgICAgaXNGdW5jdGlvbihmaWVsZFJlZi5zZWxlY3QpICYmXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmLnNlbGVjdCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfc2V0Rm9ybVN0YXRlID0gKHVwZGF0ZWRGb3JtU3RhdGUpID0+IHtcbiAgICAgICAgX2Zvcm1TdGF0ZSA9IHtcbiAgICAgICAgICAgIC4uLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi51cGRhdGVkRm9ybVN0YXRlLFxuICAgICAgICB9O1xuICAgIH07XG4gICAgY29uc3QgX3Jlc2V0RGVmYXVsdFZhbHVlcyA9ICgpID0+IGlzRnVuY3Rpb24oX29wdGlvbnMuZGVmYXVsdFZhbHVlcykgJiZcbiAgICAgICAgX29wdGlvbnMuZGVmYXVsdFZhbHVlcygpLnRoZW4oKHZhbHVlcykgPT4ge1xuICAgICAgICAgICAgcmVzZXQodmFsdWVzLCBfb3B0aW9ucy5yZXNldE9wdGlvbnMpO1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgY29uc3QgbWV0aG9kcyA9IHtcbiAgICAgICAgY29udHJvbDoge1xuICAgICAgICAgICAgcmVnaXN0ZXIsXG4gICAgICAgICAgICB1bnJlZ2lzdGVyLFxuICAgICAgICAgICAgZ2V0RmllbGRTdGF0ZSxcbiAgICAgICAgICAgIGhhbmRsZVN1Ym1pdCxcbiAgICAgICAgICAgIHNldEVycm9yLFxuICAgICAgICAgICAgX3N1YnNjcmliZSxcbiAgICAgICAgICAgIF9ydW5TY2hlbWEsXG4gICAgICAgICAgICBfZm9jdXNFcnJvcixcbiAgICAgICAgICAgIF9nZXRXYXRjaCxcbiAgICAgICAgICAgIF9nZXREaXJ0eSxcbiAgICAgICAgICAgIF9zZXRWYWxpZCxcbiAgICAgICAgICAgIF9zZXRGaWVsZEFycmF5LFxuICAgICAgICAgICAgX3NldERpc2FibGVkRmllbGQsXG4gICAgICAgICAgICBfc2V0RXJyb3JzLFxuICAgICAgICAgICAgX2dldEZpZWxkQXJyYXksXG4gICAgICAgICAgICBfcmVzZXQsXG4gICAgICAgICAgICBfcmVzZXREZWZhdWx0VmFsdWVzLFxuICAgICAgICAgICAgX3JlbW92ZVVubW91bnRlZCxcbiAgICAgICAgICAgIF9kaXNhYmxlRm9ybSxcbiAgICAgICAgICAgIF9zdWJqZWN0cyxcbiAgICAgICAgICAgIF9wcm94eUZvcm1TdGF0ZSxcbiAgICAgICAgICAgIGdldCBfZmllbGRzKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfZmllbGRzO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfZm9ybVZhbHVlcygpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX2Zvcm1WYWx1ZXM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9zdGF0ZSgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX3N0YXRlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHNldCBfc3RhdGUodmFsdWUpIHtcbiAgICAgICAgICAgICAgICBfc3RhdGUgPSB2YWx1ZTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQgX2RlZmF1bHRWYWx1ZXMoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9kZWZhdWx0VmFsdWVzO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfbmFtZXMoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9uYW1lcztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZXQgX25hbWVzKHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgX25hbWVzID0gdmFsdWU7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9mb3JtU3RhdGUoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9mb3JtU3RhdGU7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9vcHRpb25zKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfb3B0aW9ucztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZXQgX29wdGlvbnModmFsdWUpIHtcbiAgICAgICAgICAgICAgICBfb3B0aW9ucyA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4uX29wdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgIC4uLnZhbHVlLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgICBzdWJzY3JpYmUsXG4gICAgICAgIHRyaWdnZXIsXG4gICAgICAgIHJlZ2lzdGVyLFxuICAgICAgICBoYW5kbGVTdWJtaXQsXG4gICAgICAgIHdhdGNoLFxuICAgICAgICBzZXRWYWx1ZSxcbiAgICAgICAgZ2V0VmFsdWVzLFxuICAgICAgICByZXNldCxcbiAgICAgICAgcmVzZXRGaWVsZCxcbiAgICAgICAgY2xlYXJFcnJvcnMsXG4gICAgICAgIHVucmVnaXN0ZXIsXG4gICAgICAgIHNldEVycm9yLFxuICAgICAgICBzZXRGb2N1cyxcbiAgICAgICAgZ2V0RmllbGRTdGF0ZSxcbiAgICB9O1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLm1ldGhvZHMsXG4gICAgICAgIGZvcm1Db250cm9sOiBtZXRob2RzLFxuICAgIH07XG59XG5cbnZhciBnZW5lcmF0ZUlkID0gKCkgPT4ge1xuICAgIGlmICh0eXBlb2YgY3J5cHRvICE9PSAndW5kZWZpbmVkJyAmJiBjcnlwdG8ucmFuZG9tVVVJRCkge1xuICAgICAgICByZXR1cm4gY3J5cHRvLnJhbmRvbVVVSUQoKTtcbiAgICB9XG4gICAgY29uc3QgZCA9IHR5cGVvZiBwZXJmb3JtYW5jZSA9PT0gJ3VuZGVmaW5lZCcgPyBEYXRlLm5vdygpIDogcGVyZm9ybWFuY2Uubm93KCkgKiAxMDAwO1xuICAgIHJldHVybiAneHh4eHh4eHgteHh4eC00eHh4LXl4eHgteHh4eHh4eHh4eHh4Jy5yZXBsYWNlKC9beHldL2csIChjKSA9PiB7XG4gICAgICAgIGNvbnN0IHIgPSAoTWF0aC5yYW5kb20oKSAqIDE2ICsgZCkgJSAxNiB8IDA7XG4gICAgICAgIHJldHVybiAoYyA9PSAneCcgPyByIDogKHIgJiAweDMpIHwgMHg4KS50b1N0cmluZygxNik7XG4gICAgfSk7XG59O1xuXG52YXIgZ2V0Rm9jdXNGaWVsZE5hbWUgPSAobmFtZSwgaW5kZXgsIG9wdGlvbnMgPSB7fSkgPT4gb3B0aW9ucy5zaG91bGRGb2N1cyB8fCBpc1VuZGVmaW5lZChvcHRpb25zLnNob3VsZEZvY3VzKVxuICAgID8gb3B0aW9ucy5mb2N1c05hbWUgfHxcbiAgICAgICAgYCR7bmFtZX0uJHtpc1VuZGVmaW5lZChvcHRpb25zLmZvY3VzSW5kZXgpID8gaW5kZXggOiBvcHRpb25zLmZvY3VzSW5kZXh9LmBcbiAgICA6ICcnO1xuXG52YXIgYXBwZW5kQXQgPSAoZGF0YSwgdmFsdWUpID0+IFtcbiAgICAuLi5kYXRhLFxuICAgIC4uLmNvbnZlcnRUb0FycmF5UGF5bG9hZCh2YWx1ZSksXG5dO1xuXG52YXIgZmlsbEVtcHR5QXJyYXkgPSAodmFsdWUpID0+IEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUubWFwKCgpID0+IHVuZGVmaW5lZCkgOiB1bmRlZmluZWQ7XG5cbmZ1bmN0aW9uIGluc2VydChkYXRhLCBpbmRleCwgdmFsdWUpIHtcbiAgICByZXR1cm4gW1xuICAgICAgICAuLi5kYXRhLnNsaWNlKDAsIGluZGV4KSxcbiAgICAgICAgLi4uY29udmVydFRvQXJyYXlQYXlsb2FkKHZhbHVlKSxcbiAgICAgICAgLi4uZGF0YS5zbGljZShpbmRleCksXG4gICAgXTtcbn1cblxudmFyIG1vdmVBcnJheUF0ID0gKGRhdGEsIGZyb20sIHRvKSA9PiB7XG4gICAgaWYgKCFBcnJheS5pc0FycmF5KGRhdGEpKSB7XG4gICAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gICAgaWYgKGlzVW5kZWZpbmVkKGRhdGFbdG9dKSkge1xuICAgICAgICBkYXRhW3RvXSA9IHVuZGVmaW5lZDtcbiAgICB9XG4gICAgZGF0YS5zcGxpY2UodG8sIDAsIGRhdGEuc3BsaWNlKGZyb20sIDEpWzBdKTtcbiAgICByZXR1cm4gZGF0YTtcbn07XG5cbnZhciBwcmVwZW5kQXQgPSAoZGF0YSwgdmFsdWUpID0+IFtcbiAgICAuLi5jb252ZXJ0VG9BcnJheVBheWxvYWQodmFsdWUpLFxuICAgIC4uLmNvbnZlcnRUb0FycmF5UGF5bG9hZChkYXRhKSxcbl07XG5cbmZ1bmN0aW9uIHJlbW92ZUF0SW5kZXhlcyhkYXRhLCBpbmRleGVzKSB7XG4gICAgbGV0IGkgPSAwO1xuICAgIGNvbnN0IHRlbXAgPSBbLi4uZGF0YV07XG4gICAgZm9yIChjb25zdCBpbmRleCBvZiBpbmRleGVzKSB7XG4gICAgICAgIHRlbXAuc3BsaWNlKGluZGV4IC0gaSwgMSk7XG4gICAgICAgIGkrKztcbiAgICB9XG4gICAgcmV0dXJuIGNvbXBhY3QodGVtcCkubGVuZ3RoID8gdGVtcCA6IFtdO1xufVxudmFyIHJlbW92ZUFycmF5QXQgPSAoZGF0YSwgaW5kZXgpID0+IGlzVW5kZWZpbmVkKGluZGV4KVxuICAgID8gW11cbiAgICA6IHJlbW92ZUF0SW5kZXhlcyhkYXRhLCBjb252ZXJ0VG9BcnJheVBheWxvYWQoaW5kZXgpLnNvcnQoKGEsIGIpID0+IGEgLSBiKSk7XG5cbnZhciBzd2FwQXJyYXlBdCA9IChkYXRhLCBpbmRleEEsIGluZGV4QikgPT4ge1xuICAgIFtkYXRhW2luZGV4QV0sIGRhdGFbaW5kZXhCXV0gPSBbZGF0YVtpbmRleEJdLCBkYXRhW2luZGV4QV1dO1xufTtcblxudmFyIHVwZGF0ZUF0ID0gKGZpZWxkVmFsdWVzLCBpbmRleCwgdmFsdWUpID0+IHtcbiAgICBmaWVsZFZhbHVlc1tpbmRleF0gPSB2YWx1ZTtcbiAgICByZXR1cm4gZmllbGRWYWx1ZXM7XG59O1xuXG4vKipcbiAqIEEgY3VzdG9tIGhvb2sgdGhhdCBleHBvc2VzIGNvbnZlbmllbnQgbWV0aG9kcyB0byBwZXJmb3JtIG9wZXJhdGlvbnMgd2l0aCBhIGxpc3Qgb2YgZHluYW1pYyBpbnB1dHMgdGhhdCBuZWVkIHRvIGJlIGFwcGVuZGVkLCB1cGRhdGVkLCByZW1vdmVkIGV0Yy4g4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXVzZWZpZWxkYXJyYXktc3N1Z24pIOKAoiBbVmlkZW9dKGh0dHBzOi8veW91dHUuYmUvNE1yYmZHU0ZZMkEpXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWZpZWxkYXJyYXkpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS11c2VmaWVsZGFycmF5LXNzdWduKVxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIHVzZUZpZWxkQXJyYXkgcHJvcHNcbiAqXG4gKiBAcmV0dXJucyBtZXRob2RzIC0gZnVuY3Rpb25zIHRvIG1hbmlwdWxhdGUgd2l0aCB0aGUgRmllbGQgQXJyYXlzIChkeW5hbWljIGlucHV0cykge0BsaW5rIFVzZUZpZWxkQXJyYXlSZXR1cm59XG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IHJlZ2lzdGVyLCBjb250cm9sLCBoYW5kbGVTdWJtaXQsIHJlc2V0LCB0cmlnZ2VyLCBzZXRFcnJvciB9ID0gdXNlRm9ybSh7XG4gKiAgICAgZGVmYXVsdFZhbHVlczoge1xuICogICAgICAgdGVzdDogW11cbiAqICAgICB9XG4gKiAgIH0pO1xuICogICBjb25zdCB7IGZpZWxkcywgYXBwZW5kIH0gPSB1c2VGaWVsZEFycmF5KHtcbiAqICAgICBjb250cm9sLFxuICogICAgIG5hbWU6IFwidGVzdFwiXG4gKiAgIH0pO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0KGRhdGEgPT4gY29uc29sZS5sb2coZGF0YSkpfT5cbiAqICAgICAgIHtmaWVsZHMubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICogICAgICAgICAgPGlucHV0IGtleT17aXRlbS5pZH0gey4uLnJlZ2lzdGVyKGB0ZXN0LiR7aW5kZXh9LmZpcnN0TmFtZWApfSAgLz5cbiAqICAgICAgICkpfVxuICogICAgICAgPGJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgb25DbGljaz17KCkgPT4gYXBwZW5kKHsgZmlyc3ROYW1lOiBcImJpbGxcIiB9KX0+XG4gKiAgICAgICAgIGFwcGVuZFxuICogICAgICAgPC9idXR0b24+XG4gKiAgICAgICA8aW5wdXQgdHlwZT1cInN1Ym1pdFwiIC8+XG4gKiAgICAgPC9mb3JtPlxuICogICApO1xuICogfVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIHVzZUZpZWxkQXJyYXkocHJvcHMpIHtcbiAgICBjb25zdCBtZXRob2RzID0gdXNlRm9ybUNvbnRleHQoKTtcbiAgICBjb25zdCB7IGNvbnRyb2wgPSBtZXRob2RzLmNvbnRyb2wsIG5hbWUsIGtleU5hbWUgPSAnaWQnLCBzaG91bGRVbnJlZ2lzdGVyLCBydWxlcywgfSA9IHByb3BzO1xuICAgIGNvbnN0IFtmaWVsZHMsIHNldEZpZWxkc10gPSBSZWFjdF9fZGVmYXVsdC51c2VTdGF0ZShjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpKTtcbiAgICBjb25zdCBpZHMgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKS5tYXAoZ2VuZXJhdGVJZCkpO1xuICAgIGNvbnN0IF9maWVsZElkcyA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZihmaWVsZHMpO1xuICAgIGNvbnN0IF9uYW1lID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKG5hbWUpO1xuICAgIGNvbnN0IF9hY3Rpb25lZCA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZihmYWxzZSk7XG4gICAgX25hbWUuY3VycmVudCA9IG5hbWU7XG4gICAgX2ZpZWxkSWRzLmN1cnJlbnQgPSBmaWVsZHM7XG4gICAgY29udHJvbC5fbmFtZXMuYXJyYXkuYWRkKG5hbWUpO1xuICAgIHJ1bGVzICYmXG4gICAgICAgIGNvbnRyb2wucmVnaXN0ZXIobmFtZSwgcnVsZXMpO1xuICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4gY29udHJvbC5fc3ViamVjdHMuYXJyYXkuc3Vic2NyaWJlKHtcbiAgICAgICAgbmV4dDogKHsgdmFsdWVzLCBuYW1lOiBmaWVsZEFycmF5TmFtZSwgfSkgPT4ge1xuICAgICAgICAgICAgaWYgKGZpZWxkQXJyYXlOYW1lID09PSBfbmFtZS5jdXJyZW50IHx8ICFmaWVsZEFycmF5TmFtZSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWVzID0gZ2V0KHZhbHVlcywgX25hbWUuY3VycmVudCk7XG4gICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZmllbGRWYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldEZpZWxkcyhmaWVsZFZhbHVlcyk7XG4gICAgICAgICAgICAgICAgICAgIGlkcy5jdXJyZW50ID0gZmllbGRWYWx1ZXMubWFwKGdlbmVyYXRlSWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9KS51bnN1YnNjcmliZSwgW2NvbnRyb2xdKTtcbiAgICBjb25zdCB1cGRhdGVWYWx1ZXMgPSBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjaygodXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpID0+IHtcbiAgICAgICAgX2FjdGlvbmVkLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICB9LCBbY29udHJvbCwgbmFtZV0pO1xuICAgIGNvbnN0IGFwcGVuZCA9ICh2YWx1ZSwgb3B0aW9ucykgPT4ge1xuICAgICAgICBjb25zdCBhcHBlbmRWYWx1ZSA9IGNvbnZlcnRUb0FycmF5UGF5bG9hZChjbG9uZU9iamVjdCh2YWx1ZSkpO1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IGFwcGVuZEF0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGFwcGVuZFZhbHVlKTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSBnZXRGb2N1c0ZpZWxkTmFtZShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcy5sZW5ndGggLSAxLCBvcHRpb25zKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSBhcHBlbmRBdChpZHMuY3VycmVudCwgYXBwZW5kVmFsdWUubWFwKGdlbmVyYXRlSWQpKTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgYXBwZW5kQXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGZpbGxFbXB0eUFycmF5KHZhbHVlKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBwcmVwZW5kID0gKHZhbHVlLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGNvbnN0IHByZXBlbmRWYWx1ZSA9IGNvbnZlcnRUb0FycmF5UGF5bG9hZChjbG9uZU9iamVjdCh2YWx1ZSkpO1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IHByZXBlbmRBdChjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpLCBwcmVwZW5kVmFsdWUpO1xuICAgICAgICBjb250cm9sLl9uYW1lcy5mb2N1cyA9IGdldEZvY3VzRmllbGROYW1lKG5hbWUsIDAsIG9wdGlvbnMpO1xuICAgICAgICBpZHMuY3VycmVudCA9IHByZXBlbmRBdChpZHMuY3VycmVudCwgcHJlcGVuZFZhbHVlLm1hcChnZW5lcmF0ZUlkKSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIHByZXBlbmRBdCwge1xuICAgICAgICAgICAgYXJnQTogZmlsbEVtcHR5QXJyYXkodmFsdWUpLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHJlbW92ZSA9IChpbmRleCkgPT4ge1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IHJlbW92ZUFycmF5QXQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgaW5kZXgpO1xuICAgICAgICBpZHMuY3VycmVudCA9IHJlbW92ZUFycmF5QXQoaWRzLmN1cnJlbnQsIGluZGV4KTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgIUFycmF5LmlzQXJyYXkoZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSkpICYmXG4gICAgICAgICAgICBzZXQoY29udHJvbC5fZmllbGRzLCBuYW1lLCB1bmRlZmluZWQpO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCByZW1vdmVBcnJheUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleCxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBpbnNlcnQkMSA9IChpbmRleCwgdmFsdWUsIG9wdGlvbnMpID0+IHtcbiAgICAgICAgY29uc3QgaW5zZXJ0VmFsdWUgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBpbnNlcnQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgaW5kZXgsIGluc2VydFZhbHVlKTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSBnZXRGb2N1c0ZpZWxkTmFtZShuYW1lLCBpbmRleCwgb3B0aW9ucyk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gaW5zZXJ0KGlkcy5jdXJyZW50LCBpbmRleCwgaW5zZXJ0VmFsdWUubWFwKGdlbmVyYXRlSWQpKTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgaW5zZXJ0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleCxcbiAgICAgICAgICAgIGFyZ0I6IGZpbGxFbXB0eUFycmF5KHZhbHVlKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBzd2FwID0gKGluZGV4QSwgaW5kZXhCKSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKTtcbiAgICAgICAgc3dhcEFycmF5QXQodXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIGluZGV4QSwgaW5kZXhCKTtcbiAgICAgICAgc3dhcEFycmF5QXQoaWRzLmN1cnJlbnQsIGluZGV4QSwgaW5kZXhCKTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgc3dhcEFycmF5QXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGluZGV4QSxcbiAgICAgICAgICAgIGFyZ0I6IGluZGV4QixcbiAgICAgICAgfSwgZmFsc2UpO1xuICAgIH07XG4gICAgY29uc3QgbW92ZSA9IChmcm9tLCB0bykgPT4ge1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSk7XG4gICAgICAgIG1vdmVBcnJheUF0KHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBmcm9tLCB0byk7XG4gICAgICAgIG1vdmVBcnJheUF0KGlkcy5jdXJyZW50LCBmcm9tLCB0byk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIG1vdmVBcnJheUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBmcm9tLFxuICAgICAgICAgICAgYXJnQjogdG8sXG4gICAgICAgIH0sIGZhbHNlKTtcbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZSA9IChpbmRleCwgdmFsdWUpID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlVmFsdWUgPSBjbG9uZU9iamVjdCh2YWx1ZSk7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gdXBkYXRlQXQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgaW5kZXgsIHVwZGF0ZVZhbHVlKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSBbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdLm1hcCgoaXRlbSwgaSkgPT4gIWl0ZW0gfHwgaSA9PT0gaW5kZXggPyBnZW5lcmF0ZUlkKCkgOiBpZHMuY3VycmVudFtpXSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyhbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgdXBkYXRlQXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGluZGV4LFxuICAgICAgICAgICAgYXJnQjogdXBkYXRlVmFsdWUsXG4gICAgICAgIH0sIHRydWUsIGZhbHNlKTtcbiAgICB9O1xuICAgIGNvbnN0IHJlcGxhY2UgPSAodmFsdWUpID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSB1cGRhdGVkRmllbGRBcnJheVZhbHVlcy5tYXAoZ2VuZXJhdGVJZCk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyhbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdKTtcbiAgICAgICAgc2V0RmllbGRzKFsuLi51cGRhdGVkRmllbGRBcnJheVZhbHVlc10pO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIFsuLi51cGRhdGVkRmllbGRBcnJheVZhbHVlc10sIChkYXRhKSA9PiBkYXRhLCB7fSwgdHJ1ZSwgZmFsc2UpO1xuICAgIH07XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgY29udHJvbC5fc3RhdGUuYWN0aW9uID0gZmFsc2U7XG4gICAgICAgIGlzV2F0Y2hlZChuYW1lLCBjb250cm9sLl9uYW1lcykgJiZcbiAgICAgICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIC4uLmNvbnRyb2wuX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICBpZiAoX2FjdGlvbmVkLmN1cnJlbnQgJiZcbiAgICAgICAgICAgICghZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMubW9kZSkuaXNPblN1Ym1pdCB8fFxuICAgICAgICAgICAgICAgIGNvbnRyb2wuX2Zvcm1TdGF0ZS5pc1N1Ym1pdHRlZCkgJiZcbiAgICAgICAgICAgICFnZXRWYWxpZGF0aW9uTW9kZXMoY29udHJvbC5fb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSkuaXNPblN1Ym1pdCkge1xuICAgICAgICAgICAgaWYgKGNvbnRyb2wuX29wdGlvbnMucmVzb2x2ZXIpIHtcbiAgICAgICAgICAgICAgICBjb250cm9sLl9ydW5TY2hlbWEoW25hbWVdKS50aGVuKChyZXN1bHQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBnZXQocmVzdWx0LmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nRXJyb3IgPSBnZXQoY29udHJvbC5fZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChleGlzdGluZ0Vycm9yXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICghZXJyb3IgJiYgZXhpc3RpbmdFcnJvci50eXBlKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvciAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXhpc3RpbmdFcnJvci50eXBlICE9PSBlcnJvci50eXBlIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ0Vycm9yLm1lc3NhZ2UgIT09IGVycm9yLm1lc3NhZ2UpKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBlcnJvciAmJiBlcnJvci50eXBlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gc2V0KGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIGVycm9yKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdW5zZXQoY29udHJvbC5fZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnM6IGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoY29udHJvbC5fZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoZmllbGQgJiZcbiAgICAgICAgICAgICAgICAgICAgZmllbGQuX2YgJiZcbiAgICAgICAgICAgICAgICAgICAgIShnZXRWYWxpZGF0aW9uTW9kZXMoY29udHJvbC5fb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSkuaXNPblN1Ym1pdCAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMubW9kZSkuaXNPblN1Ym1pdCkpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsaWRhdGVGaWVsZChmaWVsZCwgY29udHJvbC5fbmFtZXMuZGlzYWJsZWQsIGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIGNvbnRyb2wuX29wdGlvbnMuY3JpdGVyaWFNb2RlID09PSBWQUxJREFUSU9OX01PREUuYWxsLCBjb250cm9sLl9vcHRpb25zLnNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24sIHRydWUpLnRoZW4oKGVycm9yKSA9PiAhaXNFbXB0eU9iamVjdChlcnJvcikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yczogdXBkYXRlRmllbGRBcnJheVJvb3RFcnJvcihjb250cm9sLl9mb3JtU3RhdGUuZXJyb3JzLCBlcnJvciwgbmFtZSksXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoY29udHJvbC5fZm9ybVZhbHVlcyksXG4gICAgICAgIH0pO1xuICAgICAgICBjb250cm9sLl9uYW1lcy5mb2N1cyAmJlxuICAgICAgICAgICAgaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKGNvbnRyb2wuX2ZpZWxkcywgKHJlZiwga2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGNvbnRyb2wuX25hbWVzLmZvY3VzICYmXG4gICAgICAgICAgICAgICAgICAgIGtleS5zdGFydHNXaXRoKGNvbnRyb2wuX25hbWVzLmZvY3VzKSAmJlxuICAgICAgICAgICAgICAgICAgICByZWYuZm9jdXMpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVmLmZvY3VzKCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAxO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSAnJztcbiAgICAgICAgY29udHJvbC5fc2V0VmFsaWQoKTtcbiAgICAgICAgX2FjdGlvbmVkLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9LCBbZmllbGRzLCBuYW1lLCBjb250cm9sXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgIWdldChjb250cm9sLl9mb3JtVmFsdWVzLCBuYW1lKSAmJiBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUpO1xuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgdXBkYXRlTW91bnRlZCA9IChuYW1lLCB2YWx1ZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkLl9mLm1vdW50ID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGNvbnRyb2wuX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciB8fCBzaG91bGRVbnJlZ2lzdGVyXG4gICAgICAgICAgICAgICAgPyBjb250cm9sLnVucmVnaXN0ZXIobmFtZSlcbiAgICAgICAgICAgICAgICA6IHVwZGF0ZU1vdW50ZWQobmFtZSwgZmFsc2UpO1xuICAgICAgICB9O1xuICAgIH0sIFtuYW1lLCBjb250cm9sLCBrZXlOYW1lLCBzaG91bGRVbnJlZ2lzdGVyXSk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc3dhcDogUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soc3dhcCwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICBtb3ZlOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhtb3ZlLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIHByZXBlbmQ6IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKHByZXBlbmQsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgYXBwZW5kOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhhcHBlbmQsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgcmVtb3ZlOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhyZW1vdmUsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgaW5zZXJ0OiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhpbnNlcnQkMSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICB1cGRhdGU6IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKHVwZGF0ZSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICByZXBsYWNlOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhyZXBsYWNlLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIGZpZWxkczogUmVhY3RfX2RlZmF1bHQudXNlTWVtbygoKSA9PiBmaWVsZHMubWFwKChmaWVsZCwgaW5kZXgpID0+ICh7XG4gICAgICAgICAgICAuLi5maWVsZCxcbiAgICAgICAgICAgIFtrZXlOYW1lXTogaWRzLmN1cnJlbnRbaW5kZXhdIHx8IGdlbmVyYXRlSWQoKSxcbiAgICAgICAgfSkpLCBbZmllbGRzLCBrZXlOYW1lXSksXG4gICAgfTtcbn1cblxuLyoqXG4gKiBDdXN0b20gaG9vayB0byBtYW5hZ2UgdGhlIGVudGlyZSBmb3JtLlxuICpcbiAqIEByZW1hcmtzXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2Vmb3JtKSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tZ2V0LXN0YXJ0ZWQtdHMtNWtzbW0pIOKAoiBbVmlkZW9dKGh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9UmtYdjRBWFhDXzQpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gZm9ybSBjb25maWd1cmF0aW9uIGFuZCB2YWxpZGF0aW9uIHBhcmFtZXRlcnMuXG4gKlxuICogQHJldHVybnMgbWV0aG9kcyAtIGluZGl2aWR1YWwgZnVuY3Rpb25zIHRvIG1hbmFnZSB0aGUgZm9ybSBzdGF0ZS4ge0BsaW5rIFVzZUZvcm1SZXR1cm59XG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IHJlZ2lzdGVyLCBoYW5kbGVTdWJtaXQsIHdhdGNoLCBmb3JtU3RhdGU6IHsgZXJyb3JzIH0gfSA9IHVzZUZvcm0oKTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSBkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpO1xuICpcbiAqICAgY29uc29sZS5sb2cod2F0Y2goXCJleGFtcGxlXCIpKTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChvblN1Ym1pdCl9PlxuICogICAgICAgPGlucHV0IGRlZmF1bHRWYWx1ZT1cInRlc3RcIiB7Li4ucmVnaXN0ZXIoXCJleGFtcGxlXCIpfSAvPlxuICogICAgICAgPGlucHV0IHsuLi5yZWdpc3RlcihcImV4YW1wbGVSZXF1aXJlZFwiLCB7IHJlcXVpcmVkOiB0cnVlIH0pfSAvPlxuICogICAgICAge2Vycm9ycy5leGFtcGxlUmVxdWlyZWQgJiYgPHNwYW4+VGhpcyBmaWVsZCBpcyByZXF1aXJlZDwvc3Bhbj59XG4gKiAgICAgICA8YnV0dG9uPlN1Ym1pdDwvYnV0dG9uPlxuICogICAgIDwvZm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VGb3JtKHByb3BzID0ge30pIHtcbiAgICBjb25zdCBfZm9ybUNvbnRyb2wgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYodW5kZWZpbmVkKTtcbiAgICBjb25zdCBfdmFsdWVzID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKHVuZGVmaW5lZCk7XG4gICAgY29uc3QgW2Zvcm1TdGF0ZSwgdXBkYXRlRm9ybVN0YXRlXSA9IFJlYWN0X19kZWZhdWx0LnVzZVN0YXRlKHtcbiAgICAgICAgaXNEaXJ0eTogZmFsc2UsXG4gICAgICAgIGlzVmFsaWRhdGluZzogZmFsc2UsXG4gICAgICAgIGlzTG9hZGluZzogaXNGdW5jdGlvbihwcm9wcy5kZWZhdWx0VmFsdWVzKSxcbiAgICAgICAgaXNTdWJtaXR0ZWQ6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdHRpbmc6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgc3VibWl0Q291bnQ6IDAsXG4gICAgICAgIGRpcnR5RmllbGRzOiB7fSxcbiAgICAgICAgdG91Y2hlZEZpZWxkczoge30sXG4gICAgICAgIHZhbGlkYXRpbmdGaWVsZHM6IHt9LFxuICAgICAgICBlcnJvcnM6IHByb3BzLmVycm9ycyB8fCB7fSxcbiAgICAgICAgZGlzYWJsZWQ6IHByb3BzLmRpc2FibGVkIHx8IGZhbHNlLFxuICAgICAgICBpc1JlYWR5OiBmYWxzZSxcbiAgICAgICAgZGVmYXVsdFZhbHVlczogaXNGdW5jdGlvbihwcm9wcy5kZWZhdWx0VmFsdWVzKVxuICAgICAgICAgICAgPyB1bmRlZmluZWRcbiAgICAgICAgICAgIDogcHJvcHMuZGVmYXVsdFZhbHVlcyxcbiAgICB9KTtcbiAgICBpZiAoIV9mb3JtQ29udHJvbC5jdXJyZW50KSB7XG4gICAgICAgIGlmIChwcm9wcy5mb3JtQ29udHJvbCkge1xuICAgICAgICAgICAgX2Zvcm1Db250cm9sLmN1cnJlbnQgPSB7XG4gICAgICAgICAgICAgICAgLi4ucHJvcHMuZm9ybUNvbnRyb2wsXG4gICAgICAgICAgICAgICAgZm9ybVN0YXRlLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmIChwcm9wcy5kZWZhdWx0VmFsdWVzICYmICFpc0Z1bmN0aW9uKHByb3BzLmRlZmF1bHRWYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgcHJvcHMuZm9ybUNvbnRyb2wucmVzZXQocHJvcHMuZGVmYXVsdFZhbHVlcywgcHJvcHMucmVzZXRPcHRpb25zKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IHsgZm9ybUNvbnRyb2wsIC4uLnJlc3QgfSA9IGNyZWF0ZUZvcm1Db250cm9sKHByb3BzKTtcbiAgICAgICAgICAgIF9mb3JtQ29udHJvbC5jdXJyZW50ID0ge1xuICAgICAgICAgICAgICAgIC4uLnJlc3QsXG4gICAgICAgICAgICAgICAgZm9ybVN0YXRlLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjb25zdCBjb250cm9sID0gX2Zvcm1Db250cm9sLmN1cnJlbnQuY29udHJvbDtcbiAgICBjb250cm9sLl9vcHRpb25zID0gcHJvcHM7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IHN1YiA9IGNvbnRyb2wuX3N1YnNjcmliZSh7XG4gICAgICAgICAgICBmb3JtU3RhdGU6IGNvbnRyb2wuX3Byb3h5Rm9ybVN0YXRlLFxuICAgICAgICAgICAgY2FsbGJhY2s6ICgpID0+IHVwZGF0ZUZvcm1TdGF0ZSh7IC4uLmNvbnRyb2wuX2Zvcm1TdGF0ZSB9KSxcbiAgICAgICAgICAgIHJlUmVuZGVyUm9vdDogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHVwZGF0ZUZvcm1TdGF0ZSgoZGF0YSkgPT4gKHtcbiAgICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgICBpc1JlYWR5OiB0cnVlLFxuICAgICAgICB9KSk7XG4gICAgICAgIGNvbnRyb2wuX2Zvcm1TdGF0ZS5pc1JlYWR5ID0gdHJ1ZTtcbiAgICAgICAgcmV0dXJuIHN1YjtcbiAgICB9LCBbY29udHJvbF0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiBjb250cm9sLl9kaXNhYmxlRm9ybShwcm9wcy5kaXNhYmxlZCksIFtjb250cm9sLCBwcm9wcy5kaXNhYmxlZF0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChwcm9wcy5tb2RlKSB7XG4gICAgICAgICAgICBjb250cm9sLl9vcHRpb25zLm1vZGUgPSBwcm9wcy5tb2RlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm9wcy5yZVZhbGlkYXRlTW9kZSkge1xuICAgICAgICAgICAgY29udHJvbC5fb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSA9IHByb3BzLnJlVmFsaWRhdGVNb2RlO1xuICAgICAgICB9XG4gICAgfSwgW2NvbnRyb2wsIHByb3BzLm1vZGUsIHByb3BzLnJlVmFsaWRhdGVNb2RlXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKHByb3BzLmVycm9ycykge1xuICAgICAgICAgICAgY29udHJvbC5fc2V0RXJyb3JzKHByb3BzLmVycm9ycyk7XG4gICAgICAgICAgICBjb250cm9sLl9mb2N1c0Vycm9yKCk7XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbCwgcHJvcHMuZXJyb3JzXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgcHJvcHMuc2hvdWxkVW5yZWdpc3RlciAmJlxuICAgICAgICAgICAgY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgdmFsdWVzOiBjb250cm9sLl9nZXRXYXRjaCgpLFxuICAgICAgICAgICAgfSk7XG4gICAgfSwgW2NvbnRyb2wsIHByb3BzLnNob3VsZFVucmVnaXN0ZXJdKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoY29udHJvbC5fcHJveHlGb3JtU3RhdGUuaXNEaXJ0eSkge1xuICAgICAgICAgICAgY29uc3QgaXNEaXJ0eSA9IGNvbnRyb2wuX2dldERpcnR5KCk7XG4gICAgICAgICAgICBpZiAoaXNEaXJ0eSAhPT0gZm9ybVN0YXRlLmlzRGlydHkpIHtcbiAgICAgICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgaXNEaXJ0eSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0sIFtjb250cm9sLCBmb3JtU3RhdGUuaXNEaXJ0eV0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChwcm9wcy52YWx1ZXMgJiYgIWRlZXBFcXVhbChwcm9wcy52YWx1ZXMsIF92YWx1ZXMuY3VycmVudCkpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX3Jlc2V0KHByb3BzLnZhbHVlcywgY29udHJvbC5fb3B0aW9ucy5yZXNldE9wdGlvbnMpO1xuICAgICAgICAgICAgX3ZhbHVlcy5jdXJyZW50ID0gcHJvcHMudmFsdWVzO1xuICAgICAgICAgICAgdXBkYXRlRm9ybVN0YXRlKChzdGF0ZSkgPT4gKHsgLi4uc3RhdGUgfSkpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29udHJvbC5fcmVzZXREZWZhdWx0VmFsdWVzKCk7XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbCwgcHJvcHMudmFsdWVzXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCFjb250cm9sLl9zdGF0ZS5tb3VudCkge1xuICAgICAgICAgICAgY29udHJvbC5fc2V0VmFsaWQoKTtcbiAgICAgICAgICAgIGNvbnRyb2wuX3N0YXRlLm1vdW50ID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY29udHJvbC5fc3RhdGUud2F0Y2gpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX3N0YXRlLndhdGNoID0gZmFsc2U7XG4gICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgLi4uY29udHJvbC5fZm9ybVN0YXRlIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnRyb2wuX3JlbW92ZVVubW91bnRlZCgpO1xuICAgIH0pO1xuICAgIF9mb3JtQ29udHJvbC5jdXJyZW50LmZvcm1TdGF0ZSA9IGdldFByb3h5Rm9ybVN0YXRlKGZvcm1TdGF0ZSwgY29udHJvbCk7XG4gICAgcmV0dXJuIF9mb3JtQ29udHJvbC5jdXJyZW50O1xufVxuXG5leHBvcnQgeyBDb250cm9sbGVyLCBGb3JtLCBGb3JtUHJvdmlkZXIsIGFwcGVuZEVycm9ycywgY3JlYXRlRm9ybUNvbnRyb2wsIGdldCwgc2V0LCB1c2VDb250cm9sbGVyLCB1c2VGaWVsZEFycmF5LCB1c2VGb3JtLCB1c2VGb3JtQ29udGV4dCwgdXNlRm9ybVN0YXRlLCB1c2VXYXRjaCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguZXNtLm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlJlYWN0X19kZWZhdWx0IiwiaXNDaGVja0JveElucHV0IiwiZWxlbWVudCIsInR5cGUiLCJpc0RhdGVPYmplY3QiLCJ2YWx1ZSIsIkRhdGUiLCJpc051bGxPclVuZGVmaW5lZCIsImlzT2JqZWN0VHlwZSIsImlzT2JqZWN0IiwiQXJyYXkiLCJpc0FycmF5IiwiZ2V0RXZlbnRWYWx1ZSIsImV2ZW50IiwidGFyZ2V0IiwiY2hlY2tlZCIsImdldE5vZGVQYXJlbnROYW1lIiwibmFtZSIsInN1YnN0cmluZyIsInNlYXJjaCIsImlzTmFtZUluRmllbGRBcnJheSIsIm5hbWVzIiwiaGFzIiwiaXNQbGFpbk9iamVjdCIsInRlbXBPYmplY3QiLCJwcm90b3R5cGVDb3B5IiwiY29uc3RydWN0b3IiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImlzV2ViIiwid2luZG93IiwiSFRNTEVsZW1lbnQiLCJkb2N1bWVudCIsImNsb25lT2JqZWN0IiwiZGF0YSIsImNvcHkiLCJpc0ZpbGVMaXN0SW5zdGFuY2UiLCJGaWxlTGlzdCIsIlNldCIsIkJsb2IiLCJrZXkiLCJpc0tleSIsInRlc3QiLCJpc1VuZGVmaW5lZCIsInZhbCIsInVuZGVmaW5lZCIsImNvbXBhY3QiLCJmaWx0ZXIiLCJCb29sZWFuIiwic3RyaW5nVG9QYXRoIiwiaW5wdXQiLCJyZXBsYWNlIiwic3BsaXQiLCJnZXQiLCJvYmplY3QiLCJwYXRoIiwiZGVmYXVsdFZhbHVlIiwicmVzdWx0IiwicmVkdWNlIiwiaXNCb29sZWFuIiwic2V0IiwiaW5kZXgiLCJ0ZW1wUGF0aCIsImxlbmd0aCIsImxhc3RJbmRleCIsIm5ld1ZhbHVlIiwib2JqVmFsdWUiLCJpc05hTiIsIkVWRU5UUyIsIkJMVVIiLCJGT0NVU19PVVQiLCJDSEFOR0UiLCJWQUxJREFUSU9OX01PREUiLCJvbkJsdXIiLCJvbkNoYW5nZSIsIm9uU3VibWl0Iiwib25Ub3VjaGVkIiwiYWxsIiwiSU5QVVRfVkFMSURBVElPTl9SVUxFUyIsIm1heCIsIm1pbiIsIm1heExlbmd0aCIsIm1pbkxlbmd0aCIsInBhdHRlcm4iLCJyZXF1aXJlZCIsInZhbGlkYXRlIiwiSG9va0Zvcm1Db250ZXh0IiwiY3JlYXRlQ29udGV4dCIsImRpc3BsYXlOYW1lIiwidXNlRm9ybUNvbnRleHQiLCJ1c2VDb250ZXh0IiwiRm9ybVByb3ZpZGVyIiwicHJvcHMiLCJjaGlsZHJlbiIsImNyZWF0ZUVsZW1lbnQiLCJQcm92aWRlciIsImdldFByb3h5Rm9ybVN0YXRlIiwiZm9ybVN0YXRlIiwiY29udHJvbCIsImxvY2FsUHJveHlGb3JtU3RhdGUiLCJpc1Jvb3QiLCJkZWZhdWx0VmFsdWVzIiwiX2RlZmF1bHRWYWx1ZXMiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsIl9rZXkiLCJfcHJveHlGb3JtU3RhdGUiLCJ1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IiwidXNlTGF5b3V0RWZmZWN0IiwidXNlRWZmZWN0IiwidXNlRm9ybVN0YXRlIiwibWV0aG9kcyIsImRpc2FibGVkIiwiZXhhY3QiLCJ1cGRhdGVGb3JtU3RhdGUiLCJ1c2VTdGF0ZSIsIl9mb3JtU3RhdGUiLCJfbG9jYWxQcm94eUZvcm1TdGF0ZSIsInVzZVJlZiIsImlzRGlydHkiLCJpc0xvYWRpbmciLCJkaXJ0eUZpZWxkcyIsInRvdWNoZWRGaWVsZHMiLCJ2YWxpZGF0aW5nRmllbGRzIiwiaXNWYWxpZGF0aW5nIiwiaXNWYWxpZCIsImVycm9ycyIsIl9zdWJzY3JpYmUiLCJjdXJyZW50IiwiY2FsbGJhY2siLCJfc2V0VmFsaWQiLCJ1c2VNZW1vIiwiaXNTdHJpbmciLCJnZW5lcmF0ZVdhdGNoT3V0cHV0IiwiX25hbWVzIiwiZm9ybVZhbHVlcyIsImlzR2xvYmFsIiwid2F0Y2giLCJhZGQiLCJtYXAiLCJmaWVsZE5hbWUiLCJ3YXRjaEFsbCIsInVzZVdhdGNoIiwiX2RlZmF1bHRWYWx1ZSIsInVwZGF0ZVZhbHVlIiwiX2dldFdhdGNoIiwidmFsdWVzIiwiX2Zvcm1WYWx1ZXMiLCJfcmVtb3ZlVW5tb3VudGVkIiwidXNlQ29udHJvbGxlciIsInNob3VsZFVucmVnaXN0ZXIiLCJpc0FycmF5RmllbGQiLCJhcnJheSIsIl9wcm9wcyIsIl9yZWdpc3RlclByb3BzIiwicmVnaXN0ZXIiLCJydWxlcyIsImZpZWxkU3RhdGUiLCJkZWZpbmVQcm9wZXJ0aWVzIiwiaW52YWxpZCIsImVudW1lcmFibGUiLCJpc1RvdWNoZWQiLCJlcnJvciIsInVzZUNhbGxiYWNrIiwicmVmIiwiZWxtIiwiZmllbGQiLCJfZmllbGRzIiwiX2YiLCJmb2N1cyIsInNlbGVjdCIsInNldEN1c3RvbVZhbGlkaXR5IiwibWVzc2FnZSIsInJlcG9ydFZhbGlkaXR5IiwiX3Nob3VsZFVucmVnaXN0ZXJGaWVsZCIsIl9vcHRpb25zIiwidXBkYXRlTW91bnRlZCIsIm1vdW50IiwiX3N0YXRlIiwiYWN0aW9uIiwidW5yZWdpc3RlciIsIl9zZXREaXNhYmxlZEZpZWxkIiwiQ29udHJvbGxlciIsInJlbmRlciIsImZsYXR0ZW4iLCJvYmoiLCJvdXRwdXQiLCJrZXlzIiwibmVzdGVkIiwibmVzdGVkS2V5IiwiUE9TVF9SRVFVRVNUIiwiRm9ybSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwibWV0aG9kIiwiaGVhZGVycyIsImVuY1R5cGUiLCJvbkVycm9yIiwib25TdWNjZXNzIiwidmFsaWRhdGVTdGF0dXMiLCJyZXN0Iiwic3VibWl0IiwiaGFzRXJyb3IiLCJoYW5kbGVTdWJtaXQiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiZm9ybURhdGFKc29uIiwiSlNPTiIsInN0cmluZ2lmeSIsIl9hIiwiZmxhdHRlbkZvcm1WYWx1ZXMiLCJhcHBlbmQiLCJzaG91bGRTdHJpbmdpZnlTdWJtaXNzaW9uRGF0YSIsInNvbWUiLCJpbmNsdWRlcyIsInJlc3BvbnNlIiwiZmV0Y2giLCJTdHJpbmciLCJib2R5Iiwic3RhdHVzIiwiX3N1YmplY3RzIiwic3RhdGUiLCJuZXh0IiwiaXNTdWJtaXRTdWNjZXNzZnVsIiwic2V0RXJyb3IiLCJGcmFnbWVudCIsIm5vVmFsaWRhdGUiLCJhcHBlbmRFcnJvcnMiLCJ2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEiLCJ0eXBlcyIsImNvbnZlcnRUb0FycmF5UGF5bG9hZCIsImNyZWF0ZVN1YmplY3QiLCJfb2JzZXJ2ZXJzIiwib2JzZXJ2ZXIiLCJzdWJzY3JpYmUiLCJwdXNoIiwidW5zdWJzY3JpYmUiLCJvIiwib2JzZXJ2ZXJzIiwiaXNQcmltaXRpdmUiLCJkZWVwRXF1YWwiLCJvYmplY3QxIiwib2JqZWN0MiIsIl9pbnRlcm5hbF92aXNpdGVkIiwiV2Vha1NldCIsImdldFRpbWUiLCJrZXlzMSIsImtleXMyIiwidmFsMSIsInZhbDIiLCJpc0VtcHR5T2JqZWN0IiwiaXNGaWxlSW5wdXQiLCJpc0Z1bmN0aW9uIiwiaXNIVE1MRWxlbWVudCIsIm93bmVyIiwib3duZXJEb2N1bWVudCIsImRlZmF1bHRWaWV3IiwiaXNNdWx0aXBsZVNlbGVjdCIsImlzUmFkaW9JbnB1dCIsImlzUmFkaW9PckNoZWNrYm94IiwibGl2ZSIsImlzQ29ubmVjdGVkIiwiYmFzZUdldCIsInVwZGF0ZVBhdGgiLCJzbGljZSIsImlzRW1wdHlBcnJheSIsInVuc2V0IiwicGF0aHMiLCJjaGlsZE9iamVjdCIsIm9iamVjdEhhc0Z1bmN0aW9uIiwibWFya0ZpZWxkc0RpcnR5IiwiZmllbGRzIiwiaXNQYXJlbnROb2RlQXJyYXkiLCJnZXREaXJ0eUZpZWxkc0Zyb21EZWZhdWx0VmFsdWVzIiwiZGlydHlGaWVsZHNGcm9tVmFsdWVzIiwiZ2V0RGlydHlGaWVsZHMiLCJkZWZhdWx0UmVzdWx0IiwidmFsaWRSZXN1bHQiLCJnZXRDaGVja2JveFZhbHVlIiwib3B0aW9ucyIsIm9wdGlvbiIsImF0dHJpYnV0ZXMiLCJnZXRGaWVsZFZhbHVlQXMiLCJ2YWx1ZUFzTnVtYmVyIiwidmFsdWVBc0RhdGUiLCJzZXRWYWx1ZUFzIiwiTmFOIiwiZGVmYXVsdFJldHVybiIsImdldFJhZGlvVmFsdWUiLCJwcmV2aW91cyIsImdldEZpZWxkVmFsdWUiLCJmaWxlcyIsInJlZnMiLCJzZWxlY3RlZE9wdGlvbnMiLCJnZXRSZXNvbHZlck9wdGlvbnMiLCJmaWVsZHNOYW1lcyIsImNyaXRlcmlhTW9kZSIsInNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24iLCJpc1JlZ2V4IiwiUmVnRXhwIiwiZ2V0UnVsZVZhbHVlIiwicnVsZSIsInNvdXJjZSIsImdldFZhbGlkYXRpb25Nb2RlcyIsIm1vZGUiLCJpc09uU3VibWl0IiwiaXNPbkJsdXIiLCJpc09uQ2hhbmdlIiwiaXNPbkFsbCIsImlzT25Ub3VjaCIsIkFTWU5DX0ZVTkNUSU9OIiwiaGFzUHJvbWlzZVZhbGlkYXRpb24iLCJmaWVsZFJlZmVyZW5jZSIsImZpbmQiLCJ2YWxpZGF0ZUZ1bmN0aW9uIiwiaGFzVmFsaWRhdGlvbiIsImlzV2F0Y2hlZCIsImlzQmx1ckV2ZW50Iiwid2F0Y2hOYW1lIiwic3RhcnRzV2l0aCIsIml0ZXJhdGVGaWVsZHNCeUFjdGlvbiIsImFib3J0RWFybHkiLCJjdXJyZW50RmllbGQiLCJzY2hlbWFFcnJvckxvb2t1cCIsImpvaW4iLCJmb3VuZEVycm9yIiwicm9vdCIsInBvcCIsInNob3VsZFJlbmRlckZvcm1TdGF0ZSIsImZvcm1TdGF0ZURhdGEiLCJzaG91bGRTdWJzY3JpYmVCeU5hbWUiLCJzaWduYWxOYW1lIiwiY3VycmVudE5hbWUiLCJza2lwVmFsaWRhdGlvbiIsImlzU3VibWl0dGVkIiwicmVWYWxpZGF0ZU1vZGUiLCJ1bnNldEVtcHR5QXJyYXkiLCJ1cGRhdGVGaWVsZEFycmF5Um9vdEVycm9yIiwiZmllbGRBcnJheUVycm9ycyIsImlzTWVzc2FnZSIsImdldFZhbGlkYXRlRXJyb3IiLCJldmVyeSIsImdldFZhbHVlQW5kTWVzc2FnZSIsInZhbGlkYXRpb25EYXRhIiwidmFsaWRhdGVGaWVsZCIsImRpc2FibGVkRmllbGROYW1lcyIsImlzRmllbGRBcnJheSIsImlucHV0VmFsdWUiLCJpbnB1dFJlZiIsImlzUmFkaW8iLCJpc0NoZWNrQm94IiwiaXNFbXB0eSIsImFwcGVuZEVycm9yc0N1cnJ5IiwiYmluZCIsImdldE1pbk1heE1lc3NhZ2UiLCJleGNlZWRNYXgiLCJtYXhMZW5ndGhNZXNzYWdlIiwibWluTGVuZ3RoTWVzc2FnZSIsIm1heFR5cGUiLCJtaW5UeXBlIiwiZXhjZWVkTWluIiwibWF4T3V0cHV0IiwibWluT3V0cHV0IiwidmFsdWVOdW1iZXIiLCJ2YWx1ZURhdGUiLCJjb252ZXJ0VGltZVRvRGF0ZSIsInRpbWUiLCJ0b0RhdGVTdHJpbmciLCJpc1RpbWUiLCJpc1dlZWsiLCJtYXhMZW5ndGhPdXRwdXQiLCJtaW5MZW5ndGhPdXRwdXQiLCJwYXR0ZXJuVmFsdWUiLCJtYXRjaCIsInZhbGlkYXRlRXJyb3IiLCJ2YWxpZGF0aW9uUmVzdWx0IiwiZGVmYXVsdE9wdGlvbnMiLCJzaG91bGRGb2N1c0Vycm9yIiwiY3JlYXRlRm9ybUNvbnRyb2wiLCJzdWJtaXRDb3VudCIsImlzUmVhZHkiLCJpc1N1Ym1pdHRpbmciLCJ1bk1vdW50IiwiZGVsYXlFcnJvckNhbGxiYWNrIiwidGltZXIiLCJfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUiLCJzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycyIsImRlYm91bmNlIiwid2FpdCIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJzaG91bGRVcGRhdGVWYWxpZCIsInJlc29sdmVyIiwiX3J1blNjaGVtYSIsImV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbiIsIl91cGRhdGVJc1ZhbGlkYXRpbmciLCJmcm9tIiwiZm9yRWFjaCIsIl9zZXRGaWVsZEFycmF5IiwiYXJncyIsInNob3VsZFNldFZhbHVlcyIsInNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlIiwiZmllbGRWYWx1ZXMiLCJhcmdBIiwiYXJnQiIsIl9nZXREaXJ0eSIsInVwZGF0ZUVycm9ycyIsIl9zZXRFcnJvcnMiLCJ1cGRhdGVWYWxpZEFuZFZhbHVlIiwic2hvdWxkU2tpcFNldFZhbHVlQXMiLCJkZWZhdWx0Q2hlY2tlZCIsInNldEZpZWxkVmFsdWUiLCJ1cGRhdGVUb3VjaEFuZERpcnR5IiwiZmllbGRWYWx1ZSIsInNob3VsZERpcnR5Iiwic2hvdWxkUmVuZGVyIiwic2hvdWxkVXBkYXRlRmllbGQiLCJpc1ByZXZpb3VzRGlydHkiLCJpc0N1cnJlbnRGaWVsZFByaXN0aW5lIiwiaXNQcmV2aW91c0ZpZWxkVG91Y2hlZCIsInNob3VsZFJlbmRlckJ5RXJyb3IiLCJwcmV2aW91c0ZpZWxkRXJyb3IiLCJkZWxheUVycm9yIiwidXBkYXRlZEZvcm1TdGF0ZSIsImNvbnRleHQiLCJleGVjdXRlU2NoZW1hQW5kVXBkYXRlU3RhdGUiLCJzaG91bGRPbmx5Q2hlY2tWYWxpZCIsInZhbGlkIiwiaXNGaWVsZEFycmF5Um9vdCIsImlzUHJvbWlzZUZ1bmN0aW9uIiwiZmllbGRFcnJvciIsImdldFZhbHVlcyIsIl9nZXRGaWVsZEFycmF5Iiwib3B0aW9uUmVmIiwic2VsZWN0ZWQiLCJjaGVja2JveFJlZiIsInJhZGlvUmVmIiwic2hvdWxkVG91Y2giLCJzaG91bGRWYWxpZGF0ZSIsInRyaWdnZXIiLCJzZXRWYWx1ZXMiLCJmaWVsZEtleSIsInNldFZhbHVlIiwiY2xvbmVWYWx1ZSIsImlzRmllbGRWYWx1ZVVwZGF0ZWQiLCJfdXBkYXRlSXNGaWVsZFZhbHVlVXBkYXRlZCIsIk51bWJlciIsInZhbGlkYXRpb25Nb2RlQmVmb3JlU3VibWl0IiwidmFsaWRhdGlvbk1vZGVBZnRlclN1Ym1pdCIsInNob3VsZFNraXBWYWxpZGF0aW9uIiwiZGVwcyIsIndhdGNoZWQiLCJwcmV2aW91c0Vycm9yTG9va3VwUmVzdWx0IiwiZXJyb3JMb29rdXBSZXN1bHQiLCJfZm9jdXNJbnB1dCIsImZpZWxkTmFtZXMiLCJQcm9taXNlIiwic2hvdWxkRm9jdXMiLCJnZXRGaWVsZFN0YXRlIiwiY2xlYXJFcnJvcnMiLCJpbnB1dE5hbWUiLCJjdXJyZW50RXJyb3IiLCJjdXJyZW50UmVmIiwicmVzdE9mRXJyb3JUcmVlIiwicGF5bG9hZCIsIl9zZXRGb3JtU3RhdGUiLCJyZVJlbmRlclJvb3QiLCJkZWxldGUiLCJrZWVwVmFsdWUiLCJrZWVwRXJyb3IiLCJrZWVwRGlydHkiLCJrZWVwVG91Y2hlZCIsImtlZXBJc1ZhbGlkYXRpbmciLCJrZWVwRGVmYXVsdFZhbHVlIiwia2VlcElzVmFsaWQiLCJkaXNhYmxlZElzRGVmaW5lZCIsInByb2dyZXNzaXZlIiwiZmllbGRSZWYiLCJxdWVyeVNlbGVjdG9yQWxsIiwicmFkaW9PckNoZWNrYm94IiwiX2ZvY3VzRXJyb3IiLCJfZGlzYWJsZUZvcm0iLCJvblZhbGlkIiwib25JbnZhbGlkIiwiZSIsIm9uVmFsaWRFcnJvciIsInByZXZlbnREZWZhdWx0IiwicGVyc2lzdCIsInNpemUiLCJyZXNldEZpZWxkIiwiX3Jlc2V0Iiwia2VlcFN0YXRlT3B0aW9ucyIsInVwZGF0ZWRWYWx1ZXMiLCJjbG9uZVVwZGF0ZWRWYWx1ZXMiLCJpc0VtcHR5UmVzZXRWYWx1ZXMiLCJrZWVwRGVmYXVsdFZhbHVlcyIsImtlZXBWYWx1ZXMiLCJrZWVwRGlydHlWYWx1ZXMiLCJmaWVsZHNUb0NoZWNrIiwiZm9ybSIsImNsb3Nlc3QiLCJyZXNldCIsImtlZXBTdWJtaXRDb3VudCIsImtlZXBJc1N1Ym1pdHRlZCIsImtlZXBFcnJvcnMiLCJrZWVwSXNTdWJtaXRTdWNjZXNzZnVsIiwic2V0Rm9jdXMiLCJzaG91bGRTZWxlY3QiLCJfcmVzZXREZWZhdWx0VmFsdWVzIiwidGhlbiIsInJlc2V0T3B0aW9ucyIsImZvcm1Db250cm9sIiwiZ2VuZXJhdGVJZCIsImNyeXB0byIsInJhbmRvbVVVSUQiLCJkIiwicGVyZm9ybWFuY2UiLCJub3ciLCJjIiwiciIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsImdldEZvY3VzRmllbGROYW1lIiwiZm9jdXNOYW1lIiwiZm9jdXNJbmRleCIsImFwcGVuZEF0IiwiZmlsbEVtcHR5QXJyYXkiLCJpbnNlcnQiLCJtb3ZlQXJyYXlBdCIsInRvIiwic3BsaWNlIiwicHJlcGVuZEF0IiwicmVtb3ZlQXRJbmRleGVzIiwiaW5kZXhlcyIsImkiLCJ0ZW1wIiwicmVtb3ZlQXJyYXlBdCIsInNvcnQiLCJhIiwiYiIsInN3YXBBcnJheUF0IiwiaW5kZXhBIiwiaW5kZXhCIiwidXBkYXRlQXQiLCJ1c2VGaWVsZEFycmF5Iiwia2V5TmFtZSIsInNldEZpZWxkcyIsImlkcyIsIl9maWVsZElkcyIsIl9uYW1lIiwiX2FjdGlvbmVkIiwiZmllbGRBcnJheU5hbWUiLCJ1cGRhdGVWYWx1ZXMiLCJ1cGRhdGVkRmllbGRBcnJheVZhbHVlcyIsImFwcGVuZFZhbHVlIiwicHJlcGVuZCIsInByZXBlbmRWYWx1ZSIsInJlbW92ZSIsImluc2VydCQxIiwiaW5zZXJ0VmFsdWUiLCJzd2FwIiwibW92ZSIsInVwZGF0ZSIsIml0ZW0iLCJleGlzdGluZ0Vycm9yIiwidXNlRm9ybSIsIl9mb3JtQ29udHJvbCIsIl92YWx1ZXMiLCJzdWIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;