"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n    return Math.min(max, Math.max(min, value));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEscUNBQXFDO0FBQ3JDLFNBQVNBLE1BQU1DLEtBQUssRUFBRSxDQUFDQyxLQUFLQyxJQUFJO0lBQzlCLE9BQU9DLEtBQUtGLEdBQUcsQ0FBQ0MsS0FBS0MsS0FBS0QsR0FBRyxDQUFDRCxLQUFLRDtBQUNyQztBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va21hLXNjaGVkdWxlLW5nb3Nhbmducy8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvbnVtYmVyL2Rpc3QvaW5kZXgubWpzPzBhNzciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9udW1iZXIvc3JjL251bWJlci50c1xuZnVuY3Rpb24gY2xhbXAodmFsdWUsIFttaW4sIG1heF0pIHtcbiAgcmV0dXJuIE1hdGgubWluKG1heCwgTWF0aC5tYXgobWluLCB2YWx1ZSkpO1xufVxuZXhwb3J0IHtcbiAgY2xhbXBcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiY2xhbXAiLCJ2YWx1ZSIsIm1pbiIsIm1heCIsIk1hdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler?.(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n            return ourEventHandler?.(event);\n        }\n    };\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNENBQTRDO0FBQzVDLFNBQVNBLHFCQUFxQkMsb0JBQW9CLEVBQUVDLGVBQWUsRUFBRSxFQUFFQywyQkFBMkIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQzNHLE9BQU8sU0FBU0MsWUFBWUMsS0FBSztRQUMvQkosdUJBQXVCSTtRQUN2QixJQUFJRiw2QkFBNkIsU0FBUyxDQUFDRSxNQUFNQyxnQkFBZ0IsRUFBRTtZQUNqRSxPQUFPSixrQkFBa0JHO1FBQzNCO0lBQ0Y7QUFDRjtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va21hLXNjaGVkdWxlLW5nb3Nhbmducy8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzPzE4NjgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiY29tcG9zZUV2ZW50SGFuZGxlcnMiLCJvcmlnaW5hbEV2ZW50SGFuZGxlciIsIm91ckV2ZW50SGFuZGxlciIsImNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCIsImhhbmRsZUV2ZW50IiwiZXZlbnQiLCJkZWZhdWx0UHJldmVudGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { children, width = 10, height = 5, ...arrowProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg, {\n        ...arrowProps,\n        ref: forwardedRef,\n        width,\n        height,\n        viewBox: \"0 0 30 10\",\n        preserveAspectRatio: \"none\",\n        children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n            points: \"0,0 30,0 15,10\"\n        })\n    });\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n        ref.current = value;\n    }\n}\nfunction composeRefs(...refs) {\n    return (node)=>{\n        let hasCleanup = false;\n        const cleanups = refs.map((ref)=>{\n            const cleanup = setRef(ref, node);\n            if (!hasCleanup && typeof cleanup == \"function\") {\n                hasCleanup = true;\n            }\n            return cleanup;\n        });\n        if (hasCleanup) {\n            return ()=>{\n                for(let i = 0; i < cleanups.length; i++){\n                    const cleanup = cleanups[i];\n                    if (typeof cleanup == \"function\") {\n                        cleanup();\n                    } else {\n                        setRef(refs[i], null);\n                    }\n                }\n            };\n        }\n    };\n}\nfunction useComposedRefs(...refs) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props)=>{\n    const { dir, children } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, {\n        value: dir,\n        children\n    });\n};\nfunction useDirection(localDir) {\n    const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n    return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLDZDQUE2QztBQUNkO0FBQ1M7QUFDeEMsSUFBSUUsaUNBQW1CRixnREFBbUIsQ0FBQyxLQUFLO0FBQ2hELElBQUlJLG9CQUFvQixDQUFDQztJQUN2QixNQUFNLEVBQUVDLEdBQUcsRUFBRUMsUUFBUSxFQUFFLEdBQUdGO0lBQzFCLE9BQU8sYUFBYSxHQUFHSixzREFBR0EsQ0FBQ0MsaUJBQWlCTSxRQUFRLEVBQUU7UUFBRUMsT0FBT0g7UUFBS0M7SUFBUztBQUMvRTtBQUNBLFNBQVNHLGFBQWFDLFFBQVE7SUFDNUIsTUFBTUMsWUFBWVosNkNBQWdCLENBQUNFO0lBQ25DLE9BQU9TLFlBQVlDLGFBQWE7QUFDbEM7QUFDQSxJQUFJSixXQUFXSjtBQUtiLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va21hLXNjaGVkdWxlLW5nb3Nhbmducy8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtZGlyZWN0aW9uL2Rpc3QvaW5kZXgubWpzPzc1ODMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvZGlyZWN0aW9uL3NyYy9kaXJlY3Rpb24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIERpcmVjdGlvbkNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KHZvaWQgMCk7XG52YXIgRGlyZWN0aW9uUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgY29uc3QgeyBkaXIsIGNoaWxkcmVuIH0gPSBwcm9wcztcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goRGlyZWN0aW9uQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogZGlyLCBjaGlsZHJlbiB9KTtcbn07XG5mdW5jdGlvbiB1c2VEaXJlY3Rpb24obG9jYWxEaXIpIHtcbiAgY29uc3QgZ2xvYmFsRGlyID0gUmVhY3QudXNlQ29udGV4dChEaXJlY3Rpb25Db250ZXh0KTtcbiAgcmV0dXJuIGxvY2FsRGlyIHx8IGdsb2JhbERpciB8fCBcImx0clwiO1xufVxudmFyIFByb3ZpZGVyID0gRGlyZWN0aW9uUHJvdmlkZXI7XG5leHBvcnQge1xuICBEaXJlY3Rpb25Qcm92aWRlcixcbiAgUHJvdmlkZXIsXG4gIHVzZURpcmVjdGlvblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImpzeCIsIkRpcmVjdGlvbkNvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwiRGlyZWN0aW9uUHJvdmlkZXIiLCJwcm9wcyIsImRpciIsImNoaWxkcmVuIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZURpcmVjdGlvbiIsImxvY2FsRGlyIiwiZ2xvYmFsRGlyIiwidXNlQ29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/focus-guards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n        document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n        document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n        count++;\n        return ()=>{\n            if (count === 1) {\n                document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node)=>node.remove());\n            }\n            count--;\n        };\n    }, []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // src/focus-scope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContainer(node));\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trapped) {\n            let handleFocusIn2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const target = event.target;\n                if (container.contains(target)) {\n                    lastFocusedElementRef.current = target;\n                } else {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleFocusOut2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const relatedTarget = event.relatedTarget;\n                if (relatedTarget === null) return;\n                if (!container.contains(relatedTarget)) {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleMutations2 = function(mutations) {\n                const focusedElement = document.activeElement;\n                if (focusedElement !== document.body) return;\n                for (const mutation of mutations){\n                    if (mutation.removedNodes.length > 0) focus(container);\n                }\n            };\n            var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n            document.addEventListener(\"focusin\", handleFocusIn2);\n            document.addEventListener(\"focusout\", handleFocusOut2);\n            const mutationObserver = new MutationObserver(handleMutations2);\n            if (container) mutationObserver.observe(container, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                document.removeEventListener(\"focusin\", handleFocusIn2);\n                document.removeEventListener(\"focusout\", handleFocusOut2);\n                mutationObserver.disconnect();\n            };\n        }\n    }, [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (container) {\n            focusScopesStack.add(focusScope);\n            const previouslyFocusedElement = document.activeElement;\n            const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n            if (!hasFocusedCandidate) {\n                const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                container.dispatchEvent(mountEvent);\n                if (!mountEvent.defaultPrevented) {\n                    focusFirst(removeLinks(getTabbableCandidates(container)), {\n                        select: true\n                    });\n                    if (document.activeElement === previouslyFocusedElement) {\n                        focus(container);\n                    }\n                }\n            }\n            return ()=>{\n                container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                setTimeout(()=>{\n                    const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    container.dispatchEvent(unmountEvent);\n                    if (!unmountEvent.defaultPrevented) {\n                        focus(previouslyFocusedElement ?? document.body, {\n                            select: true\n                        });\n                    }\n                    container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    focusScopesStack.remove(focusScope);\n                }, 0);\n            };\n        }\n    }, [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (!loop && !trapped) return;\n        if (focusScope.paused) return;\n        const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n        const focusedElement = document.activeElement;\n        if (isTabKey && focusedElement) {\n            const container2 = event.currentTarget;\n            const [first, last] = getTabbableEdges(container2);\n            const hasTabbableElementsInside = first && last;\n            if (!hasTabbableElementsInside) {\n                if (focusedElement === container2) event.preventDefault();\n            } else {\n                if (!event.shiftKey && focusedElement === last) {\n                    event.preventDefault();\n                    if (loop) focus(first, {\n                        select: true\n                    });\n                } else if (event.shiftKey && focusedElement === first) {\n                    event.preventDefault();\n                    if (loop) focus(last, {\n                        select: true\n                    });\n                }\n            }\n        }\n    }, [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useId \".trim().toString()] || (()=>void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n    const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (!deterministicId) setId((reactId)=>reactId ?? String(count++));\n    }, [\n        deterministicId\n    ]);\n    return deterministicId || (id ? `radix-${id}` : \"\");\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSwrQkFBK0I7QUFDQTtBQUNxQztBQUNwRSxJQUFJRSxhQUFhRix5TEFBSyxDQUFDLFVBQVVHLElBQUksR0FBR0MsUUFBUSxHQUFHLElBQUssS0FBTSxLQUFLO0FBQ25FLElBQUlDLFFBQVE7QUFDWixTQUFTQyxNQUFNQyxlQUFlO0lBQzVCLE1BQU0sQ0FBQ0MsSUFBSUMsTUFBTSxHQUFHVCwyQ0FBYyxDQUFDRTtJQUNuQ0Qsa0ZBQWVBLENBQUM7UUFDZCxJQUFJLENBQUNNLGlCQUFpQkUsTUFBTSxDQUFDRSxVQUFZQSxXQUFXQyxPQUFPUDtJQUM3RCxHQUFHO1FBQUNFO0tBQWdCO0lBQ3BCLE9BQU9BLG1CQUFvQkMsQ0FBQUEsS0FBSyxDQUFDLE1BQU0sRUFBRUEsR0FBRyxDQUFDLEdBQUcsRUFBQztBQUNuRDtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va21hLXNjaGVkdWxlLW5nb3Nhbmducy8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtaWQvZGlzdC9pbmRleC5tanM/YzY0OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9pZC9zcmMvaWQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbnZhciB1c2VSZWFjdElkID0gUmVhY3RbXCIgdXNlSWQgXCIudHJpbSgpLnRvU3RyaW5nKCldIHx8ICgoKSA9PiB2b2lkIDApO1xudmFyIGNvdW50ID0gMDtcbmZ1bmN0aW9uIHVzZUlkKGRldGVybWluaXN0aWNJZCkge1xuICBjb25zdCBbaWQsIHNldElkXSA9IFJlYWN0LnVzZVN0YXRlKHVzZVJlYWN0SWQoKSk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFkZXRlcm1pbmlzdGljSWQpIHNldElkKChyZWFjdElkKSA9PiByZWFjdElkID8/IFN0cmluZyhjb3VudCsrKSk7XG4gIH0sIFtkZXRlcm1pbmlzdGljSWRdKTtcbiAgcmV0dXJuIGRldGVybWluaXN0aWNJZCB8fCAoaWQgPyBgcmFkaXgtJHtpZH1gIDogXCJcIik7XG59XG5leHBvcnQge1xuICB1c2VJZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUxheW91dEVmZmVjdCIsInVzZVJlYWN0SWQiLCJ0cmltIiwidG9TdHJpbmciLCJjb3VudCIsInVzZUlkIiwiZGV0ZXJtaW5pc3RpY0lkIiwiaWQiLCJzZXRJZCIsInVzZVN0YXRlIiwicmVhY3RJZCIsIlN0cmluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"select\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node)=>{\n    const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n    const Node = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { asChild, ...primitiveProps } = props;\n        const Comp = asChild ? Slot : node;\n        if (false) {}\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n            ...primitiveProps,\n            ref: forwardedRef\n        });\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n    if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>target.dispatchEvent(event));\n}\nvar Root = Primitive;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // src/select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: SELECT_NAME\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange,\n        caller: SELECT_NAME\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>new Set(prev).add(option));\n                        }, []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>{\n                                const optionsSet = new Set(prev);\n                                optionsSet.delete(option);\n                                return optionsSet;\n                            });\n                        }, []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectBubbleInput, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.value === context.value);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem !== void 0) {\n            context.onValueChange(nextItem.value);\n        }\n    });\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onValueNodeHasChildrenChange(hasChildren);\n    }, [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"▼\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.createSlot)(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_15__.hideOthers)(content);\n    }, [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((candidates)=>{\n        const [firstItem, ...restItems] = getItems().map((item)=>item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates){\n            if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n            candidate?.scrollIntoView({\n                block: \"nearest\"\n            });\n            if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n            if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n            candidate?.focus();\n            if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n    }, [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>focusFirst([\n            selectedItem,\n            content\n        ]), [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isPositioned) {\n            focusSelectedItem();\n        }\n    }, [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) {\n            let pointerMoveDelta = {\n                x: 0,\n                y: 0\n            };\n            const handlePointerMove = (event)=>{\n                pointerMoveDelta = {\n                    x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                    y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                };\n            };\n            const handlePointerUp = (event)=>{\n                if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                    event.preventDefault();\n                } else {\n                    if (!content.contains(event.target)) {\n                        onOpenChange(false);\n                    }\n                }\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                triggerPointerDownPosRef.current = null;\n            };\n            if (triggerPointerDownPosRef.current !== null) {\n                document.addEventListener(\"pointermove\", handlePointerMove);\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true,\n                    once: true\n                });\n            }\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true\n                });\n            };\n        }\n    }, [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const close = ()=>onOpenChange(false);\n        window.addEventListener(\"blur\", close);\n        window.addEventListener(\"resize\", close);\n        return ()=>{\n            window.removeEventListener(\"blur\", close);\n            window.removeEventListener(\"resize\", close);\n        };\n    }, [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.ref.current === document.activeElement);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem) {\n            setTimeout(()=>nextItem.ref.current.focus());\n        }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItem(node);\n            if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n    }, [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>content?.focus(), [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItemText(node);\n        }\n    }, [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            as: Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n            const triggerRect = context.trigger.getBoundingClientRect();\n            const contentRect = content.getBoundingClientRect();\n            const valueNodeRect = context.valueNode.getBoundingClientRect();\n            const itemTextRect = selectedItemText.getBoundingClientRect();\n            if (context.dir !== \"rtl\") {\n                const itemTextOffset = itemTextRect.left - contentRect.left;\n                const left = valueNodeRect.left - itemTextOffset;\n                const leftDelta = triggerRect.left - left;\n                const minContentWidth = triggerRect.width + leftDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                    CONTENT_MARGIN,\n                    // Prevents the content from going off the starting edge of the\n                    // viewport. It may still go off the ending edge, but this can be\n                    // controlled by the user since they may want to manage overflow in a\n                    // specific way.\n                    // https://github.com/radix-ui/primitives/issues/2049\n                    Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.left = clampedLeft + \"px\";\n            } else {\n                const itemTextOffset = contentRect.right - itemTextRect.right;\n                const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                const rightDelta = window.innerWidth - triggerRect.right - right;\n                const minContentWidth = triggerRect.width + rightDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                    CONTENT_MARGIN,\n                    Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.right = clampedRight + \"px\";\n            }\n            const items = getItems();\n            const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n            const itemsHeight = viewport.scrollHeight;\n            const contentStyles = window.getComputedStyle(content);\n            const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n            const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n            const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n            const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n            const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n            const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n            const viewportStyles = window.getComputedStyle(viewport);\n            const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n            const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n            const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n            const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n            const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n            const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n            const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n            const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n            const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n            if (willAlignWithoutTopOverflow) {\n                const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                contentWrapper.style.bottom = \"0px\";\n                const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                contentWrapper.style.height = height + \"px\";\n            } else {\n                const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                contentWrapper.style.top = \"0px\";\n                const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                contentWrapper.style.height = height + \"px\";\n                viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n            }\n            contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n            contentWrapper.style.minHeight = minContentHeight + \"px\";\n            contentWrapper.style.maxHeight = availableHeight + \"px\";\n            onPlaced?.();\n            requestAnimationFrame(()=>shouldExpandOnScrollRef.current = true);\n        }\n    }, [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>position(), [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node && shouldRepositionRef.current === true) {\n            position();\n            focusSelectedItem?.();\n            shouldRepositionRef.current = false;\n        }\n    }, [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>contentContext.itemRefCallback?.(node, value, disabled));\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n            setTextValue((prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setItemTextNode(node), itemContext.onItemTextChange, (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled));\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n            value: itemContext.value,\n            disabled: itemContext.disabled,\n            children: textContent\n        }, itemContext.value), [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onNativeOptionAdd(nativeOption);\n        return ()=>onNativeOptionRemove(nativeOption);\n    }, [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const canScrollUp2 = viewport.scrollTop > 0;\n                setCanScrollUp(canScrollUp2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                setCanScrollDown(canScrollDown2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (autoScrollTimerRef.current !== null) {\n            window.clearInterval(autoScrollTimerRef.current);\n            autoScrollTimerRef.current = null;\n        }\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>clearAutoScrollTimer();\n    }, [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        const activeItem = getItems().find((item)=>item.ref.current === document.activeElement);\n        activeItem?.ref.current?.scrollIntoView({\n            block: \"nearest\"\n        });\n    }, [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSelect, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const select = ref.current;\n        if (!select) return;\n        const selectProto = window.HTMLSelectElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n        const setValue = descriptor.set;\n        if (prevValue !== value && setValue) {\n            const event = new Event(\"change\", {\n                bubbles: true\n            });\n            setValue.call(select, value);\n            select.dispatchEvent(event);\n        }\n    }, [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.select, {\n        ...props,\n        style: {\n            ..._radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        },\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((key)=>{\n        const search = searchRef.current + key;\n        handleSearchChange(search);\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n    }, [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        searchRef.current = \"\";\n        window.clearTimeout(timerRef.current);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const childrenRef = getElementRef(children);\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwyREFBMkQ7QUFDNUI7QUFDL0IsU0FBU0MsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxjQUFjSCx5Q0FBWSxDQUFDRTtJQUNqQ0YsNENBQWUsQ0FBQztRQUNkRyxZQUFZRyxPQUFPLEdBQUdKO0lBQ3hCO0lBQ0EsT0FBT0YsMENBQWEsQ0FBQyxJQUFNLENBQUMsR0FBR1EsT0FBU0wsWUFBWUcsT0FBTyxNQUFNRSxPQUFPLEVBQUU7QUFDNUU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ttYS1zY2hlZHVsZS1uZ29zYW5nbnMvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanM/MTFmMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VDYWxsYmFja1JlZiIsImNhbGxiYWNrIiwiY2FsbGJhY2tSZWYiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJjdXJyZW50IiwidXNlTWVtbyIsImFyZ3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{}, caller }) {\n    const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== void 0;\n    const value = isControlled ? prop : uncontrolledProp;\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n            if (value2 !== prop) {\n                onChangeRef.current?.(value2);\n            }\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        onChangeRef\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    useInsertionEffect(()=>{\n        onChangeRef.current = onChange;\n    }, [\n        onChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            onChangeRef.current?.(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef\n    ]);\n    return [\n        value,\n        setValue,\n        onChangeRef\n    ];\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n    const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n    const isControlled = controlledState !== void 0;\n    const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const args = [\n        {\n            ...initialArg,\n            state: defaultProp\n        }\n    ];\n    if (init) {\n        args.push(init);\n    }\n    const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state2, action)=>{\n        if (action.type === SYNC_STATE) {\n            return {\n                ...state2,\n                state: action.state\n            };\n        }\n        const next = reducer(state2, action);\n        if (isControlled && !Object.is(next.state, state2.state)) {\n            onChange(next.state);\n        }\n        return next;\n    }, ...args);\n    const uncontrolledState = internalState.state;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== uncontrolledState) {\n            prevValueRef.current = uncontrolledState;\n            if (!isControlled) {\n                onChange(uncontrolledState);\n            }\n        }\n    }, [\n        onChange,\n        uncontrolledState,\n        prevValueRef,\n        isControlled\n    ]);\n    const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const isControlled2 = controlledState !== void 0;\n        if (isControlled2) {\n            return {\n                ...internalState,\n                state: controlledState\n            };\n        }\n        return internalState;\n    }, [\n        internalState,\n        controlledState\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isControlled && !Object.is(controlledState, internalState.state)) {\n            dispatch({\n                type: SYNC_STATE,\n                state: controlledState\n            });\n        }\n    }, [\n        controlledState,\n        internalState.state,\n        isControlled\n    ]);\n    return [\n        state,\n        dispatch\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n    if (typeof useReactEffectEvent === \"function\") {\n        return useReactEffectEvent(callback);\n    }\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{\n        throw new Error(\"Cannot call an event handler while rendering.\");\n    });\n    if (typeof useReactInsertionEffect === \"function\") {\n        useReactInsertionEffect(()=>{\n            ref.current = callback;\n        });\n    } else {\n        (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n            ref.current = callback;\n        });\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>ref.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n    const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\") {\n                onEscapeKeyDown(event);\n            }\n        };\n        ownerDocument.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>ownerDocument.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n    }, [\n        onEscapeKeyDown,\n        ownerDocument\n    ]);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBQzlCO0FBQy9CLElBQUlDLG1CQUFtQkMsWUFBWUMsV0FBV0gsa0RBQXFCLEdBQUcsS0FDdEU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ttYS1zY2hlZHVsZS1uZ29zYW5nbnMvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzPzJkNmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlTGF5b3V0RWZmZWN0MiIsImdsb2JhbFRoaXMiLCJkb2N1bWVudCIsInVzZUxheW91dEVmZmVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/use-previous.tsx\n\nfunction usePrevious(value) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        value,\n        previous: value\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        if (ref.current.value !== value) {\n            ref.current.previous = ref.current.value;\n            ref.current.value = value;\n        }\n        return ref.current.previous;\n    }, [\n        value\n    ]);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLG1EQUFtRDtBQUNwQjtBQUMvQixTQUFTQyxZQUFZQyxLQUFLO0lBQ3hCLE1BQU1DLE1BQU1ILHlDQUFZLENBQUM7UUFBRUU7UUFBT0csVUFBVUg7SUFBTTtJQUNsRCxPQUFPRiwwQ0FBYSxDQUFDO1FBQ25CLElBQUlHLElBQUlJLE9BQU8sQ0FBQ0wsS0FBSyxLQUFLQSxPQUFPO1lBQy9CQyxJQUFJSSxPQUFPLENBQUNGLFFBQVEsR0FBR0YsSUFBSUksT0FBTyxDQUFDTCxLQUFLO1lBQ3hDQyxJQUFJSSxPQUFPLENBQUNMLEtBQUssR0FBR0E7UUFDdEI7UUFDQSxPQUFPQyxJQUFJSSxPQUFPLENBQUNGLFFBQVE7SUFDN0IsR0FBRztRQUFDSDtLQUFNO0FBQ1o7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ttYS1zY2hlZHVsZS1uZ29zYW5nbnMvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcz9hMGFmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1wcmV2aW91cy9zcmMvdXNlLXByZXZpb3VzLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VQcmV2aW91cyh2YWx1ZSkge1xuICBjb25zdCByZWYgPSBSZWFjdC51c2VSZWYoeyB2YWx1ZSwgcHJldmlvdXM6IHZhbHVlIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgaWYgKHJlZi5jdXJyZW50LnZhbHVlICE9PSB2YWx1ZSkge1xuICAgICAgcmVmLmN1cnJlbnQucHJldmlvdXMgPSByZWYuY3VycmVudC52YWx1ZTtcbiAgICAgIHJlZi5jdXJyZW50LnZhbHVlID0gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiByZWYuY3VycmVudC5wcmV2aW91cztcbiAgfSwgW3ZhbHVlXSk7XG59XG5leHBvcnQge1xuICB1c2VQcmV2aW91c1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVByZXZpb3VzIiwidmFsdWUiLCJyZWYiLCJ1c2VSZWYiLCJwcmV2aW91cyIsInVzZU1lbW8iLCJjdXJyZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n    const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (element) {\n            setSize({\n                width: element.offsetWidth,\n                height: element.offsetHeight\n            });\n            const resizeObserver = new ResizeObserver((entries)=>{\n                if (!Array.isArray(entries)) {\n                    return;\n                }\n                if (!entries.length) {\n                    return;\n                }\n                const entry = entries[0];\n                let width;\n                let height;\n                if (\"borderBoxSize\" in entry) {\n                    const borderSizeEntry = entry[\"borderBoxSize\"];\n                    const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n                    width = borderSize[\"inlineSize\"];\n                    height = borderSize[\"blockSize\"];\n                } else {\n                    width = element.offsetWidth;\n                    height = element.offsetHeight;\n                }\n                setSize({\n                    width,\n                    height\n                });\n            });\n            resizeObserver.observe(element, {\n                box: \"border-box\"\n            });\n            return ()=>resizeObserver.unobserve(element);\n        } else {\n            setSize(void 0);\n        }\n    }, [\n        element\n    ]);\n    return size;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n    // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n    position: \"absolute\",\n    border: 0,\n    width: 1,\n    height: 1,\n    padding: 0,\n    margin: -1,\n    overflow: \"hidden\",\n    clip: \"rect(0, 0, 0, 0)\",\n    whiteSpace: \"nowrap\",\n    wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span, {\n        ...props,\n        ref: forwardedRef,\n        style: {\n            ...VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        }\n    });\n});\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;