(()=>{var e={};e.id=214,e.ids=[214],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2144:(e,s,n)=>{"use strict";n.r(s),n.d(s,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=n(482),t=n(9108),a=n(2563),i=n.n(a),l=n(8300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);n.d(s,c);let o=["",{children:["(main)",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,5376)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/about/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,4173)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,2555)),"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/about/page.tsx"],m="/(main)/about/page",h={require:n,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/(main)/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2798:(e,s,n)=>{Promise.resolve().then(n.bind(n,9705))},5303:()=>{},9705:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>p});var r=n(2295),t=n(783),a=n.n(t),i=n(2254),l=n(2768),c=n(4513),o=n(8200),d=n(5094),m=n(5008),h=n(1453);let x=[{name:"Changelogs",href:"/changelogs"},{name:"About",href:"/about"}],u=[{name:"KIT Club",href:"https://www.facebook.com/kitclubKMA"},{name:"Issues",href:"https://github.com/ngosangns/kma-schedule-ngosangns/issues"}];function p(){let e=(0,i.usePathname)(),{sidebarOpen:s,toggleSidebar:n}=(0,m.useUI)();return r.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[r.jsx("div",{className:"flex items-center space-x-4",children:r.jsx(a(),{href:"/",className:"text-xl font-bold hover:text-primary transition-colors",children:"ACTVN SCHEDULE"})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[x.map(s=>r.jsx(a(),{href:s.href,className:(0,h.cn)("text-sm font-medium transition-colors hover:text-primary",e===s.href?"text-primary":"text-muted-foreground"),children:s.name},s.name)),r.jsx("div",{className:"h-4 w-px bg-border"}),u.map(e=>(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1",children:[e.name,r.jsx(l.Z,{className:"h-3 w-3"})]},e.name))]}),r.jsx(d.z,{variant:"ghost",size:"sm",className:"md:hidden",onClick:n,children:s?r.jsx(c.Z,{className:"h-5 w-5"}):r.jsx(o.Z,{className:"h-5 w-5"})})]}),s&&r.jsx("div",{className:"md:hidden border-t py-4",children:(0,r.jsxs)("nav",{className:"flex flex-col space-y-3",children:[x.map(s=>r.jsx(a(),{href:s.href,className:(0,h.cn)("text-sm font-medium transition-colors hover:text-primary px-2 py-1",e===s.href?"text-primary":"text-muted-foreground"),onClick:n,children:s.name},s.name)),r.jsx("div",{className:"h-px bg-border my-2"}),u.map(e=>(0,r.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-sm font-medium text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1 px-2 py-1",onClick:n,children:[e.name,r.jsx(l.Z,{className:"h-3 w-3"})]},e.name))]})})]})})}},5376:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>x});var r=n(5036),t=n(4551),a=n(1842),i=n(3377),l=n(9508);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),o=(0,l.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),d=(0,l.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),m=(0,l.Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),h=(0,l.Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);function x(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Về ACTVN Schedule"}),r.jsx("p",{className:"text-muted-foreground",children:"Ứng dụng xem thời kh\xf3a biểu d\xe0nh cho sinh vi\xean Học viện Kỹ thuật Mật m\xe3"})]}),(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,r.jsxs)(t.Zb,{children:[r.jsx(t.Ol,{children:(0,r.jsxs)(t.ll,{className:"flex items-center gap-2",children:[r.jsx(c,{className:"h-5 w-5"}),"Về dự \xe1n"]})}),(0,r.jsxs)(t.aY,{className:"space-y-4",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"ACTVN Schedule l\xe0 một ứng dụng web hiện đại được x\xe2y dựng để gi\xfap sinh vi\xean Học viện Kỹ thuật Mật m\xe3 xem thời kh\xf3a biểu một c\xe1ch dễ d\xe0ng v\xe0 tiện lợi."}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium",children:"T\xednh năng ch\xednh:"}),(0,r.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1 ml-4",children:[r.jsx("li",{children:"• Xem thời kh\xf3a biểu theo tuần"}),r.jsx("li",{children:"• Giao diện responsive, th\xe2n thiện với mobile"}),r.jsx("li",{children:"• Xuất lịch sang Google Calendar"}),r.jsx("li",{children:"• Lọc theo buổi học (s\xe1ng, chiều, tối)"}),r.jsx("li",{children:"• Chế độ xem lịch v\xe0 danh s\xe1ch"}),r.jsx("li",{children:"• Dark mode"})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[r.jsx(a.C,{variant:"secondary",children:"Next.js"}),r.jsx(a.C,{variant:"secondary",children:"TypeScript"}),r.jsx(a.C,{variant:"secondary",children:"Tailwind CSS"}),r.jsx(a.C,{variant:"secondary",children:"shadcn/ui"})]})]})]}),(0,r.jsxs)(t.Zb,{children:[r.jsx(t.Ol,{children:(0,r.jsxs)(t.ll,{className:"flex items-center gap-2",children:[r.jsx(o,{className:"h-5 w-5"}),"Đội ngũ ph\xe1t triển"]})}),(0,r.jsxs)(t.aY,{className:"space-y-4",children:[r.jsx("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center",children:r.jsx(c,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"ngosangns"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Lead Developer"})]})]})}),r.jsx(i.Z,{}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium",children:"Li\xean hệ:"}),r.jsx("div",{className:"space-y-2",children:(0,r.jsxs)("a",{href:"https://github.com/ngosangns",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[r.jsx(d,{className:"h-4 w-4"}),"GitHub",r.jsx(m,{className:"h-3 w-3"})]})})]})]})]}),(0,r.jsxs)(t.Zb,{className:"md:col-span-2",children:[(0,r.jsxs)(t.Ol,{children:[(0,r.jsxs)(t.ll,{className:"flex items-center gap-2",children:[r.jsx(h,{className:"h-5 w-5"}),"C\xe1c dự \xe1n li\xean quan"]}),r.jsx(t.SZ,{children:"Những ứng dụng kh\xe1c m\xe0 ch\xfang t\xf4i đ\xe3 ph\xe1t triển cho sinh vi\xean KMA"})]}),r.jsx(t.aY,{children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[r.jsx("h4",{className:"font-medium",children:"KIT Schedule"}),r.jsx(a.C,{variant:"outline",children:"Mobile App"})]}),r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Ứng dụng xem lịch học tr\xean điện thoại d\xe0nh cho sinh vi\xean học viện KMA."}),(0,r.jsxs)("a",{href:"https://play.google.com/store/apps/details?id=kma.hatuan314.schedule",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-1 text-sm text-primary hover:underline",children:["Xem tr\xean Google Play",r.jsx(m,{className:"h-3 w-3"})]})]}),(0,r.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[r.jsx("h4",{className:"font-medium",children:"KMA T\xedn chỉ"}),r.jsx(a.C,{variant:"outline",children:"Web Tool"})]}),r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Tool hỗ trợ sinh vi\xean sắp xếp lịch học hợp l\xed cho bản th\xe2n v\xe0o mỗi m\xf9a đăng k\xfd học."}),(0,r.jsxs)("a",{href:"https://github.com/ngosangns/tin-chi",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-1 text-sm text-primary hover:underline",children:["Xem tr\xean GitHub",r.jsx(m,{className:"h-3 w-3"})]})]})]})})]})]})]})}},4173:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>o});var r=n(5036);let t=(0,n(6843).createProxy)(String.raw`/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Header.tsx`),{__esModule:a,$$typeof:i}=t,l=t.default;function c(){return r.jsx("footer",{className:"border-t bg-background",children:r.jsx("div",{className:"container mx-auto px-4 py-6",children:(0,r.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[r.jsx("p",{children:"KMA Schedule v2022.12 - ngosangns"}),r.jsx("p",{className:"mt-1",children:"Built with Next.js, TypeScript, and shadcn/ui"})]})})})}function o({children:e}){return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen",children:[r.jsx(l,{}),r.jsx("main",{className:"flex-1 container mx-auto px-4 py-6 max-w-7xl",tabIndex:-1,role:"main","aria-label":"Main content",children:e}),r.jsx(c,{})]})}},1842:(e,s,n)=>{"use strict";n.d(s,{C:()=>l});var r=n(5036);n(2);var t=n(4467),a=n(1171);let i=(0,t.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...n}){return r.jsx("div",{className:(0,a.cn)(i({variant:s}),e),...n})}},4551:(e,s,n)=>{"use strict";n.d(s,{Ol:()=>l,SZ:()=>o,Zb:()=>i,aY:()=>d,ll:()=>c});var r=n(5036),t=n(2),a=n(1171);let i=t.forwardRef(({className:e,...s},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let l=t.forwardRef(({className:e,...s},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let c=t.forwardRef(({className:e,...s},n)=>r.jsx("h3",{ref:n,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let o=t.forwardRef(({className:e,...s},n)=>r.jsx("p",{ref:n,className:(0,a.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let d=t.forwardRef(({className:e,...s},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("p-6 pt-0",e),...s}));d.displayName="CardContent",t.forwardRef(({className:e,...s},n)=>r.jsx("div",{ref:n,className:(0,a.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},3377:(e,s,n)=>{"use strict";n.d(s,{Z:()=>l});var r=n(5036),t=n(2),a=n(5367),i=n(1171);let l=t.forwardRef(({className:e,orientation:s="horizontal",decorative:n=!0,...t},l)=>r.jsx(a.f,{ref:l,decorative:n,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...t}));l.displayName=a.f.displayName},1171:(e,s,n)=>{"use strict";n.d(s,{cn:()=>a});var r=n(990),t=n(1774);function a(...e){return(0,t.m6)((0,r.W)(e))}n(5603)}};var s=require("../../../webpack-runtime.js");s.C(e);var n=e=>s(s.s=e),r=s.X(0,[21,795,498,584],()=>n(2144));module.exports=r})();