{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/AboutPage", "regex": "^/AboutPage(?:/)?$", "routeKeys": {}, "namedRegex": "^/AboutPage(?:/)?$"}, {"page": "/CalendarPage", "regex": "^/CalendarPage(?:/)?$", "routeKeys": {}, "namedRegex": "^/CalendarPage(?:/)?$"}, {"page": "/ChangelogsPage", "regex": "^/ChangelogsPage(?:/)?$", "routeKeys": {}, "namedRegex": "^/ChangelogsPage(?:/)?$"}, {"page": "/LoginPage", "regex": "^/LoginPage(?:/)?$", "routeKeys": {}, "namedRegex": "^/LoginPage(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/calendar", "regex": "^/calendar(?:/)?$", "routeKeys": {}, "namedRegex": "^/calendar(?:/)?$"}, {"page": "/changelogs", "regex": "^/changelogs(?:/)?$", "routeKeys": {}, "namedRegex": "^/changelogs(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}