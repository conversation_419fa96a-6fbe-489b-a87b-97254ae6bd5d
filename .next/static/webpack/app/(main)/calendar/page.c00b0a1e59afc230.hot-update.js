"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/calendar/page",{

/***/ "(app-pages-browser)/./src/lib/ts/user.ts":
/*!****************************!*\
  !*** ./src/lib/ts/user.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   login: function() { return /* binding */ login; },\n/* harmony export */   logout: function() { return /* binding */ logout; }\n/* harmony export */ });\n/* harmony import */ var _calendar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./calendar */ \"(app-pages-browser)/./src/lib/ts/calendar.ts\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./storage */ \"(app-pages-browser)/./src/lib/ts/storage.ts\");\n/* harmony import */ var md5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! md5 */ \"(app-pages-browser)/./node_modules/md5/md5.js\");\n/* harmony import */ var md5__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(md5__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function login(username, password) {\n    let result = await fetch(\"https://actvn-schedule.cors-ngosangns.workers.dev/login\", {\n        method: \"GET\"\n    });\n    const resultText = await result.text();\n    const viewState = (0,_calendar__WEBPACK_IMPORTED_MODULE_0__.getFieldFromResult)(resultText, \"__VIEWSTATE\");\n    const eventValidation = (0,_calendar__WEBPACK_IMPORTED_MODULE_0__.getFieldFromResult)(resultText, \"__EVENTVALIDATION\");\n    const data = {\n        __VIEWSTATE: viewState,\n        __EVENTVALIDATION: eventValidation,\n        txtUserName: username.toUpperCase(),\n        txtPassword: md5__WEBPACK_IMPORTED_MODULE_2___default()(password),\n        btnSubmit: \"Đăng nhập\"\n    };\n    result = await fetch(\"https://actvn-schedule.cors-ngosangns.workers.dev/login\", {\n        method: \"POST\",\n        body: Object.keys(data).map((key)=>encodeURIComponent(key) + \"=\" + encodeURIComponent(key in data ? data[key] : \"\")).join(\"&\"),\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        }\n    });\n    // Get cookies from response headers\n    const setCookieHeader = result.headers.get(\"set-cookie\") || result.headers.get(\"Set-Cookie\");\n    if (setCookieHeader) {\n        return setCookieHeader;\n    }\n    // If no cookies in headers, try to extract from response text\n    const responseText = await result.text();\n    // The response text appears to be the cookie value directly\n    // Format it as a proper cookie string\n    if (responseText && responseText.startsWith(\"SignIn=\")) {\n        return responseText;\n    }\n    return responseText;\n}\nfunction logout() {\n    (0,_storage__WEBPACK_IMPORTED_MODULE_1__.clearData)();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ts/user.ts\n"));

/***/ })

});