"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/calendar/page",{

/***/ "(app-pages-browser)/./src/lib/ts/calendar.ts":
/*!********************************!*\
  !*** ./src/lib/ts/calendar.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanFromHTMLtoArray: function() { return /* binding */ cleanFromHTMLtoArray; },\n/* harmony export */   exportToGoogleCalendar: function() { return /* binding */ exportToGoogleCalendar; },\n/* harmony export */   fetchCalendarWithGet: function() { return /* binding */ fetchCalendarWithGet; },\n/* harmony export */   fetchCalendarWithPost: function() { return /* binding */ fetchCalendarWithPost; },\n/* harmony export */   filterTrashInHtml: function() { return /* binding */ filterTrashInHtml; },\n/* harmony export */   getFieldFromResult: function() { return /* binding */ getFieldFromResult; },\n/* harmony export */   processCalendar: function() { return /* binding */ processCalendar; },\n/* harmony export */   processMainForm: function() { return /* binding */ processMainForm; },\n/* harmony export */   processSemesters: function() { return /* binding */ processSemesters; },\n/* harmony export */   processStudent: function() { return /* binding */ processStudent; },\n/* harmony export */   restructureTKB: function() { return /* binding */ restructureTKB; }\n/* harmony export */ });\n/* harmony import */ var _worker__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./worker */ \"(app-pages-browser)/./src/lib/ts/worker.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function fetchCalendarWithPost(formObj, signInToken) {\n    const response = await fetch(\"https://actvn-schedule.cors-ngosangns.workers.dev/subject\", {\n        method: \"POST\",\n        body: Object.keys(formObj).map((key)=>{\n            return encodeURIComponent(key) + \"=\" + encodeURIComponent(formObj[key]);\n        }).join(\"&\"),\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"x-cors-headers\": JSON.stringify({\n                Cookie: signInToken\n            })\n        }\n    });\n    return await response.text();\n}\nasync function fetchCalendarWithGet(signInToken) {\n    const response = await fetch(\"https://actvn-schedule.cors-ngosangns.workers.dev/subject\", {\n        method: \"GET\",\n        headers: {\n            \"x-cors-headers\": JSON.stringify({\n                Cookie: signInToken\n            })\n        }\n    });\n    console.log(\"Calendar response status:\", response.status);\n    console.log(\"Calendar response headers:\", Object.fromEntries(response.headers.entries()));\n    return await response.text();\n}\nfunction getFieldFromResult(result, field) {\n    let res = result.match(new RegExp('id=\"' + field + '\" value=\"(.+?)\"', \"g\"));\n    if (!res || !res.length) return false;\n    res = res[0];\n    res = res.match(/value=\"(.+?)\"/);\n    if (!res || !res.length) return false;\n    res = res[1];\n    return res;\n}\nfunction stripHTMLTags(str) {\n    if (str === null || str === false) return \"\";\n    else str = str.toString();\n    return str.replace(/<[^>]*>/g, \"\");\n}\nfunction filterTrashInHtml(html) {\n    let result = html;\n    result = result.replace(/src=\"(.+?)\"/g, \"\");\n    return result;\n}\nfunction cleanFromHTMLtoArray(raw_tkb) {\n    if (!raw_tkb || !raw_tkb.length) return false;\n    // remove trash and catch table from html string\n    raw_tkb = raw_tkb.replace(/ {2,}/gm, \" \");\n    raw_tkb = raw_tkb.replace(/<!--.*?-->|\\t|(?:\\r?\\n[ \\t]*)+/gm, \"\");\n    const raw_tkb_matched = raw_tkb.match(/<table.+?gridRegistered.+?<\\/table>/g);\n    if (raw_tkb_matched && raw_tkb_matched.length) raw_tkb = raw_tkb_matched[0];\n    // convert response to DOM then export the table to array\n    if (typeof document === \"undefined\") {\n        throw new Error(\"DOM operations not available on server side\");\n    }\n    const tempDiv = document.createElement(\"div\");\n    tempDiv.id = \"cleanTKB\";\n    tempDiv.style.display = \"none\";\n    tempDiv.innerHTML = raw_tkb;\n    document.body.appendChild(tempDiv);\n    const data_content_temp = Array.prototype.map.call(tempDiv.querySelectorAll(\"#gridRegistered tr\"), (tr)=>Array.prototype.map.call(tr.querySelectorAll(\"td\"), (td)=>stripHTMLTags(td.innerHTML)));\n    document.body.removeChild(tempDiv);\n    // check null\n    if (!data_content_temp) return false;\n    return data_content_temp;\n}\nfunction processStudent(rawHtml) {\n    let student = rawHtml.match(/<span id=\"lblStudent\">(.+?)<\\/span/g);\n    if (student && student.length) {\n        student = student[0].match(/<span id=\"lblStudent\">(.+?)<\\/span/);\n        if (student && student.length > 1) return student[1];\n    }\n    return \"KIT Club\";\n}\nasync function processCalendar(rawHtml) {\n    if (!rawHtml) throw new Error(\"empty data\");\n    return await new Promise((resolve, reject)=>{\n        const worker = (0,_worker__WEBPACK_IMPORTED_MODULE_0__.createInlineWorker)((_rawHtml)=>self.postMessage(restructureTKB(_rawHtml.data)), restructureTKB);\n        worker.onmessage = (res)=>resolve(res.data ? res.data : res.data === false ? {\n                data_subject: []\n            } : null);\n        worker.onerror = (err)=>reject(err);\n        worker.postMessage(cleanFromHTMLtoArray(rawHtml));\n    }).catch((e)=>{\n        throw e;\n    });\n}\nfunction processMainForm(rawHtml) {\n    // parse html\n    if (typeof DOMParser === \"undefined\") {\n        throw new Error(\"DOMParser not available on server side\");\n    }\n    const parser = new DOMParser();\n    const dom = parser.parseFromString(rawHtml, \"text/html\");\n    const form1 = dom.getElementById(\"Form1\");\n    if (!form1) return {};\n    const formData = {};\n    const inputs = form1.querySelectorAll(\"input, select, textarea\");\n    inputs.forEach((input)=>{\n        if (input.name && input.value) {\n            formData[input.name] = input.value;\n        }\n    });\n    return formData;\n}\n/**\n * Get semesters from mainForm\n * @param {string} response\n * @return {{\n * \tsemesters: Array<{value: string, from: string, to: string, th: string}>\n * \tcurrentSemester: string\n * } | null} response\n */ function processSemesters(response) {\n    if (typeof DOMParser === \"undefined\") {\n        throw new Error(\"DOMParser not available on server side\");\n    }\n    const parser = new DOMParser();\n    const dom = parser.parseFromString(response, \"text/html\");\n    const semesterSelect = dom.querySelector(\"select[name=drpSemester]\");\n    if (!semesterSelect) return null;\n    const options = semesterSelect.querySelectorAll(\"option\");\n    const semesters = [];\n    let currentSemester = \"\";\n    for(let i = 0; i < options.length; i++){\n        const option = options[i];\n        const tmp = option.innerHTML.split(\"_\");\n        semesters.push({\n            value: option.value,\n            from: tmp[1],\n            to: tmp[2],\n            th: tmp[0]\n        });\n        if (option.selected) {\n            currentSemester = option.value;\n        }\n    }\n    return {\n        semesters: semesters,\n        currentSemester: currentSemester\n    };\n}\nfunction restructureTKB(data) {\n    const categories = {\n        lop_hoc_phan: \"Lớp học phần\",\n        hoc_phan: \"Học phần\",\n        thoi_gian: \"Thời gian\",\n        dia_diem: \"\\xd0ịa điểm\",\n        giang_vien: \"Giảng vi\\xean\",\n        si_so: \"Sĩ số\",\n        so_dk: \"Số \\xd0K\",\n        so_tc: \"Số TC\",\n        ghi_chu: \"Ghi ch\\xfa\"\n    };\n    // check null\n    if (data.length == 0 || data == false) return false;\n    // remove price\n    data.pop();\n    // if after remove price just only have header titles then return\n    if (data.length == 1) return false;\n    // create var\n    const header_data = data[0];\n    const content_data = data.slice(1, data.length);\n    let min_time, max_time;\n    const data_subject = Array.prototype.map.call(content_data, function(td) {\n        const regex_time_spliter = \"([0-9]{2}\\\\/[0-9]{2}\\\\/[0-9]{4}).+?([0-9]{2}\\\\/[0-9]{2}\\\\/[0-9]{4}):(\\\\([0-9]*\\\\))?(.+?)((Từ)|$)+?\";\n        const regex_time_spliter_multi = new RegExp(regex_time_spliter, \"g\");\n        const regex_time_spliter_line = new RegExp(regex_time_spliter);\n        let temp_dia_diem = td[header_data.indexOf(categories.dia_diem)];\n        const temp_dia_diem_season_index = temp_dia_diem.match(/\\([0-9,]+?\\)/g);\n        // return null (not remove) if not match the pattern (to sync with season time)\n        if (!temp_dia_diem_season_index) temp_dia_diem = null;\n        if (temp_dia_diem) {\n            // add \\n before each season\n            temp_dia_diem_season_index.forEach((child_item)=>temp_dia_diem = temp_dia_diem.replace(child_item, \"\\n\" + child_item));\n            // split season\n            temp_dia_diem = temp_dia_diem.match(/\\n\\(([0-9,]+?)\\)(.+)/g);\n            temp_dia_diem = Array.prototype.map.call(temp_dia_diem, (item)=>{\n                let temp = item.match(/\\n\\(([0-9,]+?)\\)(.+)/);\n                temp = [\n                    temp[1].split(\",\"),\n                    temp[2]\n                ];\n                // merge splited season to address\n                const temp2 = Array.prototype.map.call(temp[0], (child_item)=>\"(\".concat(child_item, \") \").concat(temp[1]));\n                return temp2;\n            }).flat();\n            temp_dia_diem.sort(function(a, b) {\n                return parseInt(a[1]) - parseInt(b[1]);\n            });\n            // remove season index in string\n            temp_dia_diem = Array.prototype.map.call(temp_dia_diem, (item)=>item.replace(/^\\([0-9]+?\\) /i, \"\").trim());\n        }\n        // ---------------------------------\n        const temp_thoi_gian = td[header_data.indexOf(categories.thoi_gian)].match(regex_time_spliter_multi);\n        // throw Error if subject hasn't had class times\n        if (!temp_thoi_gian) return false;\n        temp_thoi_gian.forEach((item, index)=>{\n            item = item.match(regex_time_spliter_line);\n            // remove if not match the pattern\n            if (!item) {\n                temp_thoi_gian.splice(index, 1);\n                return;\n            }\n            item[4] = item[4].split(\"&nbsp;&nbsp;&nbsp;\");\n            item[4].shift(); // remove trash\n            item[4].forEach((child_item, child_index)=>{\n                // split day of week part\n                child_item = child_item.match(/((Thứ .+?)||Chủ nhật) tiết (.+?)$/);\n                // remove if not match the pattern\n                if (!child_item) {\n                    item[4].splice(child_index, 1);\n                    return;\n                }\n                // remove trash\n                const dayOfWeek_number = {\n                    \"Thứ 2\": 2,\n                    \"Thứ 3\": 3,\n                    \"Thứ 4\": 4,\n                    \"Thứ 5\": 5,\n                    \"Thứ 6\": 6,\n                    \"Thứ 7\": 7,\n                    \"Chủ nhật\": 8\n                };\n                if (child_item) {\n                    child_item[3] = child_item[3].split(/[^0-9]+/g);\n                    child_item[3].pop();\n                    child_item = {\n                        dow: dayOfWeek_number[child_item[1]],\n                        shi: child_item[3]\n                    };\n                }\n                // save element\n                item[4][child_index] = child_item;\n            });\n            // remove trash\n            item[1] = \"\".concat(item[1].substr(3, 2), \"/\").concat(item[1].substr(0, 2), \"/\").concat(item[1].substr(6, 4));\n            item[2] = \"\".concat(item[2].substr(3, 2), \"/\").concat(item[2].substr(0, 2), \"/\").concat(item[2].substr(6, 4));\n            item[1] = new Date(Date.parse(item[1]));\n            item[2] = new Date(Date.parse(item[2]));\n            item = {\n                startTime: item[1],\n                endTime: item[2],\n                dayOfWeek: item[4],\n                address: temp_dia_diem ? temp_dia_diem[index] : null\n            };\n            // save min/max time\n            if (min_time) {\n                if (min_time > item.startTime) min_time = item.startTime;\n            } else min_time = item.startTime;\n            if (max_time) {\n                if (max_time < item.endTime) max_time = item.endTime;\n            } else max_time = item.endTime;\n            // save element\n            temp_thoi_gian[index] = item;\n        });\n        // ---------------------------------\n        return {\n            lop_hoc_phan: td[header_data.indexOf(categories.lop_hoc_phan)],\n            hoc_phan: td[header_data.indexOf(categories.hoc_phan)],\n            giang_vien: td[header_data.indexOf(categories.giang_vien)],\n            si_so: td[header_data.indexOf(categories.si_so)],\n            so_dk: td[header_data.indexOf(categories.so_dk)],\n            so_tc: td[header_data.indexOf(categories.so_tc)],\n            tkb: temp_thoi_gian\n        };\n    });\n    min_time = min_time.getTime();\n    max_time = max_time.getTime();\n    const days_outline = [];\n    const one_day_time = 86400000;\n    for(let time_iter = min_time; time_iter <= max_time; time_iter += one_day_time){\n        if (new Date(time_iter).getDay() + 1 == 2 || time_iter == min_time) {\n            days_outline.push([\n                {\n                    time: time_iter,\n                    shift: []\n                }\n            ]);\n            continue;\n        }\n        days_outline[days_outline.length - 1].push({\n            time: time_iter,\n            shift: []\n        });\n    }\n    for (const week of days_outline){\n        for (const day of week){\n            day.shift = Array.from({\n                length: 16\n            }, (_, shift)=>{\n                for (const subject of data_subject){\n                    if (subject) {\n                        for (const season of subject.tkb)if (day.time >= season.startTime.getTime() && day.time <= season.endTime.getTime()) for (const sub_day of season.dayOfWeek){\n                            if (sub_day.dow == new Date(day.time).getDay() + 1 || new Date(day.time).getDay() + 1 == 1 && sub_day.dow == 8 // Chu nhat\n                            ) {\n                                if (shift + 1 >= parseInt(sub_day.shi[0]) && shift + 1 <= parseInt(sub_day.shi[sub_day.shi.length - 1])) if (shift + 1 === parseInt(sub_day.shi[0])) {\n                                    return {\n                                        content: \"\".concat(subject.lop_hoc_phan).concat(season.address ? \" (học tại \".concat(season.address, \")\") : \"\"),\n                                        name: subject.lop_hoc_phan,\n                                        address: season.address ? season.address : null,\n                                        length: sub_day.shi.length\n                                    };\n                                } else return {\n                                    content: null,\n                                    name: null,\n                                    address: null,\n                                    length: 0\n                                };\n                            }\n                        }\n                    }\n                }\n                return {\n                    content: null,\n                    name: null,\n                    address: null,\n                    length: 1\n                };\n            });\n        }\n    }\n    return {\n        data_subject: days_outline\n    };\n}\nfunction exportToGoogleCalendar(student, calendar) {\n    if (!calendar || !calendar.data_subject || !Array.isArray(calendar.data_subject)) {\n        console.error(\"Invalid calendar data for export\");\n        return;\n    }\n    const time_sift_table = [\n        {},\n        {\n            start: \"000000\",\n            end: \"004500\"\n        },\n        {\n            start: \"005000\",\n            end: \"013500\"\n        },\n        {\n            start: \"014000\",\n            end: \"022500\"\n        },\n        {\n            start: \"023500\",\n            end: \"032000\"\n        },\n        {\n            start: \"032500\",\n            end: \"041000\"\n        },\n        {\n            start: \"041500\",\n            end: \"050000\"\n        },\n        {\n            start: \"053000\",\n            end: \"061500\"\n        },\n        {\n            start: \"062000\",\n            end: \"070500\"\n        },\n        {\n            start: \"071000\",\n            end: \"075500\"\n        },\n        {\n            start: \"080500\",\n            end: \"085000\"\n        },\n        {\n            start: \"085500\",\n            end: \"094000\"\n        },\n        {\n            start: \"094500\",\n            end: \"103000\"\n        },\n        {\n            start: \"110000\",\n            end: \"114500\"\n        },\n        {\n            start: \"114500\",\n            end: \"123000\"\n        },\n        {\n            start: \"124500\",\n            end: \"133000\"\n        },\n        {\n            start: \"133000\",\n            end: \"141500\"\n        }\n    ];\n    let result = \"BEGIN:VCALENDAR\\nCALSCALE:GREGORIAN\\nMETHOD:PUBLISH\\n\\n\";\n    calendar.data_subject.forEach((week)=>{\n        for (const day of week){\n            const timeIter = new Date(day.time);\n            if (day.shift && Array.isArray(day.shift)) {\n                day.shift.forEach((shift, shift_index)=>{\n                    if (shift.content) {\n                        const startIndex = shift_index + 1;\n                        const endIndex = shift_index + (parseInt(shift.length) || 1);\n                        // Ensure indices are within bounds\n                        if (startIndex < time_sift_table.length && endIndex < time_sift_table.length) {\n                            var _time_sift_table_startIndex, _time_sift_table_endIndex;\n                            const startTime = (_time_sift_table_startIndex = time_sift_table[startIndex]) === null || _time_sift_table_startIndex === void 0 ? void 0 : _time_sift_table_startIndex.start;\n                            const endTime = (_time_sift_table_endIndex = time_sift_table[endIndex]) === null || _time_sift_table_endIndex === void 0 ? void 0 : _time_sift_table_endIndex.end;\n                            if (startTime && endTime) {\n                                result += \"BEGIN:VEVENT\\nDTSTART:\".concat(moment__WEBPACK_IMPORTED_MODULE_1___default()(timeIter).format(\"YYYYMMDD\"), \"T\").concat(startTime, \"Z\\n\");\n                                result += \"DTEND:\".concat(moment__WEBPACK_IMPORTED_MODULE_1___default()(timeIter).format(\"YYYYMMDD\"), \"T\").concat(endTime, \"Z\\n\");\n                                if (shift.address) result += \"LOCATION:\".concat(shift.address, \"\\n\");\n                                result += \"SUMMARY:\".concat(shift.name, \"\\n\");\n                                result += \"END:VEVENT\\n\\n\";\n                            }\n                        }\n                    }\n                });\n            }\n        }\n    });\n    result += \"END:VCALENDAR\";\n    const link = document.createElement(\"a\");\n    link.setAttribute(\"href\", \"data:text/plain;charset=utf-8,\" + encodeURIComponent(result));\n    link.setAttribute(\"download\", \"\".concat(student ? student.split(\" - \")[0] : \"tkb_export\", \".ics\"));\n    link.style.display = \"none\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/ts/calendar.ts\n"));

/***/ })

});