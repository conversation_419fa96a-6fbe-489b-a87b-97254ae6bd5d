"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{3742:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},8971:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},2674:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},4998:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4979:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3025:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},4813:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},8051:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},8957:function(e,t,n){let r;n.d(t,{VY:function(){return rh},ZA:function(){return rv},JO:function(){return rd},ck:function(){return ry},wU:function(){return rx},eT:function(){return rw},__:function(){return rg},h_:function(){return rp},fC:function(){return rc},$G:function(){return rE},u_:function(){return rb},Z0:function(){return rS},xz:function(){return rs},B4:function(){return rf},l_:function(){return rm}});var o,i,l,a,u,c,s,f=n(7294),d=n.t(f,2),p=n(3935);function h(e,[t,n]){return Math.min(n,Math.max(t,e))}function m(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var v=n(5893);function g(e,t=[]){let n=[],r=()=>{let t=n.map(e=>f.createContext(e));return function(n){let r=n?.[e]||t;return f.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=f.createContext(r),i=n.length;n=[...n,r];let l=t=>{let{scope:n,children:r,...l}=t,a=n?.[e]?.[i]||o,u=f.useMemo(()=>l,Object.values(l));return(0,v.jsx)(a.Provider,{value:u,children:r})};return l.displayName=t+"Provider",[l,function(n,l){let a=l?.[e]?.[i]||o,u=f.useContext(a);if(u)return u;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var y=n(8771),w=n(8426),x=f.createContext(void 0),b=n(5320);function E(e){let t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...e)=>t.current?.(...e),[])}var S="dismissableLayer.update",C=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),R=f.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:l,onDismiss:a,...u}=e,s=f.useContext(C),[d,p]=f.useState(null),h=d?.ownerDocument??globalThis?.document,[,g]=f.useState({}),w=(0,y.e)(t,e=>p(e)),x=Array.from(s.layers),[R]=[...s.layersWithOutsidePointerEventsDisabled].slice(-1),A=x.indexOf(R),L=d?x.indexOf(d):-1,P=s.layersWithOutsidePointerEventsDisabled.size>0,M=L>=A,N=function(e,t=globalThis?.document){let n=E(e),r=f.useRef(!1),o=f.useRef(()=>{});return f.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){k("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...s.branches].some(e=>e.contains(t));!M||n||(o?.(e),l?.(e),e.defaultPrevented||a?.())},h),j=function(e,t=globalThis?.document){let n=E(e),r=f.useRef(!1);return f.useEffect(()=>{let e=e=>{e.target&&!r.current&&k("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...s.branches].some(e=>e.contains(t))||(i?.(e),l?.(e),e.defaultPrevented||a?.())},h);return!function(e,t=globalThis?.document){let n=E(e);f.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L!==s.layers.size-1||(r?.(e),!e.defaultPrevented&&a&&(e.preventDefault(),a()))},h),f.useEffect(()=>{if(d)return n&&(0===s.layersWithOutsidePointerEventsDisabled.size&&(c=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),s.layersWithOutsidePointerEventsDisabled.add(d)),s.layers.add(d),T(),()=>{n&&1===s.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=c)}},[d,h,n,s]),f.useEffect(()=>()=>{d&&(s.layers.delete(d),s.layersWithOutsidePointerEventsDisabled.delete(d),T())},[d,s]),f.useEffect(()=>{let e=()=>g({});return document.addEventListener(S,e),()=>document.removeEventListener(S,e)},[]),(0,v.jsx)(b.WV.div,{...u,ref:w,style:{pointerEvents:P?M?"auto":"none":void 0,...e.style},onFocusCapture:m(e.onFocusCapture,j.onFocusCapture),onBlurCapture:m(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:m(e.onPointerDownCapture,N.onPointerDownCapture)})});function T(){let e=new CustomEvent(S);document.dispatchEvent(e)}function k(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,b.jH)(o,i):o.dispatchEvent(i)}R.displayName="DismissableLayer",f.forwardRef((e,t)=>{let n=f.useContext(C),r=f.useRef(null),o=(0,y.e)(t,r);return f.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(b.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var A=0;function L(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var P="focusScope.autoFocusOnMount",M="focusScope.autoFocusOnUnmount",N={bubbles:!1,cancelable:!0},j=f.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[a,u]=f.useState(null),c=E(o),s=E(i),d=f.useRef(null),p=(0,y.e)(t,e=>u(e)),h=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let e=function(e){if(h.paused||!a)return;let t=e.target;a.contains(t)?d.current=t:W(d.current,{select:!0})},t=function(e){if(h.paused||!a)return;let t=e.relatedTarget;null===t||a.contains(t)||W(d.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&W(a)});return a&&n.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,a,h.paused]),f.useEffect(()=>{if(a){I.add(h);let e=document.activeElement;if(!a.contains(e)){let t=new CustomEvent(P,N);a.addEventListener(P,c),a.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(W(r,{select:t}),document.activeElement!==n)return}(D(a).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&W(a))}return()=>{a.removeEventListener(P,c),setTimeout(()=>{let t=new CustomEvent(M,N);a.addEventListener(M,s),a.dispatchEvent(t),t.defaultPrevented||W(e??document.body,{select:!0}),a.removeEventListener(M,s),I.remove(h)},0)}}},[a,c,s,h]);let m=f.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=D(e);return[O(t,e),O(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&W(i,{select:!0})):(e.preventDefault(),n&&W(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,v.jsx)(b.WV.div,{tabIndex:-1,...l,ref:p,onKeyDown:m})});function D(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function O(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function W(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}j.displayName="FocusScope";var I=(r=[],{add(e){let t=r[0];e!==t&&t?.pause(),(r=H(r,e)).unshift(e)},remove(e){r=H(r,e),r[0]?.resume()}});function H(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var V=globalThis?.document?f.useLayoutEffect:()=>{},F=d[" useId ".trim().toString()]||(()=>void 0),B=0;function _(e){let[t,n]=f.useState(F());return V(()=>{e||n(e=>e??String(B++))},[e]),e||(t?`radix-${t}`:"")}let z=["top","right","bottom","left"],Z=Math.min,$=Math.max,K=Math.round,Y=Math.floor,U=e=>({x:e,y:e}),X={left:"right",right:"left",bottom:"top",top:"bottom"},q={start:"end",end:"start"};function G(e,t){return"function"==typeof e?e(t):e}function J(e){return e.split("-")[0]}function Q(e){return e.split("-")[1]}function ee(e){return"x"===e?"y":"x"}function et(e){return"y"===e?"height":"width"}let en=new Set(["top","bottom"]);function er(e){return en.has(J(e))?"y":"x"}function eo(e){return e.replace(/start|end/g,e=>q[e])}let ei=["left","right"],el=["right","left"],ea=["top","bottom"],eu=["bottom","top"];function ec(e){return e.replace(/left|right|bottom|top/g,e=>X[e])}function es(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ef(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ed(e,t,n){let r,{reference:o,floating:i}=e,l=er(t),a=ee(er(t)),u=et(a),c=J(t),s="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(Q(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let ep=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=ed(c,r,u),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=ed(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function eh(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=G(t,e),h=es(p),m=a[d?"floating"===f?"reference":"floating":f],v=ef(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=ef(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function em(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ev(e){return z.some(t=>e[t]>=0)}let eg=new Set(["left","top"]);async function ey(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=J(n),a=Q(n),u="y"===er(n),c=eg.has(l)?-1:1,s=i&&u?-1:1,f=G(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:d*c}:{x:d*c,y:p*s}}function ew(){return"undefined"!=typeof window}function ex(e){return eS(e)?(e.nodeName||"").toLowerCase():"#document"}function eb(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eE(e){var t;return null==(t=(eS(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eS(e){return!!ew()&&(e instanceof Node||e instanceof eb(e).Node)}function eC(e){return!!ew()&&(e instanceof Element||e instanceof eb(e).Element)}function eR(e){return!!ew()&&(e instanceof HTMLElement||e instanceof eb(e).HTMLElement)}function eT(e){return!!ew()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof eb(e).ShadowRoot)}let ek=new Set(["inline","contents"]);function eA(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eV(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ek.has(o)}let eL=new Set(["table","td","th"]),eP=[":popover-open",":modal"];function eM(e){return eP.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eN=["transform","translate","scale","rotate","perspective"],ej=["transform","translate","scale","rotate","perspective","filter"],eD=["paint","layout","strict","content"];function eO(e){let t=eW(),n=eC(e)?eV(e):e;return eN.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ej.some(e=>(n.willChange||"").includes(e))||eD.some(e=>(n.contain||"").includes(e))}function eW(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eI=new Set(["html","body","#document"]);function eH(e){return eI.has(ex(e))}function eV(e){return eb(e).getComputedStyle(e)}function eF(e){return eC(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eB(e){if("html"===ex(e))return e;let t=e.assignedSlot||e.parentNode||eT(e)&&e.host||eE(e);return eT(t)?t.host:t}function e_(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eB(t);return eH(n)?t.ownerDocument?t.ownerDocument.body:t.body:eR(n)&&eA(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=eb(o);if(i){let e=ez(l);return t.concat(l,l.visualViewport||[],eA(o)?o:[],e&&n?e_(e):[])}return t.concat(o,e_(o,[],n))}function ez(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eZ(e){let t=eV(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eR(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=K(n)!==i||K(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function e$(e){return eC(e)?e:e.contextElement}function eK(e){let t=e$(e);if(!eR(t))return U(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eZ(t),l=(i?K(n.width):n.width)/r,a=(i?K(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eY=U(0);function eU(e){let t=eb(e);return eW()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eY}function eX(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=e$(e),a=U(1);t&&(r?eC(r)&&(a=eK(r)):a=eK(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===eb(l))&&o)?eU(l):U(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=eb(l),t=r&&eC(r)?eb(r):r,n=e,o=ez(n);for(;o&&r&&t!==n;){let e=eK(o),t=o.getBoundingClientRect(),r=eV(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,f*=e.x,d*=e.y,c+=i,s+=l,o=ez(n=eb(o))}}return ef({width:f,height:d,x:c,y:s})}function eq(e,t){let n=eF(e).scrollLeft;return t?t.left+n:eX(eE(e)).left+n}function eG(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eq(e,r)),y:r.top+t.scrollTop}}let eJ=new Set(["absolute","fixed"]);function eQ(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=eb(e),r=eE(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eW();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eE(e),n=eF(e),r=e.ownerDocument.body,o=$(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=$(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eq(e),a=-n.scrollTop;return"rtl"===eV(r).direction&&(l+=$(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eE(e));else if(eC(t))r=function(e,t){let n=eX(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eR(e)?eK(e):U(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eU(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ef(r)}function e0(e){return"static"===eV(e).position}function e1(e,t){if(!eR(e)||"fixed"===eV(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eE(e)===n&&(n=n.ownerDocument.body),n}function e2(e,t){var n;let r=eb(e);if(eM(e))return r;if(!eR(e)){let t=eB(e);for(;t&&!eH(t);){if(eC(t)&&!e0(t))return t;t=eB(t)}return r}let o=e1(e,t);for(;o&&(n=o,eL.has(ex(n)))&&e0(o);)o=e1(o,t);return o&&eH(o)&&e0(o)&&!eO(o)?r:o||function(e){let t=eB(e);for(;eR(t)&&!eH(t);){if(eO(t))return t;if(eM(t))break;t=eB(t)}return null}(e)||r}let e5=async function(e){let t=this.getOffsetParent||e2,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eR(t),o=eE(t),i="fixed"===n,l=eX(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=U(0);if(r||!r&&!i){if(("body"!==ex(t)||eA(o))&&(a=eF(t)),r){let e=eX(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eq(o))}i&&!r&&o&&(u.x=eq(o));let c=!o||r||i?U(0):eG(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},e9={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eE(r),a=!!t&&eM(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=U(1),s=U(0),f=eR(r);if((f||!f&&!i)&&(("body"!==ex(r)||eA(l))&&(u=eF(r)),eR(r))){let e=eX(r);c=eK(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let d=!l||f||i?U(0):eG(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+d.x,y:n.y*c.y-u.scrollTop*c.y+s.y+d.y}},getDocumentElement:eE,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eM(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=e_(e,[],!1).filter(e=>eC(e)&&"body"!==ex(e)),o=null,i="fixed"===eV(e).position,l=i?eB(e):e;for(;eC(l)&&!eH(l);){let t=eV(l),n=eO(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eJ.has(o.position)||eA(l)&&!n&&function e(t,n){let r=eB(t);return!(r===n||!eC(r)||eH(r))&&("fixed"===eV(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eB(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eQ(t,n,o);return e.top=$(r.top,e.top),e.right=Z(r.right,e.right),e.bottom=Z(r.bottom,e.bottom),e.left=$(r.left,e.left),e},eQ(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:e2,getElementRects:e5,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eZ(e);return{width:t,height:n}},getScale:eK,isElement:eC,isRTL:function(e){return"rtl"===eV(e).direction}};function e7(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e6=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=G(e,t)||{};if(null==c)return{};let f=es(s),d={x:n,y:r},p=ee(er(o)),h=et(p),m=await l.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-d[p]-i.floating[h],w=d[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),b=x?x[g]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[g]||i.floating[h]);let E=b/2-m[h]/2-1,S=Z(f[v?"top":"left"],E),C=Z(f[v?"bottom":"right"],E),R=b-m[h]-C,T=b/2-m[h]/2+(y/2-w/2),k=$(S,Z(T,R)),A=!u.arrow&&null!=Q(o)&&T!==k&&i.reference[h]/2-(T<S?S:C)-m[h]/2<0,L=A?T<S?T-S:T-R:0;return{[p]:d[p]+L,data:{[p]:k,centerOffset:T-k-L,...A&&{alignmentOffset:L}},reset:A}}}),e4=(e,t,n)=>{let r=new Map,o={platform:e9,...n},i={...o.platform,_c:r};return ep(e,t,{...o,platform:i})};var e3="undefined"!=typeof document?f.useLayoutEffect:function(){};function e8(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e8(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e8(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function te(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tt(e,t){let n=te(e);return Math.round(t*n)/n}function tn(e){let t=f.useRef(e);return e3(()=>{t.current=e}),t}let tr=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e6({element:n.current,padding:r}).fn(t):{}:n?e6({element:n,padding:r}).fn(t):{}}}),to=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:l,middlewareData:a}=e,u=await ey(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}),options:[e,t]}},ti=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=G(n,e),c={x:t,y:r},s=await eh(e,u),f=er(J(o)),d=ee(f),p=c[d],h=c[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+s[e],r=p-s[t];p=$(n,Z(p,r))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+s[e],r=h-s[t];h=$(n,Z(h,r))}let m=a.fn({...e,[d]:p,[f]:h});return{...m,data:{x:m.x-t,y:m.y-r,enabled:{[d]:i,[f]:l}}}}}),options:[e,t]}},tl=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=G(n,e),s={x:t,y:r},f=er(o),d=ee(f),p=s[d],h=s[f],m=G(a,e),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+v.mainAxis,n=i.reference[d]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===d?"width":"height",t=eg.has(J(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[f])||0)+(t?0:v.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[f])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}),options:[e,t]}},ta=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:d}=e,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=G(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let x=J(a),b=er(s),E=J(s)===s,S=await (null==f.isRTL?void 0:f.isRTL(d.floating)),C=m||(E||!y?[ec(s)]:function(e){let t=ec(e);return[eo(e),t,eo(t)]}(s)),R="none"!==g;!m&&R&&C.push(...function(e,t,n,r){let o=Q(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?el:ei;return t?ei:el;case"left":case"right":return t?ea:eu;default:return[]}}(J(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(eo)))),i}(s,y,g,S));let T=[s,...C],k=await eh(e,w),A=[],L=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&A.push(k[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=Q(e),o=ee(er(e)),i=et(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=ec(l)),[l,ec(l)]}(a,c,S);A.push(k[e[0]],k[e[1]])}if(L=[...L,{placement:a,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=T[e];if(t&&(!("alignment"===h&&b!==er(t))||L.every(e=>e.overflows[0]>0&&er(e.placement)===b)))return{data:{index:e,overflows:L},reset:{placement:t}};let n=null==(i=L.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=L.filter(e=>{if(R){let t=er(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},tu=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=e,{apply:s=()=>{},...f}=G(n,e),d=await eh(e,f),p=J(l),h=Q(l),m="y"===er(l),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-d.top-d.bottom,w=v-d.left-d.right,x=Z(g-d[o],y),b=Z(v-d[i],w),E=!e.middlewareData.shift,S=x,C=b;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(C=w),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!h){let e=$(d.left,0),t=$(d.right,0),n=$(d.top,0),r=$(d.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:$(d.left,d.right)):S=g-2*(0!==n||0!==r?n+r:$(d.top,d.bottom))}await s({...e,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return v!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},tc=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=G(n,e);switch(r){case"referenceHidden":{let n=em(await eh(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:ev(n)}}}case"escaped":{let n=em(await eh(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:ev(n)}}}default:return{}}}}),options:[e,t]}},ts=(e,t)=>({...tr(e),options:[e,t]});var tf=f.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,v.jsx)(b.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,v.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tf.displayName="Arrow";var td="Popper",[tp,th]=g(td),[tm,tv]=tp(td),tg=e=>{let{__scopePopper:t,children:n}=e,[r,o]=f.useState(null);return(0,v.jsx)(tm,{scope:t,anchor:r,onAnchorChange:o,children:n})};tg.displayName=td;var ty="PopperAnchor",tw=f.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tv(ty,n),l=f.useRef(null),a=(0,y.e)(t,l);return f.useEffect(()=>{i.onAnchorChange(r?.current||l.current)}),r?null:(0,v.jsx)(b.WV.div,{...o,ref:a})});tw.displayName=ty;var tx="PopperContent",[tb,tE]=tp(tx),tS=f.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:l=0,arrowPadding:a=0,avoidCollisions:u=!0,collisionBoundary:c=[],collisionPadding:s=0,sticky:d="partial",hideWhenDetached:h=!1,updatePositionStrategy:m="optimized",onPlaced:g,...w}=e,x=tv(tx,n),[S,C]=f.useState(null),R=(0,y.e)(t,e=>C(e)),[T,k]=f.useState(null),A=function(e){let[t,n]=f.useState(void 0);return V(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(T),L=A?.width??0,P=A?.height??0,M="number"==typeof s?s:{top:0,right:0,bottom:0,left:0,...s},N=Array.isArray(c)?c:[c],j=N.length>0,D={padding:M,boundary:N.filter(tk),altBoundary:j},{refs:O,floatingStyles:W,placement:I,isPositioned:H,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:a=!0,whileElementsMounted:u,open:c}=e,[s,d]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=f.useState(r);e8(h,r)||m(r);let[v,g]=f.useState(null),[y,w]=f.useState(null),x=f.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),b=f.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||v,S=l||y,C=f.useRef(null),R=f.useRef(null),T=f.useRef(s),k=null!=u,A=tn(u),L=tn(o),P=tn(c),M=f.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};L.current&&(e.platform=L.current),e4(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};N.current&&!e8(T.current,t)&&(T.current=t,p.flushSync(()=>{d(t)}))})},[h,t,n,L,P]);e3(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let N=f.useRef(!1);e3(()=>(N.current=!0,()=>{N.current=!1}),[]),e3(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(A.current)return A.current(E,S,M);M()}},[E,S,M,A,k]);let j=f.useMemo(()=>({reference:C,floating:R,setReference:x,setFloating:b}),[x,b]),D=f.useMemo(()=>({reference:E,floating:S}),[E,S]),O=f.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=tt(D.floating,s.x),r=tt(D.floating,s.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...te(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,a,D.floating,s.x,s.y]);return f.useMemo(()=>({...s,update:M,refs:j,elements:D,floatingStyles:O}),[s,M,j,D,O])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=e$(e),f=i||l?[...s?e_(s):[],...e_(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let d=s&&u?function(e,t){let n,r=null,o=eE(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:f,width:d,height:p}=c;if(a||t(),!d||!p)return;let h=Y(f),m=Y(o.clientWidth-(s+d)),v={rootMargin:-h+"px "+-m+"px "+-Y(o.clientHeight-(f+p))+"px "+-Y(s)+"px",threshold:$(0,Z(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e7(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eX(e):null;return c&&function t(){let r=eX(e);m&&!e7(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===m}),elements:{reference:x.anchor},middleware:[to({mainAxis:o+P,alignmentAxis:l}),u&&ti({mainAxis:!0,crossAxis:!1,limiter:"partial"===d?tl():void 0,...D}),u&&ta({...D}),tu({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),T&&ts({element:T,padding:a}),tA({arrowWidth:L,arrowHeight:P}),h&&tc({strategy:"referenceHidden",...D})]}),[B,_]=tL(I),z=E(g);V(()=>{H&&z?.()},[H,z]);let K=F.arrow?.x,U=F.arrow?.y,X=F.arrow?.centerOffset!==0,[q,G]=f.useState();return V(()=>{S&&G(window.getComputedStyle(S).zIndex)},[S]),(0,v.jsx)("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:H?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,v.jsx)(tb,{scope:n,placedSide:B,onArrowChange:k,arrowX:K,arrowY:U,shouldHideArrow:X,children:(0,v.jsx)(b.WV.div,{"data-side":B,"data-align":_,...w,ref:R,style:{...w.style,animation:H?void 0:"none"}})})})});tS.displayName=tx;var tC="PopperArrow",tR={top:"bottom",right:"left",bottom:"top",left:"right"},tT=f.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tE(tC,n),i=tR[o.placedSide];return(0,v.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,v.jsx)(tf,{...r,ref:t,style:{...r.style,display:"block"}})})});function tk(e){return null!==e}tT.displayName=tC;var tA=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[u,c]=tL(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===u?(p=i?s:`${f}px`,h=`${-a}px`):"top"===u?(p=i?s:`${f}px`,h=`${r.floating.height+a}px`):"right"===u?(p=`${-a}px`,h=i?s:`${d}px`):"left"===u&&(p=`${r.floating.width+a}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function tL(e){let[t,n="center"]=e.split("-");return[t,n]}var tP=f.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=f.useState(!1);V(()=>i(!0),[]);let l=n||o&&globalThis?.document?.body;return l?p.createPortal((0,v.jsx)(b.WV.div,{...r,ref:t}),l):null});tP.displayName="Portal";var tM=d[" useInsertionEffect ".trim().toString()]||V;function tN({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,l]=function({defaultProp:e,onChange:t}){let[n,r]=f.useState(e),o=f.useRef(n),i=f.useRef(t);return tM(()=>{i.current=t},[t]),f.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),a=void 0!==e,u=a?e:o;{let t=f.useRef(void 0!==e);f.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,r])}return[u,f.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else i(t)},[a,e,i,l])]}Symbol("RADIX:SYNC_STATE");var tj=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});f.forwardRef((e,t)=>(0,v.jsx)(b.WV.span,{...e,ref:t,style:{...tj,...e.style}})).displayName="VisuallyHidden";var tD=new WeakMap,tO=new WeakMap,tW={},tI=0,tH=function(e){return e&&(e.host||tH(e.parentNode))},tV=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tH(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tW[n]||(tW[n]=new WeakMap);var i=tW[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tD.get(e)||0)+1,c=(i.get(e)||0)+1;tD.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tO.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tI++,function(){l.forEach(function(e){var t=tD.get(e)-1,o=i.get(e)-1;tD.set(e,t),i.set(e,o),t||(tO.has(e)||e.removeAttribute(r),tO.delete(e)),o||e.removeAttribute(n)}),--tI||(tD=new WeakMap,tD=new WeakMap,tO=new WeakMap,tW={})}},tF=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tV(r,o,n,"aria-hidden")):function(){return null}},tB=function(){return(tB=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function t_(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var tz="right-scroll-bar-position",tZ="width-before-scroll-bar";function t$(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tK="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,tY=new WeakMap,tU=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),l=[],a=!1,u={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=i(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var n=l;l=[],n.forEach(e),t=l}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=tB({async:!0,ssr:!1},o),u),tX=function(){},tq=f.forwardRef(function(e,t){var n,r,o,i,l=f.useRef(null),a=f.useState({onScrollCapture:tX,onWheelCapture:tX,onTouchMoveCapture:tX}),u=a[0],c=a[1],s=e.forwardProps,d=e.children,p=e.className,h=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,x=e.inert,b=e.allowPinchZoom,E=e.as,S=e.gapMode,C=t_(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[l,t],r=function(e){return n.forEach(function(t){return t$(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tK(function(){var e=tY.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||t$(e,null)}),r.forEach(function(e){t.has(e)||t$(e,o)})}tY.set(i,n)},[n]),i),T=tB(tB({},C),u);return f.createElement(f.Fragment,null,m&&f.createElement(g,{sideCar:tU,removeScrollBar:h,shards:v,noRelative:y,noIsolation:w,inert:x,setCallbacks:c,allowPinchZoom:!!b,lockRef:l,gapMode:S}),s?f.cloneElement(f.Children.only(d),tB(tB({},T),{ref:R})):f.createElement(void 0===E?"div":E,tB({},T,{className:p,ref:R}),d))});tq.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tq.classNames={fullWidth:tZ,zeroRight:tz};var tG=function(e){var t=e.sideCar,n=t_(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,tB({},n))};tG.isSideCarExport=!0;var tJ=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tQ=function(){var e=tJ();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},t0=function(){var e=tQ();return function(t){return e(t.styles,t.dynamic),null}},t1={left:0,top:0,right:0,gap:0},t2=function(e){return parseInt(e||"",10)||0},t5=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t2(n),t2(r),t2(o)]},t9=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t1;var t=t5(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t7=t0(),t6="data-scroll-locked",t4=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t6,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tz," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tZ," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tz," .").concat(tz," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tZ," .").concat(tZ," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t6,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},t3=function(){var e=parseInt(document.body.getAttribute(t6)||"0",10);return isFinite(e)?e:0},t8=function(){f.useEffect(function(){return document.body.setAttribute(t6,(t3()+1).toString()),function(){var e=t3()-1;e<=0?document.body.removeAttribute(t6):document.body.setAttribute(t6,e.toString())}},[])},ne=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t8();var i=f.useMemo(function(){return t9(o)},[o]);return f.createElement(t7,{styles:t4(i,!t,o,n?"":"!important")})},nt=!1;if("undefined"!=typeof window)try{var nn=Object.defineProperty({},"passive",{get:function(){return nt=!0,!0}});window.addEventListener("test",nn,nn),window.removeEventListener("test",nn,nn)}catch(e){nt=!1}var nr=!!nt&&{passive:!1},no=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},ni=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nl(e,r)){var o=na(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nl=function(e,t){return"v"===e?no(t,"overflowY"):no(t,"overflowX")},na=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},nu=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,f=a>0,d=0,p=0;do{if(!u)break;var h=na(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&nl(e,u)&&(d+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},nc=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ns=function(e){return[e.deltaX,e.deltaY]},nf=function(e){return e&&"current"in e?e.current:e},nd=0,np=[],nh=(tU.useMedium(function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(nd++)[0],i=f.useState(t0)[0],l=f.useRef(e);f.useEffect(function(){l.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nf),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=nc(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=ni(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=ni(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return nu(p,t,e,"h"===p?u:c,!0)},[]),u=f.useCallback(function(e){if(np.length&&np[np.length-1]===i){var n="deltaY"in e?ns(e):nc(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(nf).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?a(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=f.useCallback(function(e){n.current=nc(e),r.current=void 0},[]),d=f.useCallback(function(t){c(t.type,ns(t),t.target,a(t,e.lockRef.current))},[]),p=f.useCallback(function(t){c(t.type,nc(t),t.target,a(t,e.lockRef.current))},[]);f.useEffect(function(){return np.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,nr),document.addEventListener("touchmove",u,nr),document.addEventListener("touchstart",s,nr),function(){np=np.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,nr),document.removeEventListener("touchmove",u,nr),document.removeEventListener("touchstart",s,nr)}},[]);var h=e.removeScrollBar,m=e.inert;return f.createElement(f.Fragment,null,m?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?f.createElement(ne,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),tG),nm=f.forwardRef(function(e,t){return f.createElement(tq,tB({},e,{ref:t,sideCar:nh}))});nm.classNames=tq.classNames;var nv=[" ","Enter","ArrowUp","ArrowDown"],ng=[" ","Enter"],ny="Select",[nw,nx,nb]=function(e){let t=e+"CollectionProvider",[n,r]=g(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=f.useRef(null),i=f.useRef(new Map).current;return(0,v.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let a=e+"CollectionSlot",u=(0,w.Z8)(a),c=f.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(a,n),l=(0,y.e)(t,o.collectionRef);return(0,v.jsx)(u,{ref:l,children:r})});c.displayName=a;let s=e+"CollectionItemSlot",d="data-radix-collection-item",p=(0,w.Z8)(s),h=f.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=f.useRef(null),a=(0,y.e)(t,l),u=i(s,n);return f.useEffect(()=>(u.itemMap.set(l,{ref:l,...o}),()=>void u.itemMap.delete(l))),(0,v.jsx)(p,{[d]:"",ref:a,children:r})});return h.displayName=s,[{Provider:l,Slot:c,ItemSlot:h},function(t){let n=i(e+"CollectionConsumer",t);return f.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${d}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(ny),[nE,nS]=g(ny,[nb,th]),nC=th(),[nR,nT]=nE(ny),[nk,nA]=nE(ny),nL=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:a,onValueChange:u,dir:c,name:s,autoComplete:d,disabled:p,required:h,form:m}=e,g=nC(t),[y,w]=f.useState(null),[b,E]=f.useState(null),[S,C]=f.useState(!1),R=function(e){let t=f.useContext(x);return e||t||"ltr"}(c),[T,k]=tN({prop:r,defaultProp:o??!1,onChange:i,caller:ny}),[A,L]=tN({prop:l,defaultProp:a,onChange:u,caller:ny}),P=f.useRef(null),M=!y||m||!!y.closest("form"),[N,j]=f.useState(new Set),D=Array.from(N).map(e=>e.props.value).join(";");return(0,v.jsx)(tg,{...g,children:(0,v.jsxs)(nR,{required:h,scope:t,trigger:y,onTriggerChange:w,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:_(),value:A,onValueChange:L,open:T,onOpenChange:k,dir:R,triggerPointerDownPosRef:P,disabled:p,children:[(0,v.jsx)(nw.Provider,{scope:t,children:(0,v.jsx)(nk,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(e=>{j(t=>new Set(t).add(e))},[]),onNativeOptionRemove:f.useCallback(e=>{j(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,v.jsxs)(ri,{"aria-hidden":!0,required:h,tabIndex:-1,name:s,autoComplete:d,value:A,onChange:e=>L(e.target.value),disabled:p,form:m,children:[void 0===A?(0,v.jsx)("option",{value:""}):null,Array.from(N)]},D):null]})})};nL.displayName=ny;var nP="SelectTrigger",nM=f.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nC(n),l=nT(nP,n),a=l.disabled||r,u=(0,y.e)(t,l.onTriggerChange),c=nx(n),s=f.useRef("touch"),[d,p,h]=ra(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),r=ru(t,e,n);void 0!==r&&l.onValueChange(r.value)}),g=e=>{a||(l.onOpenChange(!0),h()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,v.jsx)(tw,{asChild:!0,...i,children:(0,v.jsx)(b.WV.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":rl(l.value)?"":void 0,...o,ref:u,onClick:m(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==s.current&&g(e)}),onPointerDown:m(o.onPointerDown,e=>{s.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:m(o.onKeyDown,e=>{let t=""!==d.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),(!t||" "!==e.key)&&nv.includes(e.key)&&(g(),e.preventDefault())})})})});nM.displayName=nP;var nN="SelectValue",nj=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nT(nN,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,f=(0,y.e)(t,u.onValueNodeChange);return V(()=>{c(s)},[c,s]),(0,v.jsx)(b.WV.span,{...a,ref:f,style:{pointerEvents:"none"},children:rl(u.value)?(0,v.jsx)(v.Fragment,{children:l}):i})});nj.displayName=nN;var nD=f.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,v.jsx)(b.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nD.displayName="SelectIcon";var nO=e=>(0,v.jsx)(tP,{asChild:!0,...e});nO.displayName="SelectPortal";var nW="SelectContent",nI=f.forwardRef((e,t)=>{let n=nT(nW,e.__scopeSelect),[r,o]=f.useState();return(V(()=>{o(new DocumentFragment)},[]),n.open)?(0,v.jsx)(nB,{...e,ref:t}):r?p.createPortal((0,v.jsx)(nH,{scope:e.__scopeSelect,children:(0,v.jsx)(nw.Slot,{scope:e.__scopeSelect,children:(0,v.jsx)("div",{children:e.children})})}),r):null});nI.displayName=nW;var[nH,nV]=nE(nW),nF=(0,w.Z8)("SelectContent.RemoveScroll"),nB=f.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:h,sticky:g,hideWhenDetached:w,avoidCollisions:x,...b}=e,E=nT(nW,n),[S,C]=f.useState(null),[T,k]=f.useState(null),P=(0,y.e)(t,e=>C(e)),[M,N]=f.useState(null),[D,O]=f.useState(null),W=nx(n),[I,H]=f.useState(!1),V=f.useRef(!1);f.useEffect(()=>{if(S)return tF(S)},[S]),f.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??L()),document.body.insertAdjacentElement("beforeend",e[1]??L()),A++,()=>{1===A&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),A--}},[]);let F=f.useCallback(e=>{let[t,...n]=W().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),n?.focus(),document.activeElement!==o))return},[W,T]),B=f.useCallback(()=>F([M,S]),[F,M,S]);f.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:_,triggerPointerDownPosRef:z}=E;f.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,_,z]),f.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[Z,$]=ra(e=>{let t=W().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ru(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=f.useCallback((e,t,n)=>{let r=!V.current&&!n;(void 0!==E.value&&E.value===t||r)&&(N(e),r&&(V.current=!0))},[E.value]),Y=f.useCallback(()=>S?.focus(),[S]),U=f.useCallback((e,t,n)=>{let r=!V.current&&!n;(void 0!==E.value&&E.value===t||r)&&O(e)},[E.value]),X="popper"===r?nz:n_,q=X===nz?{side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:h,sticky:g,hideWhenDetached:w,avoidCollisions:x}:{};return(0,v.jsx)(nH,{scope:n,content:S,viewport:T,onViewportChange:k,itemRefCallback:K,selectedItem:M,onItemLeave:Y,itemTextRefCallback:U,focusSelectedItem:B,selectedItemText:D,position:r,isPositioned:I,searchRef:Z,children:(0,v.jsx)(nm,{as:nF,allowPinchZoom:!0,children:(0,v.jsx)(j,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:m(o,e=>{E.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,v.jsx)(R,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,v.jsx)(X,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...b,...q,onPlaced:()=>H(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:m(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});nB.displayName="SelectContentImpl";var n_=f.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nT(nW,n),l=nV(nW,n),[a,u]=f.useState(null),[c,s]=f.useState(null),d=(0,y.e)(t,e=>s(e)),p=nx(n),m=f.useRef(!1),g=f.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:E,focusSelectedItem:S}=l,C=f.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&c&&w&&x&&E){let e=i.trigger.getBoundingClientRect(),t=c.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,u=e.width+l,c=Math.max(u,t.width),s=h(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.left=s+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,u=e.width+l,c=Math.max(u,t.width),s=h(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.right=s+"px"}let l=p(),u=window.innerHeight-20,s=w.scrollHeight,f=window.getComputedStyle(c),d=parseInt(f.borderTopWidth,10),v=parseInt(f.paddingTop,10),g=parseInt(f.borderBottomWidth,10),y=d+v+s+parseInt(f.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,y),S=window.getComputedStyle(w),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,k=x.offsetHeight/2,A=d+v+(x.offsetTop+k);if(A<=T){let e=l.length>0&&x===l[l.length-1].ref.current;a.style.bottom="0px";let t=c.clientHeight-w.offsetTop-w.offsetHeight;a.style.height=A+Math.max(u-T,k+(e?R:0)+t+g)+"px"}else{let e=l.length>0&&x===l[0].ref.current;a.style.top="0px";let t=Math.max(T,d+w.offsetTop+(e?C:0)+k);a.style.height=t+(y-A)+"px",w.scrollTop=A-T+w.offsetTop}a.style.margin="10px 0",a.style.minHeight=b+"px",a.style.maxHeight=u+"px",r?.(),requestAnimationFrame(()=>m.current=!0)}},[p,i.trigger,i.valueNode,a,c,w,x,E,i.dir,r]);V(()=>C(),[C]);let[R,T]=f.useState();V(()=>{c&&T(window.getComputedStyle(c).zIndex)},[c]);let k=f.useCallback(e=>{e&&!0===g.current&&(C(),S?.(),g.current=!1)},[C,S]);return(0,v.jsx)(nZ,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:m,onScrollButtonChange:k,children:(0,v.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,v.jsx)(b.WV.div,{...o,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});n_.displayName="SelectItemAlignedPosition";var nz=f.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nC(n);return(0,v.jsx)(tS,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nz.displayName="SelectPopperPosition";var[nZ,n$]=nE(nW,{}),nK="SelectViewport",nY=f.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nV(nK,n),l=n$(nK,n),a=(0,y.e)(t,i.onViewportChange),u=f.useRef(0);return(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,v.jsx)(nw.Slot,{scope:n,children:(0,v.jsx)(b.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:m(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=l;if(r?.current&&n){let e=Math.abs(u.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});nY.displayName=nK;var nU="SelectGroup",[nX,nq]=nE(nU),nG=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=_();return(0,v.jsx)(nX,{scope:n,id:o,children:(0,v.jsx)(b.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nG.displayName=nU;var nJ="SelectLabel",nQ=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nq(nJ,n);return(0,v.jsx)(b.WV.div,{id:o.id,...r,ref:t})});nQ.displayName=nJ;var n0="SelectItem",[n1,n2]=nE(n0),n5=f.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,a=nT(n0,n),u=nV(n0,n),c=a.value===r,[s,d]=f.useState(i??""),[p,h]=f.useState(!1),g=(0,y.e)(t,e=>u.itemRefCallback?.(e,r,o)),w=_(),x=f.useRef("touch"),E=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,v.jsx)(n1,{scope:n,value:r,disabled:o,textId:w,isSelected:c,onItemTextChange:f.useCallback(e=>{d(t=>t||(e?.textContent??"").trim())},[]),children:(0,v.jsx)(nw.ItemSlot,{scope:n,value:r,disabled:o,textValue:s,children:(0,v.jsx)(b.WV.div,{role:"option","aria-labelledby":w,"data-highlighted":p?"":void 0,"aria-selected":c&&p,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:g,onFocus:m(l.onFocus,()=>h(!0)),onBlur:m(l.onBlur,()=>h(!1)),onClick:m(l.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:m(l.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:m(l.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:m(l.onPointerMove,e=>{x.current=e.pointerType,o?u.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:m(l.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:m(l.onKeyDown,e=>{u.searchRef?.current!==""&&" "===e.key||(ng.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});n5.displayName=n0;var n9="SelectItemText",n7=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,l=nT(n9,n),a=nV(n9,n),u=n2(n9,n),c=nA(n9,n),[s,d]=f.useState(null),h=(0,y.e)(t,e=>d(e),u.onItemTextChange,e=>a.itemTextRefCallback?.(e,u.value,u.disabled)),m=s?.textContent,g=f.useMemo(()=>(0,v.jsx)("option",{value:u.value,disabled:u.disabled,children:m},u.value),[u.disabled,u.value,m]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=c;return V(()=>(w(g),()=>x(g)),[w,x,g]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(b.WV.span,{id:u.textId,...i,ref:h}),u.isSelected&&l.valueNode&&!l.valueNodeHasChildren?p.createPortal(i.children,l.valueNode):null]})});n7.displayName=n9;var n6="SelectItemIndicator",n4=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return n2(n6,n).isSelected?(0,v.jsx)(b.WV.span,{"aria-hidden":!0,...r,ref:t}):null});n4.displayName=n6;var n3="SelectScrollUpButton",n8=f.forwardRef((e,t)=>{let n=nV(n3,e.__scopeSelect),r=n$(n3,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,y.e)(t,r.onScrollButtonChange);return V(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(rn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n8.displayName=n3;var re="SelectScrollDownButton",rt=f.forwardRef((e,t)=>{let n=nV(re,e.__scopeSelect),r=n$(re,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,y.e)(t,r.onScrollButtonChange);return V(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,v.jsx)(rn,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});rt.displayName=re;var rn=f.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nV("SelectScrollButton",n),l=f.useRef(null),a=nx(n),u=f.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return f.useEffect(()=>()=>u(),[u]),V(()=>{let e=a().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[a]),(0,v.jsx)(b.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:m(o.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(r,50))}),onPointerMove:m(o.onPointerMove,()=>{i.onItemLeave?.(),null===l.current&&(l.current=window.setInterval(r,50))}),onPointerLeave:m(o.onPointerLeave,()=>{u()})})}),rr=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,v.jsx)(b.WV.div,{"aria-hidden":!0,...r,ref:t})});rr.displayName="SelectSeparator";var ro="SelectArrow";f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nC(n),i=nT(ro,n),l=nV(ro,n);return i.open&&"popper"===l.position?(0,v.jsx)(tT,{...o,...r,ref:t}):null}).displayName=ro;var ri=f.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{let o=f.useRef(null),i=(0,y.e)(r,o),l=function(e){let t=f.useRef({value:e,previous:e});return f.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return f.useEffect(()=>{let e=o.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[l,t]),(0,v.jsx)(b.WV.select,{...n,style:{...tj,...n.style},ref:i,defaultValue:t})});function rl(e){return""===e||void 0===e}function ra(e){let t=E(e),n=f.useRef(""),r=f.useRef(0),o=f.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=f.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function ru(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}ri.displayName="SelectBubbleInput";var rc=nL,rs=nM,rf=nj,rd=nD,rp=nO,rh=nI,rm=nY,rv=nG,rg=nQ,ry=n5,rw=n7,rx=n4,rb=n8,rE=rt,rS=rr}}]);