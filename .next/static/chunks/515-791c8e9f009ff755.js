"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[515],{2894:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4280:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},2549:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(2898).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1299:function(e,t,n){n.d(t,{aU:function(){return en},x8:function(){return er},dk:function(){return et},zt:function(){return G},fC:function(){return Q},Dx:function(){return ee},l_:function(){return J}});var r=n(2265),o=n(4887),i=n(5744),a=n(2210),s=n(7733),u=n(6989),l=n(9249),d=n(2730),c=n(5655),p=e=>{let t,n;let{present:o,children:i}=e,s=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef(null),s=r.useRef(e),u=r.useRef("none"),[l,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=f(a.current);u.current="mounted"===l?e:"none"},[l]),(0,c.b)(()=>{let t=a.current,n=s.current;if(n!==e){let r=u.current,o=f(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),(0,c.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=f(a.current).includes(n.animationName);if(n.target===o&&r&&(d("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(u.current=f(a.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(o),u="function"==typeof i?i({present:s.isPresent}):r.Children.only(i),l=(0,a.e)(s.ref,(t=Object.getOwnPropertyDescriptor(u.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?u.ref:(t=Object.getOwnPropertyDescriptor(u,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?u.props.ref:u.props.ref||u.ref);return"function"==typeof i||s.isPresent?r.cloneElement(u,{ref:l}):null};function f(e){return e?.animationName||"none"}p.displayName="Presence";var v=n(9381),m=n(6459),w=n(3763),y=n(8281),E=n(7437),T="ToastProvider",[x,h,g]=(0,s.B)("Toast"),[N,b]=(0,u.b)("Toast",[g]),[C,M]=N(T),R=e=>{let{__scopeToast:t,label:n="Notification",duration:o=5e3,swipeDirection:i="right",swipeThreshold:a=50,children:s}=e,[u,l]=r.useState(null),[d,c]=r.useState(0),p=r.useRef(!1),f=r.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${T}\`. Expected non-empty \`string\`.`),(0,E.jsx)(x.Provider,{scope:t,children:(0,E.jsx)(C,{scope:t,label:n,duration:o,swipeDirection:i,swipeThreshold:a,toastCount:d,viewport:u,onViewportChange:l,onToastAdd:r.useCallback(()=>c(e=>e+1),[]),onToastRemove:r.useCallback(()=>c(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:f,children:s})})};R.displayName=T;var P="ToastViewport",k=["F8"],L="toast.viewportPause",j="toast.viewportResume",D=r.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:o=k,label:i="Notifications ({hotkey})",...s}=e,u=M(P,n),d=h(n),c=r.useRef(null),p=r.useRef(null),f=r.useRef(null),m=r.useRef(null),w=(0,a.e)(t,m,u.onViewportChange),y=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),T=u.toastCount>0;r.useEffect(()=>{let e=e=>{0!==o.length&&o.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),r.useEffect(()=>{let e=c.current,t=m.current;if(T&&e&&t){let n=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(L);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},r=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(j);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||r()},i=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",i),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[T,u.isClosePausedRef]);let g=r.useCallback(({tabbingDirection:e})=>{let t=d().map(t=>{let n=t.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===e?r:r.reverse()});return("forwards"===e?t.reverse():t).flat()},[d]);return r.useEffect(()=>{let e=m.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){let n=document.activeElement,r=t.shiftKey;if(t.target===e&&r){p.current?.focus();return}let o=g({tabbingDirection:r?"backwards":"forwards"}),i=o.findIndex(e=>e===n);B(o.slice(i+1))?t.preventDefault():r?p.current?.focus():f.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[d,g]),(0,E.jsxs)(l.I0,{ref:c,role:"region","aria-label":i.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:T?void 0:"none"},children:[T&&(0,E.jsx)(A,{ref:p,onFocusFromOutsideViewport:()=>{B(g({tabbingDirection:"forwards"}))}}),(0,E.jsx)(x.Slot,{scope:n,children:(0,E.jsx)(v.WV.ol,{tabIndex:-1,...s,ref:w})}),T&&(0,E.jsx)(A,{ref:f,onFocusFromOutsideViewport:()=>{B(g({tabbingDirection:"backwards"}))}})]})});D.displayName=P;var I="ToastFocusProxy",A=r.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=M(I,n);return(0,E.jsx)(y.TX,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;i.viewport?.contains(t)||r()}})});A.displayName=I;var F="Toast",S=r.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:o,onOpenChange:a,...s}=e,[u,l]=(0,w.T)({prop:r,defaultProp:o??!0,onChange:a,caller:F});return(0,E.jsx)(p,{present:n||u,children:(0,E.jsx)(_,{open:u,...s,ref:t,onClose:()=>l(!1),onPause:(0,m.W)(e.onPause),onResume:(0,m.W)(e.onResume),onSwipeStart:(0,i.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.M)(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${n}px`)}),onSwipeCancel:(0,i.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.M)(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${n}px`),l(!1)})})})});S.displayName=F;var[O,W]=N(F,{onClose(){}}),_=r.forwardRef((e,t)=>{let{__scopeToast:n,type:s="foreground",duration:u,open:d,onClose:c,onEscapeKeyDown:p,onPause:f,onResume:w,onSwipeStart:y,onSwipeMove:T,onSwipeCancel:h,onSwipeEnd:g,...N}=e,b=M(F,n),[C,R]=r.useState(null),P=(0,a.e)(t,e=>R(e)),k=r.useRef(null),D=r.useRef(null),I=u||b.duration,A=r.useRef(0),S=r.useRef(I),W=r.useRef(0),{onToastAdd:_,onToastRemove:K}=b,V=(0,m.W)(()=>{C?.contains(document.activeElement)&&b.viewport?.focus(),c()}),Z=r.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(W.current),A.current=new Date().getTime(),W.current=window.setTimeout(V,e))},[V]);r.useEffect(()=>{let e=b.viewport;if(e){let t=()=>{Z(S.current),w?.()},n=()=>{let e=new Date().getTime()-A.current;S.current=S.current-e,window.clearTimeout(W.current),f?.()};return e.addEventListener(L,n),e.addEventListener(j,t),()=>{e.removeEventListener(L,n),e.removeEventListener(j,t)}}},[b.viewport,I,f,w,Z]),r.useEffect(()=>{d&&!b.isClosePausedRef.current&&Z(I)},[d,I,b.isClosePausedRef,Z]),r.useEffect(()=>(_(),()=>K()),[_,K]);let X=r.useMemo(()=>C?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!r){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}}),n}(C):null,[C]);return b.viewport?(0,E.jsxs)(E.Fragment,{children:[X&&(0,E.jsx)(U,{__scopeToast:n,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:X}),(0,E.jsx)(O,{scope:n,onClose:V,children:o.createPortal((0,E.jsx)(x.ItemSlot,{scope:n,children:(0,E.jsx)(l.fC,{asChild:!0,onEscapeKeyDown:(0,i.M)(p,()=>{b.isFocusedToastEscapeKeyDownRef.current||V(),b.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,E.jsx)(v.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":b.swipeDirection,...N,ref:P,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.M)(e.onKeyDown,e=>{"Escape"!==e.key||(p?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,V()))}),onPointerDown:(0,i.M)(e.onPointerDown,e=>{0===e.button&&(k.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.M)(e.onPointerMove,e=>{if(!k.current)return;let t=e.clientX-k.current.x,n=e.clientY-k.current.y,r=!!D.current,o=["left","right"].includes(b.swipeDirection),i=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,a=o?i(0,t):0,s=o?0:i(0,n),u="touch"===e.pointerType?10:2,l={x:a,y:s},d={originalEvent:e,delta:l};r?(D.current=l,z("toast.swipeMove",T,d,{discrete:!1})):Y(l,b.swipeDirection,u)?(D.current=l,z("toast.swipeStart",y,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(n)>u)&&(k.current=null)}),onPointerUp:(0,i.M)(e.onPointerUp,e=>{let t=D.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),D.current=null,k.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};Y(t,b.swipeDirection,b.swipeThreshold)?z("toast.swipeEnd",g,r,{discrete:!0}):z("toast.swipeCancel",h,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),b.viewport)})]}):null}),U=e=>{let{__scopeToast:t,children:n,...o}=e,i=M(F,t),[a,s]=r.useState(!1),[u,l]=r.useState(!1);return function(e=()=>{}){let t=(0,m.W)(e);(0,c.b)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>s(!0)),r.useEffect(()=>{let e=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,E.jsx)(d.h,{asChild:!0,children:(0,E.jsx)(y.TX,{...o,children:a&&(0,E.jsxs)(E.Fragment,{children:[i.label," ",n]})})})},K=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,E.jsx)(v.WV.div,{...r,ref:t})});K.displayName="ToastTitle";var V=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,E.jsx)(v.WV.div,{...r,ref:t})});V.displayName="ToastDescription";var Z="ToastAction",X=r.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,E.jsx)(q,{altText:n,asChild:!0,children:(0,E.jsx)(H,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Z}\`. Expected non-empty \`string\`.`),null)});X.displayName=Z;var $="ToastClose",H=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,o=W($,n);return(0,E.jsx)(q,{asChild:!0,children:(0,E.jsx)(v.WV.button,{type:"button",...r,ref:t,onClick:(0,i.M)(e.onClick,o.onClose)})})});H.displayName=$;var q=r.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...o}=e;return(0,E.jsx)(v.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function z(e,t,n,{discrete:r}){let o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,v.jH)(o,i):o.dispatchEvent(i)}var Y=(e,t,n=0)=>{let r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return"left"===t||"right"===t?i&&r>n:!i&&o>n};function B(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var G=R,J=D,Q=S,ee=K,et=V,en=X,er=H}}]);