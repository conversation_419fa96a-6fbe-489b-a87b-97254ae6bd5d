"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[972],{5818:function(e,t,n){n.d(t,{T:function(){return i},w:function(){return l}});var r=n(7437),a=n(6264),o=n(2169);let s={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"};function i(e){let{size:t="md",className:n,text:i}=e;return(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(a.Z,{className:(0,o.cn)("animate-spin",s[t],n)}),i&&(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:i})]})}function l(e){let{text:t="<PERSON><PERSON> tải..."}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsx)(i,{size:"lg",text:t})})}},5306:function(e,t,n){n.d(t,{z:function(){return a}});var r=n(6862);function a(){let{toast:e}=(0,r.pm)();return{showSuccess:(t,n)=>{e({title:t,description:n,variant:"default"})},showError:(t,n)=>{e({title:t,description:n,variant:"destructive"})},showWarning:(t,n)=>{e({title:t,description:n,variant:"default"})},showInfo:(t,n)=>{e({title:t,description:n,variant:"default"})}}}},4946:function(e,t,n){n.d(t,{qs:function(){return p},Ve:function(){return s},hz:function(){return o},Pn:function(){return l},N1:function(){return i},_b:function(){return d},ew:function(){return u},VZ:function(){return h},cD:function(){return c}});var r=n(2067),a=n.n(r);async function o(e,t){let n=await fetch("https://actvn-schedule.cors-ngosangns.workers.dev/subject",{method:"POST",body:Object.keys(e).map(t=>encodeURIComponent(t)+"="+encodeURIComponent(e[t])).join("&"),headers:{"Content-Type":"application/x-www-form-urlencoded","x-cors-headers":JSON.stringify({Cookie:t})}});return await n.text()}async function s(e){console.log("Fetching calendar with token:",e);let t=await fetch("https://actvn-schedule.cors-ngosangns.workers.dev/subject",{method:"GET",headers:{"x-cors-headers":JSON.stringify({Cookie:e})}});return console.log("Calendar response status:",t.status),console.log("Calendar response headers:",Object.fromEntries(t.headers.entries())),await t.text()}function i(e,t){let n=e.match(RegExp('id="'+t+'" value="(.+?)"',"g"));return!!(n&&n.length&&(n=(n=n[0]).match(/value="(.+?)"/))&&n.length)&&(n=n[1])}function l(e){return e.replace(/src="(.+?)"/g,"")}function c(e){let t=e.match(/<span id="lblStudent">(.+?)<\/span/g);return t&&t.length&&(t=t[0].match(/<span id="lblStudent">(.+?)<\/span/))&&t.length>1?t[1]:"KIT Club"}async function d(e){if(!e)throw Error("empty data");return await new Promise((t,n)=>{let r=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let a="self.onmessage = "+e.toString();for(let e of n)a+="\n"+e.toString();let o=new Blob([a],{type:"text/javascript"}),s=URL.createObjectURL(o);return new Worker(s)}(e=>self.postMessage(f(e.data)),f);r.onmessage=e=>t(e.data?e.data:!1===e.data?{data_subject:[]}:null),r.onerror=e=>n(e),r.postMessage(function(e){if(!e||!e.length)return!1;let t=(e=(e=e.replace(/ {2,}/gm," ")).replace(/<!--.*?-->|\t|(?:\r?\n[ \t]*)+/gm,"")).match(/<table.+?gridRegistered.+?<\/table>/g);if(t&&t.length&&(e=t[0]),"undefined"==typeof document)throw Error("DOM operations not available on server side");let n=document.createElement("div");n.id="cleanTKB",n.style.display="none",n.innerHTML=e,document.body.appendChild(n);let r=Array.prototype.map.call(n.querySelectorAll("#gridRegistered tr"),e=>Array.prototype.map.call(e.querySelectorAll("td"),e=>{var t;return null===(t=e.innerHTML)||!1===t?"":(t=t.toString()).replace(/<[^>]*>/g,"")}));return document.body.removeChild(n),!!r&&r}(e))}).catch(e=>{throw e})}function u(e){if("undefined"==typeof DOMParser)throw Error("DOMParser not available on server side");let t=new DOMParser().parseFromString(e,"text/html").getElementById("Form1");if(!t)return{};let n={};return t.querySelectorAll("input, select, textarea").forEach(e=>{e.name&&e.value&&(n[e.name]=e.value)}),n}function h(e){if("undefined"==typeof DOMParser)throw Error("DOMParser not available on server side");let t=new DOMParser().parseFromString(e,"text/html").querySelector("select[name=drpSemester]");if(!t)return null;let n=t.querySelectorAll("option"),r=[],a="";for(let e=0;e<n.length;e++){let t=n[e],o=t.innerHTML.split("_");r.push({value:t.value,from:o[1],to:o[2],th:o[0]}),t.selected&&(a=t.value)}return{semesters:r,currentSemester:a}}function f(e){let t,n;let r={lop_hoc_phan:"Lớp học phần",hoc_phan:"Học phần",thoi_gian:"Thời gian",dia_diem:"\xd0ịa điểm",giang_vien:"Giảng vi\xean",si_so:"Sĩ số",so_dk:"Số \xd0K",so_tc:"Số TC"};if(0==e.length||!1==e||(e.pop(),1==e.length))return!1;let a=e[0],o=e.slice(1,e.length),s=Array.prototype.map.call(o,function(e){let o="([0-9]{2}\\/[0-9]{2}\\/[0-9]{4}).+?([0-9]{2}\\/[0-9]{2}\\/[0-9]{4}):(\\([0-9]*\\))?(.+?)((Từ)|$)+?",s=RegExp(o,"g"),i=new RegExp(o),l=e[a.indexOf(r.dia_diem)],c=l.match(/\([0-9,]+?\)/g);c||(l=null),l&&(c.forEach(e=>l=l.replace(e,"\n"+e)),l=l.match(/\n\(([0-9,]+?)\)(.+)/g),(l=Array.prototype.map.call(l,e=>{let t=e.match(/\n\(([0-9,]+?)\)(.+)/);return t=[t[1].split(","),t[2]],Array.prototype.map.call(t[0],e=>"(".concat(e,") ").concat(t[1]))}).flat()).sort(function(e,t){return parseInt(e[1])-parseInt(t[1])}),l=Array.prototype.map.call(l,e=>e.replace(/^\([0-9]+?\) /i,"").trim()));let d=e[a.indexOf(r.thoi_gian)].match(s);return!!d&&(d.forEach((e,r)=>{if(!(e=e.match(i))){d.splice(r,1);return}e[4]=e[4].split("&nbsp;&nbsp;&nbsp;"),e[4].shift(),e[4].forEach((t,n)=>{if(!(t=t.match(/((Thứ .+?)||Chủ nhật) tiết (.+?)$/))){e[4].splice(n,1);return}t&&(t[3]=t[3].split(/[^0-9]+/g),t[3].pop(),t={dow:({"Thứ 2":2,"Thứ 3":3,"Thứ 4":4,"Thứ 5":5,"Thứ 6":6,"Thứ 7":7,"Chủ nhật":8})[t[1]],shi:t[3]}),e[4][n]=t}),e[1]="".concat(e[1].substr(3,2),"/").concat(e[1].substr(0,2),"/").concat(e[1].substr(6,4)),e[2]="".concat(e[2].substr(3,2),"/").concat(e[2].substr(0,2),"/").concat(e[2].substr(6,4)),e[1]=new Date(Date.parse(e[1])),e[2]=new Date(Date.parse(e[2])),e={startTime:e[1],endTime:e[2],dayOfWeek:e[4],address:l?l[r]:null},t?t>e.startTime&&(t=e.startTime):t=e.startTime,n?n<e.endTime&&(n=e.endTime):n=e.endTime,d[r]=e}),{lop_hoc_phan:e[a.indexOf(r.lop_hoc_phan)],hoc_phan:e[a.indexOf(r.hoc_phan)],giang_vien:e[a.indexOf(r.giang_vien)],si_so:e[a.indexOf(r.si_so)],so_dk:e[a.indexOf(r.so_dk)],so_tc:e[a.indexOf(r.so_tc)],tkb:d})});t=t.getTime(),n=n.getTime();let i=[];for(let e=t;e<=n;e+=864e5){if(new Date(e).getDay()+1==2||e==t){i.push([{time:e,shift:[]}]);continue}i[i.length-1].push({time:e,shift:[]})}for(let e of i)for(let t of e)t.shift=Array.from({length:16},(e,n)=>{for(let e of s)if(e){for(let r of e.tkb)if(t.time>=r.startTime.getTime()&&t.time<=r.endTime.getTime()){for(let a of r.dayOfWeek)if((a.dow==new Date(t.time).getDay()+1||new Date(t.time).getDay()+1==1&&8==a.dow)&&n+1>=parseInt(a.shi[0])&&n+1<=parseInt(a.shi[a.shi.length-1])){if(n+1===parseInt(a.shi[0]))return{content:"".concat(e.lop_hoc_phan).concat(r.address?" (học tại ".concat(r.address,")"):""),name:e.lop_hoc_phan,address:r.address?r.address:null,length:a.shi.length};return{content:null,name:null,address:null,length:0}}}}return{content:null,name:null,address:null,length:1}});return{data_subject:i}}function p(e,t){if(!t||!t.data_subject||!Array.isArray(t.data_subject)){console.error("Invalid calendar data for export");return}let n=[{},{start:"000000",end:"004500"},{start:"005000",end:"013500"},{start:"014000",end:"022500"},{start:"023500",end:"032000"},{start:"032500",end:"041000"},{start:"041500",end:"050000"},{start:"053000",end:"061500"},{start:"062000",end:"070500"},{start:"071000",end:"075500"},{start:"080500",end:"085000"},{start:"085500",end:"094000"},{start:"094500",end:"103000"},{start:"110000",end:"114500"},{start:"114500",end:"123000"},{start:"124500",end:"133000"},{start:"133000",end:"141500"}],r="BEGIN:VCALENDAR\nCALSCALE:GREGORIAN\nMETHOD:PUBLISH\n\n";t.data_subject.forEach(e=>{for(let t of e){let e=new Date(t.time);t.shift&&Array.isArray(t.shift)&&t.shift.forEach((t,o)=>{if(t.content){let l=o+1,c=o+(parseInt(t.length)||1);if(l<n.length&&c<n.length){var s,i;let o=null===(s=n[l])||void 0===s?void 0:s.start,d=null===(i=n[c])||void 0===i?void 0:i.end;o&&d&&(r+="BEGIN:VEVENT\nDTSTART:".concat(a()(e).format("YYYYMMDD"),"T").concat(o,"Z\n")+"DTEND:".concat(a()(e).format("YYYYMMDD"),"T").concat(d,"Z\n"),t.address&&(r+="LOCATION:".concat(t.address,"\n")),r+="SUMMARY:".concat(t.name,"\n")+"END:VEVENT\n\n")}}})}}),r+="END:VCALENDAR";let o=document.createElement("a");o.setAttribute("href","data:text/plain;charset=utf-8,"+encodeURIComponent(r)),o.setAttribute("download","".concat(e?e.split(" - ")[0]:"tkb_export",".ics")),o.style.display="none",document.body.appendChild(o),o.click(),document.body.removeChild(o)}},5474:function(e,t,n){n.d(t,{k:function(){return l},x:function(){return i}});var r=n(4946),a=n(8104),o=n(4922),s=n.n(o);async function i(e,t){let n=await fetch("https://actvn-schedule.cors-ngosangns.workers.dev/login",{method:"GET"}),a=await n.text(),o={__VIEWSTATE:(0,r.N1)(a,"__VIEWSTATE"),__EVENTVALIDATION:(0,r.N1)(a,"__EVENTVALIDATION"),txtUserName:e.toUpperCase(),txtPassword:s()(t),btnSubmit:"Đăng nhập"},i=(n=await fetch("https://actvn-schedule.cors-ngosangns.workers.dev/login",{method:"POST",body:Object.keys(o).map(e=>encodeURIComponent(e)+"="+encodeURIComponent(e in o?o[e]:"")).join("&"),headers:{"Content-Type":"application/x-www-form-urlencoded"}})).headers.get("set-cookie")||n.headers.get("Set-Cookie");if(console.log("Login response headers:",Object.fromEntries(n.headers.entries())),console.log("Set-Cookie header:",i),i)return i;let l=await n.text();return console.log("Login response text (first 500 chars):",l.substring(0,500)),l&&l.startsWith("SignIn="),l}function l(){(0,a.Nk)()}}}]);