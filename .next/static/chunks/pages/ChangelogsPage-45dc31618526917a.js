(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[377],{5200:function(n,i,c){(window.__NEXT_P=window.__NEXT_P||[]).push(["/ChangelogsPage",function(){return c(6002)}])},6002:function(n,i,c){"use strict";c.r(i),c.d(i,{default:function(){return h}});var l=c(5893);function h(){return(0,l.jsxs)("div",{className:"max-w-2xl mx-auto py-8 space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-semibold mb-2",children:"Version 2022.12 - 12/2022:"}),(0,l.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[(0,l.jsx)("li",{children:"Th\xeam lại t\xednh năng chọn học kỳ để xem lịch học."}),(0,l.jsx)("li",{children:"Bỏ t\xednh năng xem lịch từ file Excel."}),(0,l.jsx)("li",{children:"N\xe2ng cấp giao diện sử dụng DaisyUI."}),(0,l.jsx)("li",{children:"Th\xeam t\xednh năng hiển thị lịch học tuần hiện tại khi v\xe0o trang web."}),(0,l.jsx)("li",{children:"Bảo tr\xec API."}),(0,l.jsx)("li",{children:"Tối ưu code xử l\xed."}),(0,l.jsx)("li",{children:"Converted from SvelteKit to React with shadcn/ui."})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-semibold mb-2",children:"Version 10 - 12/2022:"}),(0,l.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[(0,l.jsx)("li",{children:"Bảo tr\xec API."}),(0,l.jsx)("li",{children:"Cập nhật c\xe1c package."})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-semibold mb-2",children:"Version 9 - 01/2021:"}),(0,l.jsx)("ul",{className:"list-disc ml-8 space-y-1",children:(0,l.jsx)("li",{children:"Bảo tr\xec API."})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-semibold mb-2",children:"Version 8 - 06/2020:"}),(0,l.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[(0,l.jsx)("li",{children:"Th\xeam t\xednh năng xem lịch từ file Excel (cơ sở miền Nam)."}),(0,l.jsx)("li",{children:"Th\xeam lại t\xednh năng xem lịch bằng t\xe0i khoản học viện."}),(0,l.jsx)("li",{children:"Bỏ bố cục xem lịch theo kỳ học."}),(0,l.jsx)("li",{children:"Bỏ t\xednh năng hiển thị bảng th\xf4ng tin chi tiết c\xe1c m\xf4n học."}),(0,l.jsx)("li",{children:"Bỏ t\xednh năng chuyển đổi hiển thị t\xean m\xf4n/m\xe3 m\xf4n."}),(0,l.jsx)("li",{children:"N\xe2ng cấp giao diện sử dụng Material Design v\xe0 dark mode."})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-semibold mb-2",children:"Version 6 - 01/2020:"}),(0,l.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[(0,l.jsx)("li",{children:"Th\xeam t\xednh năng xem lịch bằng m\xe3 nguồn HTML."}),(0,l.jsx)("li",{children:"Bỏ t\xednh năng xem lịch bằng t\xe0i khoản học viện."}),(0,l.jsx)("li",{children:"Bỏ t\xednh năng chọn học k\xec để xem lịch."}),(0,l.jsx)("li",{children:"Th\xeam 2 bố cục xem lịch theo tuần."})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-semibold mb-2",children:"Version 1 - 10/2019:"}),(0,l.jsxs)("ul",{className:"list-disc ml-8 space-y-1",children:[(0,l.jsx)("li",{children:"T\xednh năng xem lịch bằng t\xe0i khoản học viện."}),(0,l.jsx)("li",{children:"Bố cục lịch học hiển thị to\xe0n kỳ học theo bảng truyền thống."}),(0,l.jsx)("li",{children:"T\xednh năng hiển thị bảng th\xf4ng tin chi tiết c\xe1c m\xf4n học."}),(0,l.jsx)("li",{children:"T\xednh năng chuyển đổi hiển thị t\xean m\xf4n/m\xe3 m\xf4n."}),(0,l.jsx)("li",{children:"T\xednh năng chọn học k\xec để xem lịch."}),(0,l.jsx)("li",{children:"T\xednh năng xem lịch học dạng r\xfat gọn."}),(0,l.jsx)("li",{children:"T\xednh năng tải lịch học."}),(0,l.jsx)("li",{children:"T\xednh năng chuyển đổi light/dark mode."})]})]})]})}}},function(n){n.O(0,[774,888,179],function(){return n(n.s=5200)}),_N_E=n.O()}]);