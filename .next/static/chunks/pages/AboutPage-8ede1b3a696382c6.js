(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[765],{4297:function(n,e,t){"use strict";t.d(e,{Z:function(){return c}});var r=t(7294),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),c=(n,e)=>{let t=(0,r.forwardRef)(({color:t="currentColor",size:c=24,strokeWidth:l=2,absoluteStrokeWidth:a,className:h="",children:o,...u},d)=>(0,r.createElement)("svg",{ref:d,...s,width:c,height:c,stroke:t,strokeWidth:a?24*Number(l)/Number(c):l,className:["lucide",`lucide-${i(n)}`,h].join(" "),...u},[...e.map(([n,e])=>(0,r.createElement)(n,e)),...Array.isArray(o)?o:[o]]));return t.displayName=`${n}`,t}},8814:function(n,e,t){"use strict";t.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(4297).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},3734:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/AboutPage",function(){return t(2061)}])},2061:function(n,e,t){"use strict";t.r(e),t.d(e,{default:function(){return i}});var r=t(5893),s=t(8814);function i(){return(0,r.jsxs)("div",{className:"max-w-2xl mx-auto py-8 space-y-4",children:[(0,r.jsxs)("p",{children:["Hello, m\xecnh l\xe0 Sang. Đến từ Huế, sống tại TP HCM, c\xf3 thời gian ở H\xe0 Nội. ",(0,r.jsx)("br",{}),"Hồi xưa đẹp trai lắm giờ đỡ nhiều rồi :) ",(0,r.jsx)("br",{}),"Đ\xe2y l\xe0 web c\xe1 nh\xe2n của m\xecnh:"," ",(0,r.jsxs)("a",{href:"https://ngosangns.com",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["https://ngosangns.com",(0,r.jsx)(s.Z,{className:"w-3 h-3"})]})]}),(0,r.jsx)("p",{children:"Trang web n\xe0y l\xe0 một dự \xe1n nằm trong chuỗi c\xe1c dữ \xe1n hỗ trợ sinh vi\xean của C\xe2u lạc bộ lập tr\xecnh Học viện Kỹ thuật Mật M\xe3 hay c\xf2n được gọi l\xe0 KIT (KMA IT)."}),(0,r.jsx)("p",{children:"Một số dự \xe1n kh\xe1c m\xe0 ch\xfang m\xecnh đ\xe3 thực hiện:"}),(0,r.jsxs)("ul",{className:"list-disc ml-8 space-y-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsxs)("a",{href:"https://play.google.com/store/apps/details?id=kma.hatuan314.schedule",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["KIT Schedule",(0,r.jsx)(s.Z,{className:"w-3 h-3"})]})," ","- Ứng dụng xem lịch học tr\xean điện thoại d\xe0nh cho sinh vi\xean học viện KMA."]}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("a",{href:"https://github.com/ngosangns/tin-chi",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["KMA T\xedn chỉ",(0,r.jsx)(s.Z,{className:"w-3 h-3"})]})," ","- Tool hỗ trợ sinh vi\xean sắp xếp lịch học hợp l\xed cho bản th\xe2n v\xe0o mỗi m\xf9a đăng k\xfd học."]})]})]})}}},function(n){n.O(0,[774,888,179],function(){return n(n.s=3734)}),_N_E=n.O()}]);