(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[156],{8814:function(e,s,n){"use strict";n.d(s,{Z:function(){return r}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(4297).Z)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},1814:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/LoginPage",function(){return n(5135)}])},5135:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return v}});var r=n(5893),t=n(7294),a=n(9332),l=n(4284),i=n(5747),c=n(3482);let d=t.forwardRef((e,s)=>{let{className:n,type:t,...a}=e;return(0,r.jsx)("input",{type:t,className:(0,c.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),ref:s,...a})});d.displayName="Input";var o=n(5320),u=t.forwardRef((e,s)=>(0,r.jsx)(o.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));u.displayName="Label";let m=(0,n(2003).j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),h=t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,r.jsx)(u,{ref:s,className:(0,c.cn)(m(),n),...t})});h.displayName=u.displayName;var x=n(5734);let f=t.forwardRef((e,s)=>{let{className:n,...t}=e;return(0,r.jsx)("textarea",{className:(0,c.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n),ref:s,...t})});f.displayName="Textarea";var p=n(3217),g=n(7653),b=n(8814),j=n(6374),y=n(3230),w=n(3014);function v(){let e=(0,a.useRouter)(),[s,n]=(0,t.useState)(""),[c,o]=(0,t.useState)(""),[u,m]=(0,t.useState)(""),[v,N]=(0,t.useState)(!1),[k,_]=(0,t.useState)(""),[Z,E]=(0,t.useState)(""),H=async n=>{n.preventDefault(),N(!0),_("");try{let n=await (0,w.x)(s,c),r=(0,y.Pn)(await (0,y.Ve)(n)),t=await (0,y._b)(r),a=(0,y.cD)(r),l=(0,y.ew)(r),i=(0,y.VZ)(r);(0,j.OH)({signInToken:n,mainForm:l,semesters:i,calendar:t,student:a}),console.log("Login successful, saved data:",{calendar:t,student:a,semesters:i}),window.dispatchEvent(new CustomEvent("loginSuccess")),e.push("/calendar")}catch(e){console.error("Login error:",e),_("C\xf3 lỗi xảy ra khi lấy th\xf4ng tin thời kh\xf3a biểu hoặc t\xe0i khoản/mật khẩu kh\xf4ng đ\xfang!"),(0,w.k)()}finally{N(!1)}},T=async s=>{s.preventDefault(),E("");try{let s=(0,y.Pn)(u),n=await (0,y._b)(s),r=(0,y.cD)(s),t=(0,y.ew)(s),a=(0,y.VZ)(s);(0,j.OH)({mainForm:t,semesters:a,calendar:n,student:r}),window.dispatchEvent(new CustomEvent("loginSuccess")),e.push("/calendar")}catch(e){console.error("User response processing error:",e),E("C\xf3 lỗi xảy ra khi lấy th\xf4ng tin thời kh\xf3a biểu!"),(0,w.k)()}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,r.jsx)("div",{className:"w-full max-w-6xl",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)(l.Zb,{children:[(0,r.jsx)(l.Ol,{children:(0,r.jsx)(l.ll,{className:"text-lg",children:"XEM THỜI KH\xd3A BIỂU TỪ T\xc0I KHOẢN TRƯỜNG"})}),(0,r.jsx)(l.aY,{children:(0,r.jsxs)("form",{onSubmit:H,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(h,{htmlFor:"username",children:"Username"}),(0,r.jsx)(d,{id:"username",type:"text",placeholder:"Username",value:s,onChange:e=>n(e.target.value),required:!0,disabled:v})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(h,{htmlFor:"password",children:"Password"}),(0,r.jsx)(d,{id:"password",type:"password",placeholder:"Password",value:c,onChange:e=>o(e.target.value),required:!0,disabled:v})]}),(0,r.jsxs)(i.z,{type:"submit",className:"w-full",disabled:v,children:[v&&(0,r.jsx)(p.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Đăng nhập"]}),k&&(0,r.jsxs)(x.bZ,{variant:"destructive",children:[(0,r.jsx)(g.Z,{className:"h-4 w-4"}),(0,r.jsx)(x.X,{children:k})]})]})})]}),(0,r.jsxs)(l.Zb,{className:"bg-secondary",children:[(0,r.jsx)(l.Ol,{children:(0,r.jsx)(l.ll,{className:"text-lg",children:"XEM THỜI KH\xd3A BIỂU TỪ M\xc3 NGUỒN"})}),(0,r.jsxs)(l.aY,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Nhằm tăng t\xednh bảo mật cho t\xe0i khoản của c\xe1c bạn, web đ\xe3 hỗ trợ th\xeam t\xednh năng dịch thời kh\xf3a biểu từ m\xe3 nguồn HTML."}),(0,r.jsx)("p",{className:"text-sm font-medium mb-2",children:"Hướng dẫn:"}),(0,r.jsx)("div",{className:"bg-background rounded-lg p-4 mb-4",children:(0,r.jsxs)("ol",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"1"}),(0,r.jsxs)("span",{children:["Bạn v\xe0o trang quản l\xed của trường tại địa chỉ"," ",(0,r.jsxs)("a",{href:"http://qldt.actvn.edu.vn",target:"_blank",rel:"noreferrer",className:"underline inline-flex items-center gap-1",children:["http://qldt.actvn.edu.vn",(0,r.jsx)(b.Z,{className:"w-3 h-3"})]})," ","v\xe0 tiến h\xe0nh đăng nhập."]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"2"}),(0,r.jsxs)("span",{children:["V\xe0o mục: ",(0,r.jsx)("strong",{children:"Đăng k\xfd học"})," → ",(0,r.jsx)("strong",{children:"Xem kết quả ĐKH"}),"."]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"3"}),(0,r.jsxs)("span",{children:["Chuột phải chọn ",(0,r.jsx)("strong",{children:"Xem m\xe3 nguồn (View page source)"})," v\xe0 copy to\xe0n bộ code."]})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2 mt-0.5",children:"4"}),(0,r.jsxs)("span",{children:["D\xe1n code đ\xf3 v\xe0o \xf4 b\xean dưới v\xe0 bấm ",(0,r.jsx)("strong",{children:"Xem lịch học"}),"."]})]})]})}),(0,r.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(h,{htmlFor:"userResponse",children:"M\xe3 nguồn HTML"}),(0,r.jsx)(f,{id:"userResponse",placeholder:"D\xe1n m\xe3 nguồn của trang xem lịch học tại đ\xe2y...",value:u,onChange:e=>m(e.target.value),required:!0,disabled:v,rows:3})]}),(0,r.jsxs)(i.z,{type:"submit",className:"w-full",variant:"secondary",disabled:v,children:[v&&(0,r.jsx)(p.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Xem lịch học"]}),Z&&(0,r.jsxs)(x.bZ,{variant:"destructive",children:[(0,r.jsx)(g.Z,{className:"h-4 w-4"}),(0,r.jsx)(x.X,{children:Z})]})]})]})]})]})})})}}},function(e){e.O(0,[774,885,305,384,888,179],function(){return e(e.s=1814)}),_N_E=e.O()}]);