(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{8321:function(e,r,s){Promise.resolve().then(s.bind(s,7563))},7563:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return G}});var t=s(7437),n=s(2265),a=s(4033),l=s(1865),i=s(5706),c=s(3449),d=s(5671),o=s(575),u=s(2169);let m=n.forwardRef((e,r)=>{let{className:s,type:n,...a}=e;return(0,t.jsx)("input",{type:n,className:(0,u.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...a})});m.displayName="Input";let h=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("textarea",{className:(0,u.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...n})});h.displayName="Textarea";var x=s(6061);let f=(0,x.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),p=n.forwardRef((e,r)=>{let{className:s,variant:n,...a}=e;return(0,t.jsx)("div",{ref:r,role:"alert",className:(0,u.cn)(f({variant:n}),s),...a})});p.displayName="Alert",n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("h5",{ref:r,className:(0,u.cn)("mb-1 font-medium leading-none tracking-tight",s),...n})}).displayName="AlertTitle";let g=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)("div",{ref:r,className:(0,u.cn)("text-sm [&_p]:leading-relaxed",s),...n})});g.displayName="AlertDescription";var b=s(6823);let j=n.forwardRef((e,r)=>{let{className:s,orientation:n="horizontal",decorative:a=!0,...l}=e;return(0,t.jsx)(b.f,{ref:r,decorative:a,orientation:n,className:(0,u.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",s),...l})});j.displayName=b.f.displayName;var N=s(7256),v=s(6743);let w=(0,x.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),y=n.forwardRef((e,r)=>{let{className:s,...n}=e;return(0,t.jsx)(v.f,{ref:r,className:(0,u.cn)(w(),s),...n})});y.displayName=v.f.displayName;let k=l.RV,S=n.createContext({}),C=e=>{let{...r}=e;return(0,t.jsx)(S.Provider,{value:{name:r.name},children:(0,t.jsx)(l.Qr,{...r})})},R=()=>{let e=n.useContext(S),r=n.useContext(Z),{getFieldState:s,formState:t}=(0,l.Gc)(),a=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...a}},Z=n.createContext({}),V=n.forwardRef((e,r)=>{let{className:s,...a}=e,l=n.useId();return(0,t.jsx)(Z.Provider,{value:{id:l},children:(0,t.jsx)("div",{ref:r,className:(0,u.cn)("space-y-2",s),...a})})});V.displayName="FormItem";let F=n.forwardRef((e,r)=>{let{className:s,...n}=e,{error:a,formItemId:l}=R();return(0,t.jsx)(y,{ref:r,className:(0,u.cn)(a&&"text-destructive",s),htmlFor:l,...n})});F.displayName="FormLabel";let _=n.forwardRef((e,r)=>{let{...s}=e,{error:n,formItemId:a,formDescriptionId:l,formMessageId:i}=R();return(0,t.jsx)(N.g7,{ref:r,id:a,"aria-describedby":n?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!n,...s})});_.displayName="FormControl",n.forwardRef((e,r)=>{let{className:s,...n}=e,{formDescriptionId:a}=R();return(0,t.jsx)("p",{ref:r,id:a,className:(0,u.cn)("text-sm text-muted-foreground",s),...n})}).displayName="FormDescription";let T=n.forwardRef((e,r)=>{var s;let{className:n,children:a,...l}=e,{error:i,formMessageId:c}=R(),d=i?String(null!==(s=null==i?void 0:i.message)&&void 0!==s?s:""):a;return d?(0,t.jsx)("p",{ref:r,id:c,className:(0,u.cn)("text-sm font-medium text-destructive",n),...l,children:d}):null});T.displayName="FormMessage";var z=s(5818),D=s(7972),I=s(5589),A=s(6637),H=s(1981),M=s(3711),O=s(5300),P=s(5306),E=s(8104),L=s(4946),X=s(5474);let Y=c.Ry({username:c.Z_().min(1,"Vui l\xf2ng nhập t\xean đăng nhập"),password:c.Z_().min(1,"Vui l\xf2ng nhập mật khẩu")}),q=c.Ry({userResponse:c.Z_().min(1,"Vui l\xf2ng nhập nội dung phản hồi từ website")});function G(){let e=(0,a.useRouter)(),{loginWithCredentials:r,processManualData:s}=function(){let{login:e,logout:r,setLoading:s,setError:t}=(0,O.useAuth)(),{setCalendar:a,setStudent:l}=(0,O.useCalendar)(),{showSuccess:i,showError:c}=(0,P.z)(),[d,o]=(0,n.useState)(!1),m=(0,n.useCallback)(async(r,n)=>{s(),t(null);try{let s=await (0,X.x)(r,n),t=(0,L.Pn)(await (0,L.Ve)(s)),c=await (0,L._b)(t),d=(0,L.cD)(t),o=(0,L.ew)(t),u=(0,L.VZ)(t);return(0,E.OH)({signInToken:s,mainForm:o,semesters:u,calendar:c,student:d}),e({id:r,name:d||r},s),a(c),l(d),i("Đăng nhập th\xe0nh c\xf4ng!"),{success:!0,data:{calendar:c,student:d,mainForm:o,semesters:u}}}catch(r){console.error("Login error:",r);let e=(0,u.e$)(r);return t(e),c("Đăng nhập thất bại",e),(0,X.k)(),{success:!1,error:e}}},[e,a,l,s,t,i,c]),h=(0,n.useCallback)(async r=>{s(),t(null);try{let s=(0,L.Pn)(r),t=await (0,L._b)(s),n=(0,L.cD)(s),c=(0,L.ew)(s),d=(0,L.VZ)(s);return(0,E.OH)({mainForm:c,semesters:d,calendar:t,student:n}),e({id:"manual-user",name:n||"Manual User"},""),a(t),l(n),i("Dữ liệu đ\xe3 được xử l\xfd th\xe0nh c\xf4ng!"),{success:!0,data:{calendar:t,student:n,mainForm:c,semesters:d}}}catch(r){console.error("Manual response processing error:",r);let e="C\xf3 lỗi xảy ra khi xử l\xfd dữ liệu!";return t(e),c("Xử l\xfd dữ liệu thất bại",e),(0,X.k)(),{success:!1,error:e}}},[e,a,l,s,t,i,c]);return{isProcessing:d,loginWithCredentials:m,processManualData:h,changeSemester:(0,n.useCallback)(async(e,r)=>{let{semesters:s,mainForm:t,signInToken:n}=r;if(e===s.currentSemester)return{success:!0,data:null};o(!0);try{let r={...t,drpSemester:e},s=await (0,L.hz)(r,n),c=(0,L.Pn)(s),d=await (0,L._b)(c),o=(0,L.cD)(c),u=(0,L.ew)(c),m=(0,L.VZ)(c),h={mainForm:u,semesters:m,calendar:d,student:o};return a(d),l(o),(0,E.OH)(h),i("Đ\xe3 cập nhật học kỳ th\xe0nh c\xf4ng!"),{success:!0,data:h}}catch(r){console.error("Semester change error:",r);let e="C\xf3 lỗi xảy ra khi lấy dữ liệu!";return c("Cập nhật học kỳ thất bại",e),{success:!1,error:e}}finally{o(!1)}},[a,l,i,c]),exportCalendar:(0,n.useCallback)((e,r)=>{try{return(0,L.qs)(e,r),i("Đ\xe3 xuất lịch th\xe0nh c\xf4ng!"),{success:!0}}catch(r){console.error("Export calendar error:",r);let e="C\xf3 lỗi xảy ra khi xuất lịch!";return c("Xuất lịch thất bại",e),{success:!1,error:e}}},[i,c]),logout:(0,n.useCallback)(()=>{(0,X.k)(),r(),i("Đ\xe3 đăng xuất th\xe0nh c\xf4ng!")},[r,i])}}(),[c,x]=(0,n.useState)(!1),f=(0,l.cI)({resolver:(0,i.F)(Y),defaultValues:{username:"",password:""}}),b=(0,l.cI)({resolver:(0,i.F)(q),defaultValues:{userResponse:""}}),N=async s=>{(await r(s.username,s.password)).success&&e.push("/calendar")},v=async r=>{(await s(r.userResponse)).success&&e.push("/calendar")};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-background to-muted/20",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"ACTVN Schedule"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Đăng nhập để xem thời kh\xf3a biểu của bạn"})]}),(0,t.jsxs)(d.Zb,{children:[(0,t.jsxs)(d.Ol,{children:[(0,t.jsxs)(d.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(D.Z,{className:"h-5 w-5"}),"Đăng nhập"]}),(0,t.jsx)(d.SZ,{children:"Sử dụng t\xe0i khoản ACTVN của bạn để đăng nhập"})]}),(0,t.jsx)(d.aY,{children:(0,t.jsx)(k,{...f,children:(0,t.jsxs)("form",{onSubmit:f.handleSubmit(N),className:"space-y-4",children:[(0,t.jsx)(C,{control:f.control,name:"username",render:e=>{let{field:r}=e;return(0,t.jsxs)(V,{children:[(0,t.jsx)(F,{children:"T\xean đăng nhập"}),(0,t.jsx)(_,{children:(0,t.jsx)(m,{...r,placeholder:"Nhập t\xean đăng nhập",disabled:f.formState.isSubmitting})}),(0,t.jsx)(T,{})]})}}),(0,t.jsx)(C,{control:f.control,name:"password",render:e=>{let{field:r}=e;return(0,t.jsxs)(V,{children:[(0,t.jsx)(F,{children:"Mật khẩu"}),(0,t.jsx)(_,{children:(0,t.jsx)(m,{...r,type:"password",placeholder:"Nhập mật khẩu",disabled:f.formState.isSubmitting})}),(0,t.jsx)(T,{})]})}}),(0,t.jsx)(o.z,{type:"submit",className:"w-full",disabled:f.formState.isSubmitting,children:f.formState.isSubmitting?(0,t.jsx)(z.T,{size:"sm",text:"Đang đăng nhập..."}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(I.Z,{className:"mr-2 h-4 w-4"}),"Đăng nhập"]})})]})})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)(j,{className:"w-full"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,t.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Hoặc"})})]}),(0,t.jsxs)(o.z,{variant:"outline",className:"w-full",onClick:()=>x(!c),children:[(0,t.jsx)(A.Z,{className:"mr-2 h-4 w-4"}),"Nhập dữ liệu thủ c\xf4ng"]})]}),c&&(0,t.jsxs)(d.Zb,{children:[(0,t.jsxs)(d.Ol,{children:[(0,t.jsxs)(d.ll,{className:"flex items-center gap-2",children:[(0,t.jsx)(A.Z,{className:"h-5 w-5"}),"Nhập dữ liệu thủ c\xf4ng"]}),(0,t.jsx)(d.SZ,{children:"D\xe1n nội dung HTML từ trang thời kh\xf3a biểu ACTVN"})]}),(0,t.jsxs)(d.aY,{children:[(0,t.jsx)(k,{...b,children:(0,t.jsxs)("form",{onSubmit:b.handleSubmit(v),className:"space-y-4",children:[(0,t.jsx)(C,{control:b.control,name:"userResponse",render:e=>{let{field:r}=e;return(0,t.jsxs)(V,{children:[(0,t.jsx)(F,{children:"Nội dung HTML"}),(0,t.jsx)(_,{children:(0,t.jsx)(h,{...r,placeholder:"D\xe1n nội dung HTML từ trang thời kh\xf3a biểu...",className:"min-h-[120px] font-mono text-sm",disabled:b.formState.isSubmitting})}),(0,t.jsx)(T,{})]})}}),(0,t.jsx)(o.z,{type:"submit",className:"w-full",disabled:b.formState.isSubmitting,children:b.formState.isSubmitting?(0,t.jsx)(z.T,{size:"sm",text:"Đang xử l\xfd..."}):"Xử l\xfd dữ liệu"})]})}),(0,t.jsxs)(p,{className:"mt-4",children:[(0,t.jsx)(H.Z,{className:"h-4 w-4"}),(0,t.jsxs)(g,{children:[(0,t.jsx)("strong",{children:"Hướng dẫn:"})," Truy cập"," ",(0,t.jsxs)("a",{href:"https://actvn.edu.vn",target:"_blank",rel:"noopener noreferrer",className:"underline inline-flex items-center gap-1",children:["trang ACTVN",(0,t.jsx)(M.Z,{className:"h-3 w-3"})]}),", đăng nhập v\xe0 sao ch\xe9p to\xe0n bộ nội dung trang thời kh\xf3a biểu rồi d\xe1n v\xe0o đ\xe2y."]})]})]})]})]})})}}},function(e){e.O(0,[990,666,746,710,972,971,938,744],function(){return e(e.s=8321)}),_N_E=e.O()}]);