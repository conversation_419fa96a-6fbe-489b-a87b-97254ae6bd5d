[{"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/about/page.tsx": "1", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/calendar/page.tsx": "2", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/changelogs/page.tsx": "3", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/layout.tsx": "4", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx": "5", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/login/page.tsx": "6", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx": "7", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/Header.tsx": "8", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/AppLayout.tsx": "9", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Footer.tsx": "10", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Header.tsx": "11", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/alert.tsx": "12", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/badge.tsx": "13", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/button.tsx": "14", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/card.tsx": "15", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/dialog.tsx": "16", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/empty-state.tsx": "17", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/error-boundary.tsx": "18", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/form.tsx": "19", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/input.tsx": "20", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/label.tsx": "21", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/lazy-image.tsx": "22", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/loading-spinner.tsx": "23", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/select.tsx": "24", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/separator.tsx": "25", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skeleton.tsx": "26", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skip-to-content.tsx": "27", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/table.tsx": "28", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/textarea.tsx": "29", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toast.tsx": "30", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toaster.tsx": "31", "/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx": "32", "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-calendar-data.ts": "33", "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-notifications.ts": "34", "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-toast.ts": "35", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/calendar.ts": "36", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/storage.ts": "37", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/user.ts": "38", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/worker.ts": "39", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/utils.ts": "40", "/Users/<USER>/Github/kma-schedule-ngosangns/src/middleware.ts": "41", "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/AboutPage.tsx": "42", "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/ChangelogsPage.tsx": "43", "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/LoginPage.tsx": "44", "/Users/<USER>/Github/kma-schedule-ngosangns/src/types/index.ts": "45"}, {"size": 5307, "mtime": 1751477183575, "results": "46", "hashOfConfig": "47"}, {"size": 15854, "mtime": 1751478734854, "results": "48", "hashOfConfig": "47"}, {"size": 5744, "mtime": 1751477261875, "results": "49", "hashOfConfig": "47"}, {"size": 448, "mtime": 1751477463407, "results": "50", "hashOfConfig": "47"}, {"size": 532, "mtime": 1751476607013, "results": "51", "hashOfConfig": "47"}, {"size": 7284, "mtime": 1751477388528, "results": "52", "hashOfConfig": "47"}, {"size": 579, "mtime": 1751477105151, "results": "53", "hashOfConfig": "47"}, {"size": 998, "mtime": 1751472703101, "results": "54", "hashOfConfig": "47"}, {"size": 1442, "mtime": 1751477455172, "results": "55", "hashOfConfig": "47"}, {"size": 418, "mtime": 1751476598421, "results": "56", "hashOfConfig": "47"}, {"size": 4165, "mtime": 1751476591486, "results": "57", "hashOfConfig": "47"}, {"size": 1515, "mtime": 1751471772544, "results": "58", "hashOfConfig": "47"}, {"size": 1128, "mtime": 1751476518992, "results": "59", "hashOfConfig": "47"}, {"size": 1697, "mtime": 1751471772550, "results": "60", "hashOfConfig": "47"}, {"size": 1795, "mtime": 1751471772559, "results": "61", "hashOfConfig": "47"}, {"size": 3848, "mtime": 1751476518989, "results": "62", "hashOfConfig": "47"}, {"size": 1299, "mtime": 1751476662051, "results": "63", "hashOfConfig": "47"}, {"size": 3129, "mtime": 1751476653124, "results": "64", "hashOfConfig": "47"}, {"size": 4106, "mtime": 1751476408126, "results": "65", "hashOfConfig": "47"}, {"size": 786, "mtime": 1751471772565, "results": "66", "hashOfConfig": "47"}, {"size": 710, "mtime": 1751476505578, "results": "67", "hashOfConfig": "47"}, {"size": 2013, "mtime": 1751477426089, "results": "68", "hashOfConfig": "47"}, {"size": 813, "mtime": 1751476638890, "results": "69", "hashOfConfig": "47"}, {"size": 5394, "mtime": 1751471772583, "results": "70", "hashOfConfig": "47"}, {"size": 756, "mtime": 1751476518994, "results": "71", "hashOfConfig": "47"}, {"size": 261, "mtime": 1751476518995, "results": "72", "hashOfConfig": "47"}, {"size": 477, "mtime": 1751477435000, "results": "73", "hashOfConfig": "47"}, {"size": 2639, "mtime": 1751471772593, "results": "74", "hashOfConfig": "47"}, {"size": 738, "mtime": 1751471772598, "results": "75", "hashOfConfig": "47"}, {"size": 4845, "mtime": 1751476518964, "results": "76", "hashOfConfig": "47"}, {"size": 664, "mtime": 1751477537300, "results": "77", "hashOfConfig": "47"}, {"size": 5027, "mtime": 1751477656753, "results": "78", "hashOfConfig": "47"}, {"size": 5574, "mtime": 1751477955286, "results": "79", "hashOfConfig": "47"}, {"size": 837, "mtime": 1751476733641, "results": "80", "hashOfConfig": "47"}, {"size": 3948, "mtime": 1751476518981, "results": "81", "hashOfConfig": "47"}, {"size": 13947, "mtime": 1751478201785, "results": "82", "hashOfConfig": "47"}, {"size": 1847, "mtime": 1751472856489, "results": "83", "hashOfConfig": "47"}, {"size": 1555, "mtime": 1751478190734, "results": "84", "hashOfConfig": "47"}, {"size": 404, "mtime": 1751470153544, "results": "85", "hashOfConfig": "47"}, {"size": 4321, "mtime": 1751476721499, "results": "86", "hashOfConfig": "47"}, {"size": 1133, "mtime": 1751477280207, "results": "87", "hashOfConfig": "47"}, {"size": 1760, "mtime": 1751471772656, "results": "88", "hashOfConfig": "47"}, {"size": 2789, "mtime": 1751471772690, "results": "89", "hashOfConfig": "47"}, {"size": 7886, "mtime": 1751478204614, "results": "90", "hashOfConfig": "47"}, {"size": 2231, "mtime": 1751476541826, "results": "91", "hashOfConfig": "47"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14cf0np", {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/about/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/calendar/page.tsx", ["227"], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/changelogs/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/layout.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/login/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/Header.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/AppLayout.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/alert.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/button.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/card.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/empty-state.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/error-boundary.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/form.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/input.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/label.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/lazy-image.tsx", ["228"], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/loading-spinner.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/select.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skip-to-content.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/table.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/textarea.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toast.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toaster.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-calendar-data.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-notifications.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-toast.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/calendar.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/storage.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/user.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/worker.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/utils.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/middleware.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/AboutPage.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/ChangelogsPage.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/types/index.ts", [], [], {"ruleId": "229", "severity": 1, "message": "230", "line": 89, "column": 5, "nodeType": "231", "endLine": 89, "endColumn": 7, "suggestions": "232"}, {"ruleId": "233", "severity": 1, "message": "234", "line": 58, "column": 7, "nodeType": "235", "endLine": 70, "endColumn": 9}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'updateCurrentWeek'. Either include it or remove the dependency array.", "ArrayExpression", ["236"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", {"desc": "237", "fix": "238"}, "Update the dependencies array to be: [updateCurrentWeek]", {"range": "239", "text": "240"}, [2334, 2336], "[updateCurrentWeek]"]