[{"/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/about/page.tsx": "1", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/calendar/page.tsx": "2", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/changelogs/page.tsx": "3", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/layout.tsx": "4", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx": "5", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/login/page.tsx": "6", "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx": "7", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/Header.tsx": "8", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/AppLayout.tsx": "9", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Footer.tsx": "10", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Header.tsx": "11", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/alert.tsx": "12", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/badge.tsx": "13", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/button.tsx": "14", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/card.tsx": "15", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/dialog.tsx": "16", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/empty-state.tsx": "17", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/error-boundary.tsx": "18", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/form.tsx": "19", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/input.tsx": "20", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/label.tsx": "21", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/lazy-image.tsx": "22", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/loading-spinner.tsx": "23", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/select.tsx": "24", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/separator.tsx": "25", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skeleton.tsx": "26", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skip-to-content.tsx": "27", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/table.tsx": "28", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/textarea.tsx": "29", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toast.tsx": "30", "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toaster.tsx": "31", "/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx": "32", "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-calendar-data.ts": "33", "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-notifications.ts": "34", "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-toast.ts": "35", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/calendar.ts": "36", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/storage.ts": "37", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/user.ts": "38", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/worker.ts": "39", "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/utils.ts": "40", "/Users/<USER>/Github/kma-schedule-ngosangns/src/middleware.ts": "41", "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/AboutPage.tsx": "42", "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/CalendarPage.tsx": "43", "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/ChangelogsPage.tsx": "44", "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/LoginPage.tsx": "45", "/Users/<USER>/Github/kma-schedule-ngosangns/src/types/index.ts": "46"}, {"size": 5307, "mtime": 1751477183575, "results": "47", "hashOfConfig": "48"}, {"size": 14078, "mtime": 1751477601638, "results": "49", "hashOfConfig": "48"}, {"size": 5744, "mtime": 1751477261875, "results": "50", "hashOfConfig": "48"}, {"size": 448, "mtime": 1751477463407, "results": "51", "hashOfConfig": "48"}, {"size": 532, "mtime": 1751476607013, "results": "52", "hashOfConfig": "48"}, {"size": 7284, "mtime": 1751477388528, "results": "53", "hashOfConfig": "48"}, {"size": 579, "mtime": 1751477105151, "results": "54", "hashOfConfig": "48"}, {"size": 998, "mtime": 1751472703101, "results": "55", "hashOfConfig": "48"}, {"size": 1442, "mtime": 1751477455172, "results": "56", "hashOfConfig": "48"}, {"size": 418, "mtime": 1751476598421, "results": "57", "hashOfConfig": "48"}, {"size": 4165, "mtime": 1751476591486, "results": "58", "hashOfConfig": "48"}, {"size": 1515, "mtime": 1751471772544, "results": "59", "hashOfConfig": "48"}, {"size": 1128, "mtime": 1751476518992, "results": "60", "hashOfConfig": "48"}, {"size": 1697, "mtime": 1751471772550, "results": "61", "hashOfConfig": "48"}, {"size": 1795, "mtime": 1751471772559, "results": "62", "hashOfConfig": "48"}, {"size": 3848, "mtime": 1751476518989, "results": "63", "hashOfConfig": "48"}, {"size": 1299, "mtime": 1751476662051, "results": "64", "hashOfConfig": "48"}, {"size": 3129, "mtime": 1751476653124, "results": "65", "hashOfConfig": "48"}, {"size": 4106, "mtime": 1751476408126, "results": "66", "hashOfConfig": "48"}, {"size": 786, "mtime": 1751471772565, "results": "67", "hashOfConfig": "48"}, {"size": 710, "mtime": 1751476505578, "results": "68", "hashOfConfig": "48"}, {"size": 2013, "mtime": 1751477426089, "results": "69", "hashOfConfig": "48"}, {"size": 813, "mtime": 1751476638890, "results": "70", "hashOfConfig": "48"}, {"size": 5394, "mtime": 1751471772583, "results": "71", "hashOfConfig": "48"}, {"size": 756, "mtime": 1751476518994, "results": "72", "hashOfConfig": "48"}, {"size": 261, "mtime": 1751476518995, "results": "73", "hashOfConfig": "48"}, {"size": 477, "mtime": 1751477435000, "results": "74", "hashOfConfig": "48"}, {"size": 2639, "mtime": 1751471772593, "results": "75", "hashOfConfig": "48"}, {"size": 738, "mtime": 1751471772598, "results": "76", "hashOfConfig": "48"}, {"size": 4845, "mtime": 1751476518964, "results": "77", "hashOfConfig": "48"}, {"size": 664, "mtime": 1751477537300, "results": "78", "hashOfConfig": "48"}, {"size": 5027, "mtime": 1751477656753, "results": "79", "hashOfConfig": "48"}, {"size": 5449, "mtime": 1751477347338, "results": "80", "hashOfConfig": "48"}, {"size": 837, "mtime": 1751476733641, "results": "81", "hashOfConfig": "48"}, {"size": 3948, "mtime": 1751476518981, "results": "82", "hashOfConfig": "48"}, {"size": 14161, "mtime": 1751474806556, "results": "83", "hashOfConfig": "48"}, {"size": 1847, "mtime": 1751472856489, "results": "84", "hashOfConfig": "48"}, {"size": 1783, "mtime": 1751472324710, "results": "85", "hashOfConfig": "48"}, {"size": 404, "mtime": 1751470153544, "results": "86", "hashOfConfig": "48"}, {"size": 4321, "mtime": 1751476721499, "results": "87", "hashOfConfig": "48"}, {"size": 1133, "mtime": 1751477280207, "results": "88", "hashOfConfig": "48"}, {"size": 1760, "mtime": 1751471772656, "results": "89", "hashOfConfig": "48"}, {"size": 12703, "mtime": 1751475446458, "results": "90", "hashOfConfig": "48"}, {"size": 2789, "mtime": 1751471772690, "results": "91", "hashOfConfig": "48"}, {"size": 7970, "mtime": 1751472745643, "results": "92", "hashOfConfig": "48"}, {"size": 2231, "mtime": 1751476541826, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14cf0np", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/about/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/calendar/page.tsx", ["232"], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/changelogs/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/(main)/layout.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/layout.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/login/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/app/page.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/Header.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/AppLayout.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/alert.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/button.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/card.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/empty-state.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/error-boundary.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/form.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/input.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/label.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/lazy-image.tsx", ["233"], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/loading-spinner.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/select.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/skip-to-content.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/table.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/textarea.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toast.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/components/ui/toaster.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/contexts/AppContext.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-calendar-data.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-notifications.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/hooks/use-toast.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/calendar.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/storage.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/user.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/ts/worker.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/lib/utils.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/middleware.ts", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/AboutPage.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/CalendarPage.tsx", ["234"], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/ChangelogsPage.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/pages/LoginPage.tsx", [], [], "/Users/<USER>/Github/kma-schedule-ngosangns/src/types/index.ts", [], [], {"ruleId": "235", "severity": 1, "message": "236", "line": 89, "column": 5, "nodeType": "237", "endLine": 89, "endColumn": 7, "suggestions": "238"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 58, "column": 7, "nodeType": "241", "endLine": 70, "endColumn": 9}, {"ruleId": "235", "severity": 1, "message": "236", "line": 139, "column": 5, "nodeType": "237", "endLine": 139, "endColumn": 29, "suggestions": "242"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'updateCurrentWeek'. Either include it or remove the dependency array.", "ArrayExpression", ["243"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["244"], {"desc": "245", "fix": "246"}, {"desc": "247", "fix": "248"}, "Update the dependencies array to be: [updateCurrentWeek]", {"range": "249", "text": "250"}, "Update the dependencies array to be: [calendar?.data_subject, updateCurrentWeek]", {"range": "251", "text": "252"}, [2334, 2336], "[updateCurrentWeek]", [3551, 3575], "[calendar?.data_subject, updateCurrentWeek]"]