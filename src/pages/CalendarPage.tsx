import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import moment from 'moment';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow
} from '@/components/ui/table';
import {
	Loader2,
	AlertCircle,
	Download,
	LogOut,
	ChevronLeft,
	ChevronRight,
	Info
} from 'lucide-react';
import { loadData, saveData } from '@/lib/ts/storage';
import {
	fetchCalendarWithPost,
	processCalendar,
	processMainForm,
	processSemesters,
	processStudent,
	filterTrashInHtml,
	exportToGoogleCalendar
} from '@/lib/ts/calendar';
import { logout } from '@/lib/ts/user';

export default function CalendarPage() {
	const router = useRouter();
	const [loading, setLoading] = useState(false);
	const [errorMessage, setErrorMessage] = useState('');
	const [currentWeekIndex, setCurrentWeekIndex] = useState(0);
	const [currentWeek, setCurrentWeek] = useState<any[]>([]);

	// Load initial data
	const [data, setData] = useState({
		calendar: null,
		student: null,
		semesters: null,
		mainForm: null,
		signInToken: null
	});
	const { calendar, student, semesters, mainForm, signInToken } = data;

	// Load data on client side only
	useEffect(() => {
		// Initial load
		setData(loadData());

		const reloadData = () => {
			setData(loadData());
		};

		// Listen for login success events
		window.addEventListener('loginSuccess', reloadData);

		return () => {
			window.removeEventListener('loginSuccess', reloadData);
		};
	}, []);

	const shiftTimetable = [
		{ start: '07:00', end: '07:45' },
		{ start: '07:50', end: '08:35' },
		{ start: '08:40', end: '09:25' },
		{ start: '09:35', end: '10:20' },
		{ start: '10:25', end: '11:10' },
		{ start: '11:15', end: '12:00' },
		{ start: '12:30', end: '13:15' },
		{ start: '13:20', end: '14:05' },
		{ start: '14:10', end: '14:55' },
		{ start: '15:05', end: '15:50' },
		{ start: '15:55', end: '16:40' },
		{ start: '16:45', end: '17:30' },
		{ start: '18:00', end: '18:45' },
		{ start: '18:45', end: '19:30' },
		{ start: '19:45', end: '20:30' },
		{ start: '20:30', end: '21:15' }
	];

	const dayOfWeekMap = ['Chủ Nhật', 'Hai', 'Ba', 'Tư', 'Năm', 'Sáu', 'Bảy'];

	// Initialize current week index
	const initializeCurrentWeekIndex = (dataSubject: any[]) => {
		if (!dataSubject?.length) return 0;

		for (const [index, week] of dataSubject.entries()) {
			if (
				moment(week[0].time).isSameOrBefore(moment()) &&
				moment(week[week.length - 1].time).isSameOrAfter(moment())
			) {
				return index;
			}
		}
		return 0;
	};

	// Set current week based on index
	const updateCurrentWeek = (newWeekIndex: number) => {
		const dataSubject = calendar?.data_subject || [];
		if (!dataSubject.length) {
			setCurrentWeek([]);
			setCurrentWeekIndex(0);
			return;
		}

		let validIndex = newWeekIndex;
		if (validIndex < 0) validIndex = 0;
		if (validIndex >= dataSubject.length) validIndex = dataSubject.length - 1;

		setCurrentWeekIndex(validIndex);
		setCurrentWeek(dataSubject[validIndex] || []);
	};

	useEffect(() => {
		const dataSubject = calendar?.data_subject || [];
		if (dataSubject.length > 0) {
			const initialIndex = initializeCurrentWeekIndex(dataSubject);
			updateCurrentWeek(initialIndex);
		} else {
			setCurrentWeek([]);
			setCurrentWeekIndex(0);
		}
	}, [calendar?.data_subject]);

	const handleSemesterChange = async (newSemester: string) => {
		if (!semesters || !mainForm || !signInToken) return;

		setErrorMessage('');
		setLoading(true);
		const oldValue = semesters.currentSemester;

		try {
			const updatedSemesters = { ...semesters, currentSemester: newSemester };
			setData((prev) => ({ ...prev, semesters: updatedSemesters }));
			saveData({ semesters: updatedSemesters });

			const hidSemester = semesters.semesters.find((v: any) => v.value === newSemester);
			if (!hidSemester) throw new Error('Semester not found');

			const updatedMainForm = {
				...mainForm,
				drpSemester: newSemester,
				hidSemester: `${hidSemester.from}_${hidSemester.to}_${hidSemester.th}`
			};

			const response = await fetchCalendarWithPost(updatedMainForm, signInToken);
			const filteredResponse = filterTrashInHtml(response);
			const newCalendar = await processCalendar(filteredResponse);
			const newStudent = processStudent(filteredResponse);
			const newMainForm = processMainForm(filteredResponse);
			const newSemesters = processSemesters(filteredResponse);

			const newData = {
				mainForm: newMainForm,
				semesters: newSemesters,
				calendar: newCalendar,
				student: newStudent
			};

			setData((prev) => ({ ...prev, ...newData }));
			saveData(newData);

			updateCurrentWeek(0);
		} catch (error) {
			console.error('Semester change error:', error);
			setErrorMessage('Có lỗi xảy ra khi lấy dữ liệu!');
			const revertedSemesters = { ...semesters, currentSemester: oldValue };
			setData((prev) => ({ ...prev, semesters: revertedSemesters }));
		} finally {
			setLoading(false);
		}
	};

	const checkSession = (shift: number) => {
		if (shift >= 1 && shift <= 6) return 'morning';
		if (shift >= 7 && shift <= 12) return 'afternoon';
		return 'evening';
	};

	const handleLogout = () => {
		logout();
		router.push('/login');
	};

	const handleExportCalendar = () => {
		if (student && calendar) {
			exportToGoogleCalendar(student, calendar);
		}
	};

	const canGoToPreviousWeek = currentWeek.length > 0 && currentWeekIndex > 0;
	const canGoToNextWeek =
		currentWeek.length > 0 && currentWeekIndex < (calendar?.data_subject?.length || 0) - 1;

	return (
		<div className="space-y-4">
			{/* Navigation Bar */}
			<Card className="bg-secondary text-secondary-foreground">
				<CardContent className="p-4">
					<div className="flex items-center justify-between">
						<div className="flex-1">
							<h2 className="text-md font-medium">{student || ''}</h2>
						</div>
						<div className="flex items-center gap-2">
							<Button
								onClick={handleExportCalendar}
								variant="outline"
								size="sm"
								disabled={!student || !calendar || !calendar.data_subject?.length}
							>
								<Download className="w-4 h-4 mr-2" />
								Xuất file Google Calendar
							</Button>
							<Button onClick={handleLogout} variant="outline" size="sm">
								<LogOut className="w-4 h-4 mr-2" />
								Đăng xuất
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Week Navigation */}
			<div className="grid grid-cols-[1fr_10fr_1fr] gap-4">
				<Button
					variant="outline"
					className="h-full"
					onClick={() => updateCurrentWeek(currentWeekIndex - 1)}
					disabled={!canGoToPreviousWeek}
				>
					{canGoToPreviousWeek && <ChevronLeft className="w-4 h-4 mr-1" />}
				</Button>

				<Card className="bg-secondary text-secondary-foreground">
					<CardContent className="p-2">
						{/* Semester Selection */}
						<div className="bg-background rounded-lg p-4 mb-4">
							<div className="flex items-center justify-between">
								<div className="flex-1">
									{signInToken && semesters && (
										<Select
											value={semesters.currentSemester}
											onValueChange={handleSemesterChange}
											disabled={loading}
										>
											<SelectTrigger className="max-w-sm">
												<SelectValue />
											</SelectTrigger>
											<SelectContent>
												{semesters.semesters.map((item: any) => (
													<SelectItem key={item.value} value={item.value}>
														{`${item.from} - ${item.to} - KỲ ${item.th}`}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									)}
								</div>
								<div className="flex-none">
									{currentWeek.length > 0 && (
										<div className="flex items-center gap-2 text-sm text-muted-foreground">
											{loading && <Loader2 className="w-4 h-4 animate-spin" />}
											Tuần {currentWeekIndex + 1}:{moment(currentWeek[0].time).format('DD/MM/YYYY')}{' '}
											-{moment(currentWeek[currentWeek.length - 1].time).format('DD/MM/YYYY')}
										</div>
									)}
								</div>
							</div>
						</div>

						{/* Error Message */}
						{errorMessage && (
							<Alert variant="destructive" className="mb-4">
								<AlertCircle className="h-4 w-4" />
								<AlertDescription>{errorMessage}</AlertDescription>
							</Alert>
						)}

						{/* Empty State */}
						{!currentWeek.length && (
							<Alert className="mb-4">
								<Info className="h-4 w-4" />
								<AlertDescription>Lịch trống</AlertDescription>
							</Alert>
						)}

						{/* Schedule Table */}
						{currentWeek.length > 0 && (
							<div className="overflow-x-auto">
								<Table className="w-full table-fixed">
									<TableHeader>
										<TableRow>
											<TableHead colSpan={3} className="text-center relative">
												<div className="grid grid-cols-2 grid-rows-2 h-12">
													<span></span>
													<span className="flex items-center justify-center">Tiết</span>
													<span className="flex items-center justify-center">Thứ</span>
													<span></span>
												</div>
												<div className="absolute inset-0 flex items-center justify-center">
													<div className="border-b border-border w-8 transform rotate-45 translate-y-1"></div>
												</div>
											</TableHead>
											{Array.from({ length: 16 }, (_, i) => (
												<TableHead
													key={i + 1}
													className={`text-center text-xs ${
														checkSession(i + 1) === 'afternoon'
															? 'bg-secondary'
															: checkSession(i + 1) === 'evening'
																? 'bg-muted'
																: ''
													}`}
												>
													{i + 1}
												</TableHead>
											))}
										</TableRow>
									</TableHeader>
									<TableBody>
										{currentWeek.map((day: any, dayIndex: number) => (
											<TableRow key={dayIndex}>
												<TableCell colSpan={3} className="text-xs text-center">
													{day.time ? dayOfWeekMap[moment(day.time).day()] : ''}
													<br />
													{day.time ? moment(day.time).format('DD/MM/YYYY') : ''}
												</TableCell>
												{day.shift?.map((shift: any, shiftIndex: number) => {
													const shiftLength = parseInt(shift.length) || 0;
													if (shiftLength > 0) {
														return (
															<TableCell
																key={shiftIndex}
																colSpan={shiftLength}
																className={`px-2 ${
																	checkSession(shiftIndex + 1) === 'afternoon'
																		? 'bg-secondary'
																		: checkSession(shiftIndex + 1) === 'evening'
																			? 'bg-muted'
																			: ''
																}`}
															>
																{shift.content && (
																	<div className="relative group">
																		<div className="bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100 p-2 rounded text-xs cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
																			<div className="truncate">
																				{shiftTimetable[shiftIndex].start} {shift.name}
																			</div>
																		</div>

																		{/* Tooltip */}
																		<div className="absolute z-10 invisible group-hover:visible bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-100 p-2 rounded shadow-lg text-xs min-w-48 top-full left-0 mt-1">
																			<div className="space-y-1">
																				<div>Môn: {shift.name}</div>
																				<div>
																					Thời gian: {shiftTimetable[shiftIndex]?.start} -{' '}
																					{shiftTimetable[shiftIndex + shiftLength - 1]?.end ||
																						shiftTimetable[shiftIndex]?.end}
																				</div>
																				{shift.address && <div>Tại: {shift.address}</div>}
																			</div>
																		</div>
																	</div>
																)}
															</TableCell>
														);
													}
													return null;
												})}
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>
						)}
					</CardContent>
				</Card>

				<Button
					variant="outline"
					className="h-full"
					onClick={() => updateCurrentWeek(currentWeekIndex + 1)}
					disabled={!canGoToNextWeek}
				>
					{canGoToNextWeek && <ChevronRight className="w-4 h-4 ml-1" />}
				</Button>
			</div>
		</div>
	);
}
