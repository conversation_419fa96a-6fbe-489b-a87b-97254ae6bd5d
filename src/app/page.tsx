'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { loadData } from '@/lib/ts/storage';

export default function HomePage() {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		// Only run on client side
		const checkAuth = () => {
			const { calendar, signInToken, student } = loadData();

			// Check if user has valid session data
			const hasValidData =
				(calendar && calendar.data_subject && calendar.data_subject.length > 0) ||
				(signInToken && signInToken.length > 0) ||
				(student && student.length > 0);

			if (hasValidData) {
				router.push('/calendar');
			} else {
				router.push('/login');
			}

			setIsLoading(false);
		};

		checkAuth();
	}, [router]);

	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-center">
					<div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
					<p className="mt-4 text-muted-foreground">Loading...</p>
				</div>
			</div>
		);
	}

	return null;
}
