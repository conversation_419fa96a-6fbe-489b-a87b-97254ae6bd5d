'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from '@/components/ui/form';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { AlertCircle, ExternalLink, User, Lock, FileText } from 'lucide-react';
import { useAuth, useCalendar } from '@/contexts/AppContext';
import { useNotifications } from '@/hooks/use-notifications';
import { saveData } from '@/lib/ts/storage';
import {
	fetchCalendarWithGet,
	processCalendar,
	processMainForm,
	processSemesters,
	processStudent,
	filterTrashInHtml
} from '@/lib/ts/calendar';
import { login, logout } from '@/lib/ts/user';

// Form validation schemas
const loginSchema = z.object({
	username: z.string().min(1, 'Vui lòng nhập tên đăng nhập'),
	password: z.string().min(1, 'Vui lòng nhập mật khẩu')
});

const responseSchema = z.object({
	userResponse: z.string().min(1, 'Vui lòng nhập nội dung phản hồi từ website')
});

type LoginFormData = z.infer<typeof loginSchema>;
type ResponseFormData = z.infer<typeof responseSchema>;

export default function LoginPage() {
	const router = useRouter();
	const { login: authLogin, setLoading, setError } = useAuth();
	const { setCalendar, setStudent } = useCalendar();
	const { showSuccess, showError } = useNotifications();
	const [showManualInput, setShowManualInput] = useState(false);

	// Login form
	const loginForm = useForm<LoginFormData>({
		resolver: zodResolver(loginSchema),
		defaultValues: {
			username: '',
			password: ''
		}
	});

	// Manual response form
	const responseForm = useForm<ResponseFormData>({
		resolver: zodResolver(responseSchema),
		defaultValues: {
			userResponse: ''
		}
	});

	const handleLogin = async (data: LoginFormData) => {
		setLoading();
		setError(null);

		try {
			const signInToken = await login(data.username, data.password);
			const response = filterTrashInHtml(await fetchCalendarWithGet(signInToken));
			const calendar = await processCalendar(response);
			const student = processStudent(response);
			const mainForm = processMainForm(response);
			const semesters = processSemesters(response);

			const userData = {
				id: data.username,
				name: student || data.username
			};

			saveData({
				signInToken,
				mainForm,
				semesters,
				calendar,
				student
			});

			authLogin(userData, signInToken);
			setCalendar(calendar);
			setStudent(student);

			showSuccess('Đăng nhập thành công!');
			router.push('/calendar');
		} catch (error) {
			console.error('Login error:', error);
			const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra khi đăng nhập!';
			setError(errorMessage);
			showError('Đăng nhập thất bại', errorMessage);
			logout();
		}
	};

	const handleManualResponse = async (data: ResponseFormData) => {
		setLoading();
		setError(null);

		try {
			const response = filterTrashInHtml(data.userResponse);
			const calendar = await processCalendar(response);
			const student = processStudent(response);
			const mainForm = processMainForm(response);
			const semesters = processSemesters(response);

			const userData = {
				id: 'manual-user',
				name: student || 'Manual User'
			};

			saveData({
				mainForm,
				semesters,
				calendar,
				student
			});

			authLogin(userData, '');
			setCalendar(calendar);
			setStudent(student);

			showSuccess('Dữ liệu đã được xử lý thành công!');
			router.push('/calendar');
		} catch (error) {
			console.error('Manual response processing error:', error);
			const errorMessage = 'Có lỗi xảy ra khi xử lý dữ liệu!';
			setError(errorMessage);
			showError('Xử lý dữ liệu thất bại', errorMessage);
			logout();
		}
	};

	return (
		<div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-background to-muted/20">
			<div className="w-full max-w-md space-y-6">
				{/* Header */}
				<div className="text-center space-y-2">
					<h1 className="text-3xl font-bold">ACTVN Schedule</h1>
					<p className="text-muted-foreground">Đăng nhập để xem thời khóa biểu của bạn</p>
				</div>

				{/* Login Form */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<User className="h-5 w-5" />
							Đăng nhập
						</CardTitle>
						<CardDescription>Sử dụng tài khoản ACTVN của bạn để đăng nhập</CardDescription>
					</CardHeader>
					<CardContent>
						<Form {...loginForm}>
							<form onSubmit={loginForm.handleSubmit(handleLogin)} className="space-y-4">
								<FormField
									control={loginForm.control}
									name="username"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Tên đăng nhập</FormLabel>
											<FormControl>
												<Input
													{...field}
													placeholder="Nhập tên đăng nhập"
													disabled={loginForm.formState.isSubmitting}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={loginForm.control}
									name="password"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Mật khẩu</FormLabel>
											<FormControl>
												<Input
													{...field}
													type="password"
													placeholder="Nhập mật khẩu"
													disabled={loginForm.formState.isSubmitting}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<Button
									type="submit"
									className="w-full"
									disabled={loginForm.formState.isSubmitting}
								>
									{loginForm.formState.isSubmitting ? (
										<LoadingSpinner size="sm" text="Đang đăng nhập..." />
									) : (
										<>
											<Lock className="mr-2 h-4 w-4" />
											Đăng nhập
										</>
									)}
								</Button>
							</form>
						</Form>
					</CardContent>
				</Card>

				{/* Alternative Method */}
				<div className="space-y-4">
					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<Separator className="w-full" />
						</div>
						<div className="relative flex justify-center text-xs uppercase">
							<span className="bg-background px-2 text-muted-foreground">Hoặc</span>
						</div>
					</div>

					<Button
						variant="outline"
						className="w-full"
						onClick={() => setShowManualInput(!showManualInput)}
					>
						<FileText className="mr-2 h-4 w-4" />
						Nhập dữ liệu thủ công
					</Button>
				</div>

				{/* Manual Input Form */}
				{showManualInput && (
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<FileText className="h-5 w-5" />
								Nhập dữ liệu thủ công
							</CardTitle>
							<CardDescription>Dán nội dung HTML từ trang thời khóa biểu ACTVN</CardDescription>
						</CardHeader>
						<CardContent>
							<Form {...responseForm}>
								<form
									onSubmit={responseForm.handleSubmit(handleManualResponse)}
									className="space-y-4"
								>
									<FormField
										control={responseForm.control}
										name="userResponse"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Nội dung HTML</FormLabel>
												<FormControl>
													<Textarea
														{...field}
														placeholder="Dán nội dung HTML từ trang thời khóa biểu..."
														className="min-h-[120px] font-mono text-sm"
														disabled={responseForm.formState.isSubmitting}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<Button
										type="submit"
										className="w-full"
										disabled={responseForm.formState.isSubmitting}
									>
										{responseForm.formState.isSubmitting ? (
											<LoadingSpinner size="sm" text="Đang xử lý..." />
										) : (
											'Xử lý dữ liệu'
										)}
									</Button>
								</form>
							</Form>

							<Alert className="mt-4">
								<AlertCircle className="h-4 w-4" />
								<AlertDescription>
									<strong>Hướng dẫn:</strong> Truy cập{' '}
									<a
										href="https://actvn.edu.vn"
										target="_blank"
										rel="noopener noreferrer"
										className="underline inline-flex items-center gap-1"
									>
										trang ACTVN
										<ExternalLink className="h-3 w-3" />
									</a>
									, đăng nhập và sao chép toàn bộ nội dung trang thời khóa biểu rồi dán vào đây.
								</AlertDescription>
							</Alert>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
