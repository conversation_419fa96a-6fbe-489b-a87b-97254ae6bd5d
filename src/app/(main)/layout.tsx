import Header from '@/components/Header'

export default function MainLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="app flex flex-col min-h-screen">
      <Header />
      
      <main className="flex-1 flex flex-col p-4 w-full max-w-6xl min-w-[60rem] mx-auto box-border">
        {children}
      </main>

      <footer className="flex flex-col justify-center items-center p-3 w-full min-w-[60rem] text-center">
        <span>KMA Schedule v2022.12 - ngosangns</span>
      </footer>
    </div>
  )
}
